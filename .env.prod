# ==============================================
# OMO System Production Environment Variables
# ==============================================

# Domain Configuration
DOMAIN=yourdomain.com
ACME_EMAIL=<EMAIL>

# Database Configuration
DB_ROOT_PASSWORD=your_secure_root_password_here
DB_NAME=omo_production
DB_USER=omo_user
DB_PASSWORD=your_secure_db_password_here
DB_HOST=mysql
DB_PORT=3306

# Shopify App Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SHOPIFY_SCOPES=write_products,write_orders,read_customers,write_customers,write_discounts,write_draft_orders,read_returns,write_returns
SHOPIFY_API_VERSION=2025-01

# System Configuration
SYSTEM_TOKEN=your_secure_system_token_here

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=your_secure_minio_password_here

# Grafana Configuration
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_secure_grafana_password_here

# Traefik Authentication (htpasswd format)
# Generate with: echo $(htpasswd -nb admin your_password) | sed -e s/\\$/\\$\\$/g
TRAEFIK_AUTH=admin:$$2y$$10$$your_hashed_password_here

# Redis Configuration
REDIS_PASSWORD=
REDIS_HOST=redis
REDIS_PORT=6379

# Logging Configuration
LOG_LEVEL=info

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# SSL Configuration
SSL_EMAIL=${ACME_EMAIL}

# Security Configuration
SECURE_COOKIES=true
FORCE_HTTPS=true

# Performance Configuration
MAX_MEMORY_MYSQL=1G
MAX_MEMORY_REDIS=512M

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Logging Configuration (Loki)
LOKI_URL=http://loki:3100

# Production Environment
NODE_ENV=production
