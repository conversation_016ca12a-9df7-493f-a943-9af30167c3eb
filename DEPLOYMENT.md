# OMO System Production Deployment Guide

This guide provides step-by-step instructions for deploying the OMO Shopify system to production using Docker Compose with Traefik reverse proxy.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Server Setup](#server-setup)
3. [Configuration](#configuration)
4. [Deployment](#deployment)
5. [SSL/TLS Setup](#ssltls-setup)
6. [Monitoring](#monitoring)
7. [Backup & Recovery](#backup--recovery)
8. [Troubleshooting](#troubleshooting)
9. [Maintenance](#maintenance)

## Prerequisites

### System Requirements

- **OS**: Ubuntu 20.04+ or CentOS 8+ (recommended)
- **RAM**: Minimum 8GB, Recommended 16GB+
- **Storage**: Minimum 100GB SSD
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **Network**: Static IP address with domain name

### Software Requirements

- Docker Engine 24.0+
- Docker Compose 2.20+
- Git
- htpasswd (for Traefik authentication)

## Server Setup

### 1. Install Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. Install Docker Compose

```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. Create Traefik Network

```bash
docker network create traefik
```

### 4. Clone Repository

```bash
git clone <your-repository-url>
cd omo-system
```

## Configuration

### 1. Environment Configuration

Copy and configure the production environment file:

```bash
cp .env.prod .env
```

Edit `.env` with your production values:

```bash
# Domain Configuration
DOMAIN=yourdomain.com
ACME_EMAIL=<EMAIL>

# Database Configuration
DB_ROOT_PASSWORD=your_secure_root_password_here
DB_NAME=omo_production
DB_USER=omo_user
DB_PASSWORD=your_secure_db_password_here

# Shopify App Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret

# Other configurations...
```

### 2. Generate Traefik Authentication

Generate htpasswd for Traefik dashboard access:

```bash
# Install htpasswd
sudo apt-get install apache2-utils

# Generate password hash
echo $(htpasswd -nb admin your_password) | sed -e s/\\$/\\$\\$/g

# Add the output to TRAEFIK_AUTH in .env file
```

### 3. DNS Configuration

Configure your DNS records:

```
A     yourdomain.com           -> YOUR_SERVER_IP
A     app.yourdomain.com       -> YOUR_SERVER_IP
A     api.yourdomain.com       -> YOUR_SERVER_IP
A     monitoring.yourdomain.com -> YOUR_SERVER_IP
A     storage.yourdomain.com   -> YOUR_SERVER_IP
A     minio.yourdomain.com     -> YOUR_SERVER_IP
A     traefik.yourdomain.com   -> YOUR_SERVER_IP
```

## Deployment

### 1. Build and Start Services

```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

### 2. Verify Deployment

Check that all services are running:

```bash
# Check container health
docker ps

# Test database connection
docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "SHOW DATABASES;"

# Test Redis connection
docker exec omo_redis_prod redis-cli ping
```

### 3. Access Services

Once deployed, access your services:

- **Shopify App**: https://app.yourdomain.com
- **API Backend**: https://api.yourdomain.com
- **Monitoring (Grafana)**: https://monitoring.yourdomain.com
- **MinIO Console**: https://minio.yourdomain.com
- **Traefik Dashboard**: https://traefik.yourdomain.com

## SSL/TLS Setup

SSL certificates are automatically managed by Traefik using Let's Encrypt:

1. **Automatic Certificate Generation**: Traefik automatically requests certificates for all configured domains
2. **Certificate Renewal**: Certificates are automatically renewed before expiration
3. **HTTP to HTTPS Redirect**: All HTTP traffic is automatically redirected to HTTPS

### Manual Certificate Management

If needed, you can manually manage certificates:

```bash
# View certificate status
docker exec traefik cat /letsencrypt/acme.json

# Force certificate renewal (if needed)
docker restart traefik
```

## Monitoring

### Grafana Setup

1. Access Grafana at https://monitoring.yourdomain.com
2. Login with credentials from `.env` file
3. Import dashboards for:
   - Docker containers
   - MySQL performance
   - Redis metrics
   - Application logs

### Log Management

Logs are centralized using Loki:

- **Application Logs**: Stored in Loki and viewable in Grafana
- **Container Logs**: JSON format with rotation
- **Access Logs**: Traefik access logs

### Health Checks

All services include health checks:

```bash
# Check service health
docker-compose -f docker-compose.prod.yml ps

# View health check logs
docker inspect omo_mysql_prod | grep -A 10 Health
```

## Backup & Recovery

### Automated Backups

Set up automated backups using cron:

```bash
# Make backup script executable
chmod +x scripts/backup.sh

# Add to crontab (daily at 2 AM)
crontab -e
0 2 * * * /path/to/omo-system/scripts/backup.sh
```

### Manual Backup

```bash
# Run manual backup
./scripts/backup.sh
```

### Restore from Backup

```bash
# Make restore script executable
chmod +x scripts/restore.sh

# Restore everything from specific backup
./scripts/restore.sh 20240115_143000

# Restore only database
./scripts/restore.sh --database 20240115_143000

# View restore options
./scripts/restore.sh --help
```

## Troubleshooting

### Common Issues

#### 1. Services Not Starting

```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs service_name

# Check resource usage
docker stats

# Restart specific service
docker-compose -f docker-compose.prod.yml restart service_name
```

#### 2. SSL Certificate Issues

```bash
# Check Traefik logs
docker logs traefik

# Verify DNS resolution
nslookup yourdomain.com

# Check certificate status
curl -I https://yourdomain.com
```

#### 3. Database Connection Issues

```bash
# Check MySQL logs
docker logs omo_mysql_prod

# Test connection
docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "SELECT 1;"

# Check network connectivity
docker exec omo_shopify_app_prod nc -z mysql 3306
```

#### 4. Performance Issues

```bash
# Monitor resource usage
docker stats

# Check disk space
df -h

# Monitor database performance
docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "SHOW PROCESSLIST;"
```

### Log Locations

- **Application Logs**: `./omo-shopify-app/logs/`
- **Container Logs**: `docker logs container_name`
- **System Logs**: `/var/log/`

## Maintenance

### Regular Maintenance Tasks

#### 1. Update Images

```bash
# Pull latest images
docker-compose -f docker-compose.prod.yml pull

# Restart with new images
docker-compose -f docker-compose.prod.yml up -d
```

#### 2. Database Maintenance

```bash
# Optimize database
docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "OPTIMIZE TABLE database_name.table_name;"

# Check database size
docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
GROUP BY table_schema;"
```

#### 3. Log Rotation

```bash
# Clean old logs
docker system prune -f

# Rotate application logs
find ./omo-shopify-app/logs -name "*.log" -mtime +30 -delete
```

#### 4. Security Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker
sudo apt update && sudo apt install docker-ce docker-ce-cli containerd.io

# Restart services if needed
docker-compose -f docker-compose.prod.yml restart
```

### Scaling

#### Horizontal Scaling

To scale application services:

```bash
# Scale Shopify app
docker-compose -f docker-compose.prod.yml up -d --scale omo-shopify-app=3

# Scale NestJS backend
docker-compose -f docker-compose.prod.yml up -d --scale omo-nest-be=2
```

#### Vertical Scaling

Update resource limits in `docker-compose.prod.yml`:

```yaml
services:
  omo-shopify-app:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

## Security Considerations

1. **Firewall Configuration**: Only expose ports 80 and 443
2. **Regular Updates**: Keep all components updated
3. **Strong Passwords**: Use strong, unique passwords for all services
4. **Network Isolation**: Services communicate through internal networks
5. **SSL/TLS**: All traffic encrypted with valid certificates
6. **Backup Encryption**: Consider encrypting backup files
7. **Access Control**: Limit access to production servers

## Support

For issues and support:

1. Check logs first: `docker-compose -f docker-compose.prod.yml logs`
2. Review this documentation
3. Check GitHub issues
4. Contact system administrators

---

**Last Updated**: January 2025
**Version**: 1.0
