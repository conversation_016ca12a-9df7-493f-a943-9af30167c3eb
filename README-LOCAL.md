# OMO System - Local Testing Setup

This guide helps you set up the OMO system locally for testing the complete production architecture, including reverse proxy configuration, without requiring domain setup or SSL certificates.

## Overview

The local setup includes:
- **<PERSON>raefik reverse proxy** (HTTP-only, no SSL)
- **Complete service stack** (MySQL, Redis, MinIO, Loki, Grafana)
- **Both applications** (Shopify App + NestJS Backend)
- **Monitoring and queues** (Grafana + Queue management via NestJS)

## Prerequisites

- Docker and Docker Compose installed
- At least 4GB RAM available for containers
- Ports 80, 3000-3002, 6379, 8080, 9000-9001 available

## Quick Start

### 1. Configure Environment

Copy and customize the local environment file:

```bash
cp .env.local .env.local.custom
```

Edit `.env.local.custom` and update:
- `SHOPIFY_API_KEY` - Your Shopify development app API key
- `SHOPIFY_API_SECRET` - Your Shopify development app secret
- Database passwords if desired

### 2. Configure Hosts File (Recommended)

Add these entries to your hosts file for easier access:

**Windows:** `C:\Windows\System32\drivers\etc\hosts`
**macOS/Linux:** `/etc/hosts`

```
127.0.0.1 app.localhost
127.0.0.1 api.localhost
127.0.0.1 storage.localhost
127.0.0.1 minio.localhost
127.0.0.1 monitoring.localhost
127.0.0.1 traefik.localhost
```

### 3. Start the System

```bash
# Start all services
docker-compose -f docker-compose.local.yml --env-file .env.local.custom up -d

# View logs
docker-compose -f docker-compose.local.yml logs -f

# Stop all services
docker-compose -f docker-compose.local.yml down
```

## Service Access

### Via Reverse Proxy (Recommended)

| Service | URL | Description |
|---------|-----|-------------|
| **Shopify App** | http://app.localhost | Main Shopify application |
| **NestJS API** | http://api.localhost | Backend API endpoints |
| **MinIO Console** | http://minio.localhost | Object storage management |
| **MinIO API** | http://storage.localhost | S3-compatible API |
| **Grafana** | http://monitoring.localhost | Monitoring dashboards |
| **Traefik Dashboard** | http://traefik.localhost | Reverse proxy dashboard |

### Direct Port Access (Fallback)

| Service | URL | Description |
|---------|-----|-------------|
| **Shopify App** | http://localhost:3000 | Direct app access |
| **NestJS API** | http://localhost:3001 | Direct API access |
| **Grafana** | http://localhost:3002 | Direct monitoring access |
| **Traefik Dashboard** | http://localhost:8080 | Direct proxy dashboard |
| **MinIO Console** | http://localhost:9001 | Direct storage console |
| **MinIO API** | http://localhost:9000 | Direct storage API |
| **MySQL** | localhost:3306 | Database connection |
| **Redis** | localhost:6379 | Cache/queue connection |

## Default Credentials

### Grafana
- **Username:** admin
- **Password:** admin123

### MinIO
- **Username:** minioadmin
- **Password:** minio123456

### MySQL
- **Root Password:** local_root_password
- **Database:** omo_local
- **User:** omo_local_user
- **Password:** local_db_password

## Testing the Setup

### 1. Health Check

```bash
# Check all services are running
docker-compose -f docker-compose.local.yml ps

# Check service health
curl http://app.localhost/ping
curl http://api.localhost/health
```

### 2. Database Connection

```bash
# Connect to MySQL
docker exec -it omo_mysql_local mysql -u omo_local_user -plocal_db_password omo_local

# Connect to Redis
docker exec -it omo_redis_local redis-cli
```

### 3. MinIO Storage

1. Visit http://minio.localhost
2. Login with minioadmin/minio123456
3. Check that `loki` and `backups` buckets exist

### 4. Monitoring

1. Visit http://monitoring.localhost
2. Login with admin/admin123
3. Check Loki data source is connected
4. View application logs

## Development Workflow

### Building and Testing Changes

```bash
# Rebuild specific service after code changes
docker-compose -f docker-compose.local.yml build omo-shopify-app
docker-compose -f docker-compose.local.yml up -d omo-shopify-app

# View application logs
docker-compose -f docker-compose.local.yml logs -f omo-shopify-app
docker-compose -f docker-compose.local.yml logs -f omo-nest-be
```

### Database Operations

```bash
# Run Prisma migrations
docker exec -it omo_shopify_app_local npx prisma migrate dev

# Reset database
docker-compose -f docker-compose.local.yml down -v
docker-compose -f docker-compose.local.yml up -d
```

## Troubleshooting

### Common Issues

**Services not accessible via .localhost domains:**
- Ensure hosts file entries are correct
- Try direct port access as fallback
- Check Traefik dashboard at http://localhost:8080

**Database connection errors:**
- Wait for MySQL health check to pass
- Check logs: `docker-compose -f docker-compose.local.yml logs mysql`

**Build failures:**
- Ensure Docker has enough memory (4GB+)
- Clear Docker cache: `docker system prune -a`

**Port conflicts:**
- Check if ports are already in use
- Modify port mappings in docker-compose.local.yml

### Viewing Logs

```bash
# All services
docker-compose -f docker-compose.local.yml logs -f

# Specific service
docker-compose -f docker-compose.local.yml logs -f omo-shopify-app

# Traefik routing logs
docker-compose -f docker-compose.local.yml logs -f traefik
```

### Resetting Everything

```bash
# Stop and remove all containers, networks, and volumes
docker-compose -f docker-compose.local.yml down -v --remove-orphans

# Remove all images (optional)
docker-compose -f docker-compose.local.yml down --rmi all

# Start fresh
docker-compose -f docker-compose.local.yml up -d
```

## Architecture Testing

This local setup allows you to test:

✅ **Reverse proxy routing** - Same Traefik configuration as production
✅ **Service discovery** - Docker labels and automatic routing
✅ **Database migrations** - Prisma schema changes
✅ **Queue processing** - BullMQ job handling
✅ **Object storage** - MinIO S3-compatible operations
✅ **Log aggregation** - Loki log collection
✅ **Monitoring** - Grafana dashboards
✅ **CORS policies** - API cross-origin requests
✅ **Health checks** - Service dependency management

## Next Steps

Once local testing is successful:

1. **Update production environment** - Copy working configurations
2. **Deploy to staging** - Test with real domains
3. **Production deployment** - Use docker-compose.prod.yml

## Support

For issues with this local setup:

1. Check service logs for errors
2. Verify environment variables in .env.local.custom
3. Ensure all prerequisites are met
4. Try the troubleshooting steps above
