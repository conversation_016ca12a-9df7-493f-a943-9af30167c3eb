# OMO System Production Setup

This repository contains a complete production-ready Docker Compose setup for the OMO Shopify loyalty system with Traefik reverse proxy, SSL/TLS termination, monitoring, and backup solutions.

## 🚀 Quick Start

1. **Clone and Configure**
   ```bash
   git clone <repository-url>
   cd omo-system
   cp .env.prod .env
   # Edit .env with your production values
   ```

2. **Create Traefik Network**
   ```bash
   docker network create traefik
   ```

3. **Deploy**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

## 📋 What's Included

### ✅ Complete Production Setup
- **Traefik v3.0** - Reverse proxy with automatic SSL/TLS
- **MySQL 8.4** - Production database with optimized settings
- **Redis 7** - Cache and queue backend with persistence
- **MinIO** - S3-compatible object storage
- **Loki + Grafana** - Centralized logging and monitoring

### ✅ Application Services
- **OMO Shopify App** - Remix-based Shopify application
- **OMO NestJS Backend** - API backend with queue processing
- **Multi-stage Docker builds** - Optimized production images
- **Health checks** - Built-in container health monitoring

### ✅ Security & SSL
- **Automatic HTTPS** - Let's Encrypt certificates with auto-renewal
- **HTTP to HTTPS redirect** - Force secure connections
- **Network isolation** - Internal Docker networks
- **Non-root containers** - Security best practices
- **Basic authentication** - Protected admin interfaces

### ✅ Monitoring & Logging
- **Centralized logging** - All logs aggregated in Loki
- **Grafana dashboards** - Visual monitoring and alerting
- **Health check script** - System health monitoring
- **Log rotation** - Automatic log management
- **Metrics collection** - Prometheus-compatible metrics

### ✅ Backup & Recovery
- **Automated backups** - Database, files, and configurations
- **Flexible restore** - Granular restore options
- **Retention policies** - Automatic cleanup of old backups
- **MinIO integration** - Cloud-compatible backup storage

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐
│   Internet      │────│   Traefik        │
│                 │    │   (Port 80/443)  │
└─────────────────┘    └──────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
            ┌───────▼────┐ ┌────▼────┐ ┌───▼────────┐
            │ Shopify    │ │ NestJS  │ │ Monitoring │
            │ App        │ │ Backend │ │ Stack      │
            └────────────┘ └─────────┘ └────────────┘
                    │           │           │
            ┌───────┼───────────┼───────────┼────────┐
            │       │           │           │        │
        ┌───▼───┐ ┌─▼──┐ ┌─────▼─────┐ ┌──▼───┐ ┌──▼────┐
        │ MySQL │ │Redis│ │   MinIO   │ │ Loki │ │Grafana│
        └───────┘ └────┘ └───────────┘ └──────┘ └───────┘
```

## 🌐 Service URLs

After deployment, access your services at:

- **Shopify App**: https://app.yourdomain.com
- **API Backend**: https://api.yourdomain.com
- **Monitoring**: https://monitoring.yourdomain.com
- **MinIO Console**: https://minio.yourdomain.com
- **Traefik Dashboard**: https://traefik.yourdomain.com

## 📁 File Structure

```
omo-system/
├── docker-compose.prod.yml     # Production Docker Compose
├── .env.prod                   # Production environment template
├── DEPLOYMENT.md               # Detailed deployment guide
├── README-PRODUCTION.md        # This file
├── omo-shopify-app/
│   ├── Dockerfile              # Production Shopify app image
│   └── docker-entrypoint.sh    # App startup script
├── omo-nest-be/
│   ├── Dockerfile              # Production NestJS image
│   └── docker-entrypoint.sh    # Backend startup script
└── scripts/
    ├── backup.sh               # Automated backup script
    ├── restore.sh              # Flexible restore script
    └── health-check.sh         # System health monitoring
```

## 🔧 Configuration

### Environment Variables

Key configuration in `.env`:

```bash
# Domain & SSL
DOMAIN=yourdomain.com
ACME_EMAIL=<EMAIL>

# Database
DB_ROOT_PASSWORD=secure_password
DB_NAME=omo_production
DB_USER=omo_user
DB_PASSWORD=secure_password

# Shopify
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret

# Authentication
TRAEFIK_AUTH=admin:$2y$10$hashed_password
GRAFANA_PASSWORD=secure_password
```

### DNS Records Required

```
A     yourdomain.com           -> YOUR_SERVER_IP
A     *.yourdomain.com         -> YOUR_SERVER_IP
```

## 🛠️ Management Commands

### Deployment
```bash
# Deploy all services
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Check status
docker-compose -f docker-compose.prod.yml ps
```

### Backup & Restore
```bash
# Manual backup
./scripts/backup.sh

# Restore everything
./scripts/restore.sh 20240115_143000

# Restore database only
./scripts/restore.sh --database 20240115_143000
```

### Health Monitoring
```bash
# Run health check
./scripts/health-check.sh

# Set up automated health checks (cron)
*/5 * * * * /path/to/omo-system/scripts/health-check.sh
```

### Scaling
```bash
# Scale Shopify app
docker-compose -f docker-compose.prod.yml up -d --scale omo-shopify-app=3

# Scale NestJS backend
docker-compose -f docker-compose.prod.yml up -d --scale omo-nest-be=2
```

## 🔒 Security Features

- **SSL/TLS Encryption** - All traffic encrypted with Let's Encrypt certificates
- **Network Isolation** - Services communicate through internal Docker networks
- **Non-root Containers** - All application containers run as non-root users
- **Basic Authentication** - Admin interfaces protected with HTTP basic auth
- **Firewall Ready** - Only ports 80 and 443 need to be exposed
- **Secret Management** - Sensitive data managed through environment variables

## 📊 Monitoring & Alerting

### Built-in Monitoring
- **Container Health Checks** - Docker native health monitoring
- **Resource Monitoring** - CPU, memory, and disk usage tracking
- **Application Metrics** - Custom application metrics collection
- **Log Aggregation** - Centralized logging with Loki
- **Visual Dashboards** - Grafana dashboards for all services

### Health Check Script
The included health check script monitors:
- Service availability and health
- Database connectivity and size
- Redis connectivity and memory usage
- SSL certificate validity
- System resource usage
- URL accessibility

## 🔄 Backup Strategy

### Automated Backups
- **Database Backups** - Full MySQL dumps with compression
- **File Backups** - Application logs and user uploads
- **Configuration Backups** - Grafana dashboards and Traefik certificates
- **Retention Policy** - Configurable retention with automatic cleanup
- **Cloud Storage** - Optional upload to MinIO/S3 compatible storage

### Backup Schedule
- **Daily Backups** - Automated daily backups at 2 AM
- **Retention** - 30 days by default (configurable)
- **Compression** - All backups compressed to save space
- **Verification** - Backup integrity checks

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting**: Check logs with `docker-compose logs service_name`
2. **SSL certificate issues**: Verify DNS records and check Traefik logs
3. **Database connection issues**: Ensure MySQL is healthy and credentials are correct
4. **Performance issues**: Monitor resource usage with `docker stats`

### Log Locations
- **Application Logs**: `./omo-shopify-app/logs/`
- **Container Logs**: `docker logs container_name`
- **Centralized Logs**: Available in Grafana via Loki

## 📚 Documentation

- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Complete deployment guide
- **[Backup Scripts](scripts/)** - Backup and restore documentation
- **[Health Monitoring](scripts/health-check.sh)** - Health check documentation

## 🆘 Support

For issues and support:
1. Check the health status: `./scripts/health-check.sh`
2. Review logs: `docker-compose -f docker-compose.prod.yml logs`
3. Consult the [DEPLOYMENT.md](DEPLOYMENT.md) guide
4. Check container status: `docker-compose -f docker-compose.prod.yml ps`

## 📈 Performance Recommendations

### System Requirements
- **Production**: 16GB RAM, 8 CPU cores, 200GB SSD
- **High Traffic**: 32GB RAM, 16 CPU cores, 500GB SSD
- **Enterprise**: Load balancer + multiple instances

### Optimization Tips
- Monitor resource usage regularly
- Scale services based on load
- Optimize database queries
- Use Redis for caching
- Enable log rotation
- Regular security updates

---

**Production Ready** ✅ | **SSL Enabled** 🔒 | **Monitoring Included** 📊 | **Backup Automated** 💾

*Last Updated: January 2025*
