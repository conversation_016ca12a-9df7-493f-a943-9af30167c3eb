services:
  db:
    image: mysql:8.4
    container_name: shopify_omo_db
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-your_secure_password}
      MYSQL_DATABASE: ${DB_NAME:-shopify_omo}
      MYSQL_USER: ${DB_USER:-shopify_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-your_user_password}
    ports: ["3306:3306"]
    volumes: ["mysql_data_dev:/var/lib/mysql"]
    restart: always
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 5s
      retries: 10

  redis:
    image: redis:alpine
    container_name: shopify_omo_redis
    ports: ["6379:6379"]
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 5s
      timeout: 3s
      retries: 5
    volumes: ["redis_data:/data"]

  # --- <PERSON><PERSON> (S3-compatible object storage) ---
  minio:
    image: quay.io/minio/minio:latest
    container_name: minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadminpass}
    ports:
      - "9000:9000" # S3 API
      - "9001:9001" # Web console
    volumes: ["minio_data:/data"]
    restart: unless-stopped

  # One-shot job to create the 'loki' bucket
  minio-mc-init:
    image: minio/mc:latest
    depends_on: [minio]
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadminpass}
    entrypoint: >
      /bin/sh -c "
      until (mc alias set local http://minio:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD) do sleep 1; done &&
      mc mb -p local/loki || true
      "
    restart: "no"

  loki-perms:
    image: alpine:3.20
    container_name: loki-perms
    command: >
      sh -c "
      mkdir -p /loki/index /loki/index_cache &&
      chown -R 10001:10001 /loki/index /loki/index_cache
      "
    volumes:
      - loki_index:/loki/index
      - loki_index_cache:/loki/index_cache
    restart: "no"

  loki:
    image: grafana/loki:latest
    container_name: loki
    depends_on:
      - minio
      - minio-mc-init
      - loki-perms # ensure perms are fixed first
    command: ["-config.file=/etc/loki/loki-config.yml"]
    environment:
      LOKI_S3_ENDPOINT: http://minio:9000
      LOKI_S3_BUCKET: loki
      LOKI_S3_REGION: us-east-1
      AWS_ACCESS_KEY_ID: ${MINIO_ROOT_USER:-minioadmin}
      AWS_SECRET_ACCESS_KEY: ${MINIO_ROOT_PASSWORD:-minioadminpass}
    ports: ["3100:3100"]
    volumes:
      - loki_index:/loki/index
      - loki_index_cache:/loki/index_cache
      - ./loki-config.yml:/etc/loki/loki-config.yml:ro
    restart: unless-stopped

  # --- Grafana (pre-wired to Loki) ---
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    depends_on: [loki]
    ports: ["3030:3000"]
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./provisioning/datasources/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml:ro
    restart: unless-stopped

volumes:
  mysql_data_dev:
  minio_data:
  loki_index:
  loki_index_cache:
  grafana_data:
  redis_data:
