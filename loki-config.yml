auth_enabled: false

server:
  http_listen_port: ${LOKI_HTTP_PORT:-3100}

common:
  path_prefix: ${LOKI_PATH_PREFIX:-/loki}
  ring:
    instance_addr: ${LOKI_INSTANCE_ADDR:-127.0.0.1}
    kvstore:
      store: ${LOKI_KVSTORE:-inmemory}
  replication_factor: ${LOKI_REPLICATION_FACTOR:-1}

schema_config:
  configs:
    - from: "${LOKI_SCHEMA_FROM:-2024-01-01}"
      store: ${LOKI_INDEX_STORE:-tsdb} # TSDB is the recommended index
      object_store: ${LOKI_OBJECT_STORE:-s3}
      schema: ${LOKI_SCHEMA_VERSION:-v13} # v13 is current recommended schema
      index:
        prefix: ${LOKI_INDEX_PREFIX:-index_}
        period: ${LOKI_INDEX_PERIOD:-24h}

storage_config:
  tsdb_shipper:
    active_index_directory: ${LOKI_INDEX_DIRECTORY:-/loki/index}
    cache_location: ${LOKI_CACHE_DIRECTORY:-/loki/index_cache}
  aws:
    bucketnames: ${LOKI_S3_BUCKET}
    endpoint: ${LOKI_S3_ENDPOINT}
    region: ${LOKI_S3_REGION}
    access_key_id: ${AWS_ACCESS_KEY_ID}
    secret_access_key: ${AWS_SECRET_ACCESS_KEY}
    s3forcepathstyle: ${LOKI_S3_FORCE_PATH_STYLE:-true}
    insecure: ${LOKI_S3_INSECURE:-true} # switch to false when you enable TLS on MinIO
