# Repository Guidelines

## Project Structure & Module Organization
- `src/main.ts`: Nest bootstrap and global wiring.
- `src/app.module.ts`: Root module; imports feature modules.
- `src/modules/*`: Feature modules (e.g., `shop`, `loyalty`, `sales`, `shared`).
- `src/modules/*/entities`: Module-specific TypeORM entities; shared base in `src/entities`.
- `src/database`: Database module/config.
- `src/queue`: BullMQ queues and constants.
- `test/`: E2E tests and Jest config.
- `dist/`: Build output (ignored from source control).

## Build, Test, and Development Commands
- `npm run start`: Start in dev mode.
- `npm run start:dev`: Start with watch reload.
- `npm run start:prod`: Run compiled app from `dist`.
- `npm run build`: Compile TypeScript to `dist`.
- `npm run lint`: Lint and auto-fix with ESLint.
- `npm run format`: Format with Prettier.
- `npm test` | `npm run test:watch`: Unit tests (Jest, .spec.ts).
- `npm run test:e2e`: Run E2E tests in `test/`.
- `npm run test:cov`: Generate coverage in `coverage/`.

## Coding Style & Naming Conventions
- Language: TypeScript (ES2023 target); 2‑space indentation.
- Prettier: single quotes, trailing commas (`.prettierrc`).
- ESLint: TypeScript rules with Prettier integration (`eslint.config.mjs`).
- Names: files `kebab-case.ts`; classes/interfaces `PascalCase`; variables `camelCase`.
- Modules: group features under `src/modules/<feature>`; put services/controllers inside the feature folder.

## Testing Guidelines
- Framework: Jest with `ts-jest`.
- Unit tests: colocate as `*.spec.ts` beside sources in `src/`.
- E2E tests: place in `test/` and name `*.e2e-spec.ts`.
- Coverage: use `npm run test:cov`; keep meaningful coverage for changed code.

## Commit & Pull Request Guidelines
- Commits: follow Conventional Commits (e.g., `feat: add loyalty VIP tiers`, `fix(shop): handle null config`).
- PRs: include summary, linked issues, test plan/outputs, and notes on API/DB changes (entities/migrations) or queue impacts.
- Ensure `npm run lint` and tests pass; update Swagger decorators when API changes.

## Security & Configuration Tips
- Use `.env` for local; never commit secrets. Validate env via `@nestjs/config` + `joi`.
- TypeORM (MySQL) and BullMQ require configured hosts/credentials; document any new variables in the PR.
