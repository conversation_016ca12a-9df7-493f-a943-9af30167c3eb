import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON>clas<PERSON>ontroller } from './aclas.controller';

describe('AclasController', () => {
  let controller: AclasController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AclasController],
    }).compile();

    controller = module.get<AclasController>(AclasController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
