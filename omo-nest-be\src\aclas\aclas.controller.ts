import { Body, Controller, Post } from '@nestjs/common';
import { CustomerCreatePayload } from 'src/customers/type';
import { AclasService } from './services/aclas.service';

@Controller('aclas')
export class AclasController {
  constructor(private readonly aclasService: AclasService) {}

  @Post()
  async addMember(
    @Body()
    payload: CustomerCreatePayload & { shop: string; accessToken: string },
  ) {
    return this.aclasService.addMember(payload);
  }
}
