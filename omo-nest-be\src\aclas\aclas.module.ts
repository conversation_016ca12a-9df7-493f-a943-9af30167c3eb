import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AclasPosConfig } from 'src/shop/entities/aclas-pos-config.entity';
import { ShopModule } from 'src/shop/shop.module';
import { AclasController } from './aclas.controller';
import { AclasApiService } from './services/aclas-api.service';
import { AclasConfigService } from './services/aclas-config.service';
import { AclasCustomerService } from './services/aclas-customer.service';
import { AclasGraphqlService } from './services/aclas-graphql.service';
import { AclasMapperService } from './services/aclas-mapper.service';
import { AclasShopifyOrderService } from './services/aclas-shopify-order.service';
import { AclasTransactionService } from './services/aclas-transaction.service';
import { AclasService } from './services/aclas.service';

@Module({
  imports: [HttpModule, ShopModule, TypeOrmModule.forFeature([AclasPosConfig])],
  controllers: [AclasController],
  providers: [
    AclasService,
    AclasConfigService,
    AclasCustomerService,
    AclasTransactionService,
    AclasGraphqlService,
    AclasMapperService,
    AclasShopifyOrderService,
    AclasApiService,
  ],
  exports: [AclasService, AclasConfigService, AclasTransactionService],
})
export class AclasModule {}
