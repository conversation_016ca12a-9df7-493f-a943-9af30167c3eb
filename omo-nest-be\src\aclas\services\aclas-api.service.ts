import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { AclasMemberAddPayload, AclasResponse } from '../type';

@Injectable()
export class AclasApiService {
  constructor(private readonly httpService: HttpService) {}

  private readonly logger = new Logger(AclasApiService.name);

  /**
   * Add a member to Aclas POS system
   * @param payload The member data payload
   * @param apiUrl The Aclas API endpoint URL
   * @returns Promise<AclasResponse> The response from Aclas API
   */
  async addMember(
    payload: AclasMemberAddPayload,
    apiUrl: string,
  ): Promise<AclasResponse> {
    try {
      this.logger.log(`Making API call to Aclas: ${apiUrl}`);

      const { data } = await this.httpService.axiosRef.post<AclasResponse>(
        apiUrl,
        payload,
      );

      this.logger.log(`Successfully added member to Aclas: ${data.code}`);
      return data;
    } catch (error) {
      this.logger.error(`Error making API call to Aclas: ${error}`);
      throw error;
    }
  }
}
