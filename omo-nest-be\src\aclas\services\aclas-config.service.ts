import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AclasPosConfig } from 'src/shop/entities/aclas-pos-config.entity';
import { ShopifyService } from 'src/shop/shopify.service';
import { Repository } from 'typeorm';

@Injectable()
export class AclasConfigService {
  constructor(
    private readonly shopifyService: ShopifyService,
    @InjectRepository(AclasPosConfig)
    private readonly aclasPosConfigRepository: Repository<AclasPosConfig>,
  ) {}

  private readonly logger = new Logger(AclasConfigService.name);

  /**
   * Get Aclas configuration for a shop
   * @param shopDomain The shop domain
   * @returns Promise<AclasPosConfig | null> The Aclas configuration or null if not found
   */
  async getAclasConfig(shopDomain: string): Promise<AclasPosConfig | null> {
    try {
      // Step 1: Find the shop by domain
      const shop = await this.shopifyService.findShopByDomain(shopDomain);
      if (!shop) {
        this.logger.warn(`Shop not found: ${shopDomain}`);
        return null;
      }

      // Step 2: Get Aclas configuration
      const config = await this.aclasPosConfigRepository.findOne({
        where: { shopId: shop.id },
      });

      if (!config) {
        this.logger.log(`No Aclas config found for shop: ${shopDomain}`);
        return null;
      }

      return config;
    } catch (error) {
      this.logger.error(
        `Error retrieving Aclas config for shop ${shopDomain}: ${error}`,
      );
      return null;
    }
  }

  /**
   * Validate merchant number against database
   * @param merchantNo The merchant number to validate
   * @returns Promise<AclasPosConfig | null> The Aclas configuration if valid, null otherwise
   */
  async validateMerchantNo(merchantNo: string): Promise<AclasPosConfig | null> {
    try {
      if (!merchantNo) {
        this.logger.warn('Merchant number is empty or null');
        return null;
      }

      // Find configuration by merchant number
      const config = await this.aclasPosConfigRepository.findOne({
        where: { merchantNo },
        relations: ['shop'],
      });

      if (!config) {
        this.logger.warn(`No Aclas config found for merchantNo: ${merchantNo}`);
        return null;
      }

      // Check if configuration is active
      if (!config.isActive) {
        this.logger.warn(
          `Aclas config is not active for merchantNo: ${merchantNo}`,
        );
        return null;
      }

      // Check if required credentials exist
      if (!config.appSecret) {
        this.logger.warn(
          `Aclas config missing credentials for merchantNo: ${merchantNo}`,
        );
        return null;
      }

      this.logger.log(`Valid Aclas config found for merchantNo: ${merchantNo}`);
      return config;
    } catch (error) {
      this.logger.error(`Error validating merchantNo ${merchantNo}: ${error}`);
      return null;
    }
  }
}
