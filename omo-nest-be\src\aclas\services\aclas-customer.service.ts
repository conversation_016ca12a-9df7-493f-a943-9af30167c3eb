import { gql } from '@apollo/client/core';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import dayjs from 'dayjs';
import { CustomerCreatePayload } from 'src/customers/type';
import { AclasPosConfig } from 'src/shop/entities/aclas-pos-config.entity';
import { ShopifyService } from 'src/shop/shopify.service';
import { v6 as uuidv6 } from 'uuid';
import { AclasMemberAddPayload } from '../type';

interface MemberTier {
  tierNo: string;
  tierName: string;
  pointRate: number;
  discountRate: number;
}

@Injectable()
export class AclasCustomerService {
  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly httpService: HttpService,
  ) {}

  private readonly logger = new Logger(AclasCustomerService.name);

  /**
   * Retrieve customer metafields from Shopify
   * @param customerId The Shopify customer admin GraphQL API ID
   * @param accessToken The shop access token
   * @param shop The shop domain
   * @returns Object containing birthday, idCard, and gender values
   */
  async getCustomerMetafields(
    customerId: string,
    accessToken: string,
    shop: string,
  ): Promise<{ birthday: string; idCard: string; gender: string }> {
    try {
      const { client } = this.shopifyService.getAdminClient({
        myshopifyDomain: shop,
        shopToken: accessToken,
      });

      const GET_CUSTOMER_METAFIELDS = gql`
        query GetCustomerMetafields($customerId: ID!) {
          customer(id: $customerId) {
            metafields(first: 10, namespace: "$app") {
              edges {
                node {
                  key
                  value
                }
              }
            }
          }
        }
      `;

      interface MetafieldNode {
        key: string;
        value: string;
      }

      interface MetafieldEdge {
        node: MetafieldNode;
      }

      interface CustomerMetafieldsResponse {
        customer: {
          metafields: {
            edges: MetafieldEdge[];
          };
        } | null;
      }

      const { data } = await client.query<CustomerMetafieldsResponse>({
        query: GET_CUSTOMER_METAFIELDS,
        variables: { customerId },
      });

      const metafields = data?.customer?.metafields?.edges || [];
      const metafieldMap = new Map<string, string>();

      metafields.forEach((edge) => {
        const node = edge.node;
        if (node?.key && node?.value) {
          metafieldMap.set(node.key, node.value);
        }
      });

      // Extract birthday, idCard, and gender from metafields
      const birthday = metafieldMap.get('birth_date') || '';
      const idCard = metafieldMap.get('id_card') || '';
      const gender = metafieldMap.get('gender') || '';

      this.logger.log(
        `Retrieved metafields for customer ${customerId}: birthday=${!!birthday}, idCard=${!!idCard}, gender=${!!gender}`,
      );

      return { birthday, idCard, gender };
    } catch (error) {
      this.logger.error(
        `Error retrieving metafields for customer ${customerId}: ${error}`,
      );
      // Return empty values on error to maintain functionality
      return { birthday: '', idCard: '', gender: '' };
    }
  }

  /**
   * Map customer data to Aclas payload format
   * @param payload Customer create payload
   * @param config Aclas POS configuration
   * @returns Promise<AclasMemberAddPayload> Mapped payload for Aclas API
   */
  async mapCustomerToAclasPayload(
    payload: CustomerCreatePayload & { shop: string; accessToken: string },
    config: AclasPosConfig,
  ): Promise<AclasMemberAddPayload> {
    const defaultAddress = payload.default_address;

    // Retrieve customer metafields for birthday, idCard, and gender
    const {
      birthday,
      idCard,
      gender: genderValue,
    } = await this.getCustomerMetafields(
      payload.admin_graphql_api_id,
      payload.accessToken,
      payload.shop,
    );

    // Generate unique identifiers
    const requestId = uuidv6();
    const memberNo = payload.id.toString(); // Use Shopify customer ID as member number
    const shortcutCode = this.generateShortcutCode(
      payload.first_name,
      payload.last_name,
      payload.id.toString(),
    );

    // Map gender from retrieved metafield value
    const gender = this.mapGenderToAclasFormat(genderValue);

    // Generate password - in a real implementation, this might be configurable
    // const passwd = this.generateMemberPassword();
    const passwd = '';

    // Format birthday for Aclas API (assuming YYYY-MM-DD format)
    const formattedBirthday = await this.formatBirthday(birthday);

    return {
      requestId,
      merchantNo: config.merchantNo!, // Use merchantNo from settings or fallback to apiKey
      appId: config.appId!,
      appSecret: config.appSecret!,
      memberNo,
      beginDate: null, // Optional - could be set to registration date
      endDate: null, // Optional - could be set based on business rules
      memberName:
        `${payload.first_name || ''} ${payload.last_name || ''}`.trim() ||
        payload.email,
      shortcutCode,
      firstName: payload.first_name || '',
      lastName: payload.last_name || '',
      passwd,
      isNeedPwd: false, // Configurable based on business requirements
      birthday: formattedBirthday,
      idCard: idCard,
      email: payload.email,
      phoneNo: payload.phone || defaultAddress?.phone || '',
      address: this.formatAddress(defaultAddress),
      postCode: defaultAddress?.zip || '',
      gender,
      memberTierNo: await this.getMemberTierNo(config),
      notes: payload.note || '',
    };
  }

  /**
   * Generate a shortcut code based on customer information
   * @param firstName Customer's first name
   * @param lastName Customer's last name
   * @param customerId Customer's ID
   * @returns Generated shortcut code
   */
  generateShortcutCode(
    firstName?: string,
    lastName?: string,
    customerId?: string,
  ): string {
    // Generate a shortcut code based on name initials and customer ID
    const firstInitial = firstName?.charAt(0)?.toUpperCase() || '';
    const lastInitial = lastName?.charAt(0)?.toUpperCase() || '';
    const idSuffix =
      customerId?.slice(-4) ||
      Math.random().toString(36).substr(2, 4).toUpperCase();

    return `${firstInitial}${lastInitial}${idSuffix}`;
  }

  /**
   * Generate a member password
   * @returns Generated 6-digit password
   */
  generateMemberPassword(): string {
    // Generate a simple 6-digit password
    // In production, this should be more sophisticated and possibly user-configurable
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Format customer address for Aclas API
   * @param address Customer's default address
   * @returns Formatted address string
   */
  formatAddress(address?: CustomerCreatePayload['default_address']): string {
    if (!address) return '';

    const parts = [
      address.address1,
      address.address2,
      address.city,
      address.province,
      address.country,
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Map gender from metafield value to Aclas API format
   * @param genderValue The gender value from metafield ("Male", "Female", "Other")
   * @returns Numeric gender code (0: unknown, 1: male, 2: Female)
   */
  mapGenderToAclasFormat(genderValue: string): 0 | 1 | 2 {
    if (!genderValue) return 0;

    const normalizedGender = genderValue.toLowerCase().trim();

    switch (normalizedGender) {
      case 'male':
        return 1;
      case 'female':
        return 2;
      case 'other':
        return 0;
      default:
        this.logger.warn(
          `Unknown gender value: ${genderValue}, defaulting to unknown (0)`,
        );
        return 0;
    }
  }

  /**
   * Get member tier number from Wisdor API
   * @param config Aclas POS configuration
   * @returns Promise<string> The first available tier number or empty string
   */
  async getMemberTierNo(config: AclasPosConfig): Promise<string> {
    try {
      const requestId = uuidv6();
      const requestBody = {
        requestId,
        merchantNo: config.merchantNo || '',
        appId: config.appId || '',
        appSecret: config.appSecret || '',
      };

      this.logger.log(
        `Fetching member tiers from Wisdor API with requestId: ${requestId}`,
      );

      const { data } = await this.httpService.axiosRef.post<MemberTier[]>(
        'https://api.wisdor.com/pos/openapi/memberTier/query',
        requestBody,
      );

      // Get first tier if available
      if (data && Array.isArray(data) && data.length > 0) {
        const firstTier = data[0];
        this.logger.log(
          `Retrieved member tier: ${firstTier.tierNo} - ${firstTier.tierName}`,
        );
        return firstTier.tierNo;
      }

      this.logger.warn('No member tiers returned from Wisdor API');
      return '';
    } catch (error) {
      this.logger.error(
        `Error fetching member tiers from Wisdor API: ${error}`,
      );
      // Return empty string on error to not break customer creation
      return '';
    }
  }

  /**
   * Format birthday from metafield value to the format expected by Aclas API
   * @param birthday The birthday value from metafield
   * @returns Formatted birthday string or null if invalid
   */
  async formatBirthday(birthday: string): Promise<string | null> {
    if (!birthday) return null;

    try {
      // Handle different possible date formats from metafields using dayjs
      // Common formats: YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, etc.
      const customParseFormat = (await import('dayjs/plugin/customParseFormat'))
        .default;
      dayjs.extend(customParseFormat);

      const date = dayjs(birthday);

      // Check if date is valid
      if (!date.isValid()) {
        this.logger.warn(`Invalid birthday format: ${birthday}`);
        return null;
      }

      // Format as YYYY-MM-DD for Aclas API
      return date.format('YYYY-MM-DD');
    } catch (error) {
      this.logger.warn(`Error formatting birthday ${birthday}: ${error}`);
      return null;
    }
  }
}
