import type { ApolloClient, NormalizedCacheObject } from '@apollo/client';
import { gql } from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import { OrderMetafield } from '../type';

@Injectable()
export class AclasGraphqlService {
  private readonly logger = new Logger(AclasGraphqlService.name);

  // GraphQL documents
  private readonly PRODUCT_VARIANT_BY_BARCODE = gql`
    query VariantByBarcode($query: String!) {
      productVariants(first: 1, query: $query) {
        nodes {
          id
          barcode
          sku
          title
          product {
            id
            title
          }
        }
      }
    }
  `;

  private readonly LOCATIONS_BY_QUERY = gql`
    query FindLocation($query: String!) {
      locations(first: 5, query: $query) {
        nodes {
          id
          name
        }
      }
    }
  `;

  private readonly ORDER_CREATE = gql`
    mutation CreateOrder(
      $order: OrderCreateOrderInput!
      $options: OrderCreateOptionsInput
    ) {
      orderCreate(order: $order, options: $options) {
        order {
          id
          name
          poNumber
          currencyCode
          processedAt
          metafields(first: 20) {
            nodes {
              id
              namespace
              key
              value
              type
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  /**
   * Find variant by barcode
   * @param client Apollo client
   * @param barcode Product barcode
   * @returns Promise<string | null> Variant GID or null
   */
  async findVariantByBarcode(
    client: ApolloClient<NormalizedCacheObject>,
    barcode: string,
  ): Promise<string | null> {
    if (!barcode) return null;

    try {
      const query = `barcode:${JSON.stringify(barcode)}`; // search syntax
      const resp = await client.query<{
        productVariants: { nodes: { id: string }[] };
      }>({
        query: this.PRODUCT_VARIANT_BY_BARCODE,
        variables: { query },
        fetchPolicy: 'no-cache',
      });

      const variantId = resp.data.productVariants.nodes[0]?.id ?? null;
      if (variantId) {
        this.logger.log(`Found variant for barcode ${barcode}: ${variantId}`);
      } else {
        this.logger.warn(`No variant found for barcode: ${barcode}`);
      }

      return variantId;
    } catch (error) {
      this.logger.error(
        `Error finding variant by barcode ${barcode}: ${error}`,
      );
      return null;
    }
  }

  /**
   * Create Shopify order via GraphQL mutation
   * @param client Apollo client
   * @param orderInput Order input data
   * @param optionsInput Options for order creation
   * @returns Promise with order creation result
   */
  async createOrder(
    client: ApolloClient<NormalizedCacheObject>,
    orderInput: {
      currency: string;
      processedAt: string;
      poNumber?: string | null;
      sourceName: string;
      sourceIdentifier: string;
      note?: string;
      test: boolean;
      lineItems: any[];
      transactions: any[];
      name?: string;
      customAttributes: { key: string; value: string }[];
      tags: string[];
      metafields?: OrderMetafield[];
    },
    optionsInput: {
      inventoryBehaviour?: 'BYPASS' | 'IGNORE' | 'FOLLOW';
      sendReceipt?: boolean;
      sendFulfillmentReceipt?: boolean;
    },
  ) {
    const resp = await client.mutate<{
      orderCreate: {
        order: {
          id: string;
          name?: string | null;
          poNumber?: string | null;
          currencyCode: string;
          processedAt: string;
        } | null;
        userErrors: { field: string[]; message: string }[];
      };
    }>({
      mutation: this.ORDER_CREATE,
      variables: { order: orderInput, options: optionsInput },
    });

    const userErrors = resp.data?.orderCreate.userErrors ?? [];

    if (userErrors.length) {
      // Check for metafield-specific errors
      const metafieldErrors = userErrors.filter((e) =>
        e.field?.some((field) => field.includes('metafield')),
      );

      if (metafieldErrors.length > 0) {
        const metafieldMsg = metafieldErrors
          .map((e) => `${e.field?.join('.') || 'metafield'}: ${e.message}`)
          .join('; ');
        this.logger.error(`Metafield creation failed: ${metafieldMsg}`);
        throw new Error(`Failed to create order metafields: ${metafieldMsg}`);
      }

      // Handle other errors
      const msg = userErrors
        .map((e) => `${e.field?.join('.') || 'orderCreate'}: ${e.message}`)
        .join('; ');
      this.logger.error(`Order creation failed: ${msg}`);
      throw new Error(`Shopify orderCreate errors: ${msg}`);
    }

    if (!resp.data?.orderCreate.order) {
      this.logger.error(
        'Order creation failed: No order returned from Shopify',
      );
      throw new Error('Failed to create Shopify order: No order returned');
    }

    this.logger.log(
      `Successfully created Shopify order: ${resp.data.orderCreate.order.id}`,
    );
    return resp.data.orderCreate.order;
  }
}
