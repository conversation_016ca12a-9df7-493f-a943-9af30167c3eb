import { Injectable, Logger } from '@nestjs/common';
import {
  AclasTransactionPayload,
  CustomAttribute,
  CustomLineItem,
  LineItem,
  LineItemBase,
  MoneyBagInput,
  OrderMetafield,
  OrderTransaction,
  PushOptions,
  TaxLine,
  VariantLineItem,
} from '../type';

@Injectable()
export class AclasMapperService {
  private readonly logger = new Logger(AclasMapperService.name);

  /**
   * Create MoneyBag input for GraphQL
   * @param amount Amount as string or number
   * @param currencyCode Currency code
   * @returns MoneyBag input object
   */
  moneyBag(amount: number | string, currencyCode: string): MoneyBagInput {
    return { shopMoney: { amount: String(amount), currencyCode } };
  }

  /**
   * Build tax lines for line item
   * @param item ACLAS item
   * @param currency Currency code
   * @returns Tax lines array
   */
  buildItemTaxLines(
    item: AclasTransactionPayload['items'][number],
    currency: string,
  ): TaxLine[] {
    // Prefer detailed parts (saleTax1..4) if present; otherwise fall back to taxAmount + saleTaxRate.
    const parts = [
      {
        amt: item.saleTax1,
        rate: item.saleTaxRate1,
        title: item.saleTaxRateName1,
      },
      {
        amt: item.saleTax2,
        rate: item.saleTaxRate2,
        title: item.saleTaxRateName2,
      },
      {
        amt: item.saleTax3,
        rate: item.saleTaxRate3,
        title: item.saleTaxRateName3,
      },
      {
        amt: item.saleTax4,
        rate: item.saleTaxRate4,
        title: item.saleTaxRateName4,
      },
    ].filter((p) => (p.amt ?? 0) > 0);

    if (parts.length) {
      return parts.map((p) => ({
        title: (p.title && p.title.trim()) || `${p.rate}% tax`,
        rate: (p.rate || 0) / 100,
        priceSet: this.moneyBag(Number(p.amt).toFixed(2), currency),
      }));
    }

    const taxAmt = item.taxAmount || 0;
    if (taxAmt > 0) {
      const rate = (item.saleTaxRate || 0) / 100;
      const title =
        (item.saleTaxRateName1 && item.saleTaxRateName1.trim()) ||
        `${item.saleTaxRate}% tax`;
      return [
        { title, rate, priceSet: this.moneyBag(taxAmt.toFixed(2), currency) },
      ];
    }
    return [];
  }

  /**
   * Build line item from ACLAS item data
   * @param item ACLAS item
   * @param currency Currency code
   * @param variantId Shopify variant ID if found
   * @returns LineItem (variant or custom)
   */
  buildLineItem(
    item: AclasTransactionPayload['items'][number],
    currency: string,
    variantId: string | null,
  ): LineItem {
    const quantity = Math.max(1, Math.round(item.qty || 0));
    const lineExTax = Number.isFinite(item.totalAmountExTax)
      ? item.totalAmountExTax
      : item.price * quantity;
    const unitPriceExTax = (lineExTax / quantity).toFixed(2);
    const taxLines = this.buildItemTaxLines(item, currency);

    const baseLineItem: LineItemBase = {
      quantity,
      priceSet: this.moneyBag(unitPriceExTax, currency),
      taxLines,
      properties: [
        { name: 'aclas_barcode', value: item.barcode },
        { name: 'aclas_productCode', value: item.productCode },
        { name: 'aclas_unit', value: item.unit || '' },
        { name: 'aclas_categoryNo', value: item.categoryNo },
      ].filter((p) => p.value),
    };

    if (variantId) {
      return { ...baseLineItem, variantId } as VariantLineItem;
    } else {
      return {
        ...baseLineItem,
        title: item.productName,
        sku: item.productCode,
        taxable:
          (item.taxAmount ||
            item.saleTax1 ||
            item.saleTax2 ||
            item.saleTax3 ||
            item.saleTax4) > 0,
      } as CustomLineItem;
    }
  }

  /**
   * Build order transactions from ACLAS payments
   * @param payments ACLAS payment data
   * @param currency Currency code
   * @param processedAt Processing timestamp
   * @param locationId Location ID if available
   * @param test Whether this is a test transaction
   * @returns Array of order transactions
   */
  buildOrderTransactions(
    payments: AclasTransactionPayload['payments'],
    currency: string,
    processedAt: string,
    locationId?: string,
    test = false,
  ): OrderTransaction[] {
    return (payments || []).map((p) => ({
      kind: 'SALE',
      status: 'SUCCESS',
      gateway: p.paymentMethodName || 'External POS',
      amountSet: this.moneyBag((p.receivedAmount ?? 0).toFixed(2), currency),
      processedAt,
      ...(locationId ? { locationId } : {}),
      test,
      receiptJson: {
        paymentMethodNo: p.paymentMethodNo,
        itemNo: p.itemNo,
        couponNo: p.couponNo,
        note: p.note,
      },
    }));
  }

  /**
   * Build custom attributes from ACLAS payload
   * @param payload ACLAS transaction payload
   * @returns Array of custom attributes
   */
  buildCustomAttributes(payload: AclasTransactionPayload): CustomAttribute[] {
    return [
      { key: 'aclas_orderNo', value: payload.summary?.orderNo },
      { key: 'aclas_invoiceMemo', value: payload.summary?.invoiceMemo || '' },
      { key: 'merchantNo', value: payload.merchantNo },
      { key: 'storeNo', value: payload.storeNo || payload.summary?.storeNo },
      { key: 'posNo', value: payload.posNo || payload.summary?.posNo },
      { key: 'deviceId', value: payload.deviceId },
      { key: 'staffNo', value: payload.summary?.staffNo || '' },
      { key: 'staffName', value: payload.summary?.staffName || '' },
      { key: 'orderType', value: String(payload.summary?.orderType ?? '') },
    ].filter((a) => a.value !== undefined && a.value !== null);
  }

  /**
   * Build tags from ACLAS payload
   * @param payload ACLAS transaction payload
   * @returns Array of tags
   */
  buildTags(payload: AclasTransactionPayload): string[] {
    return [
      'source:aclas-pos',
      `store:${payload.storeNo || payload.summary?.storeNo}`,
      `pos:${payload.posNo || payload.summary?.posNo}`,
    ].filter(Boolean);
  }

  /**
   * Validate decimal value format
   * @param value Value to validate
   * @returns Validated decimal string
   */
  private validateDecimalValue(value: number | string): string {
    if (value === '' || value === null || value === undefined) {
      return '0';
    }
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : numValue.toFixed(2);
  }

  /**
   * Validate integer value format
   * @param value Value to validate
   * @returns Validated integer string or empty string
   */
  private validateIntegerValue(
    value: number | string | null | undefined,
  ): string | undefined | number {
    if (value === undefined) {
      return undefined;
    }
    if (value === '' || value === null) {
      return '';
    }
    const numValue = typeof value === 'string' ? parseInt(value, 10) : value;
    return isNaN(numValue) ? '' : String(Math.floor(numValue));
  }

  /**
   * Validate date value format
   * @param value Date value to validate
   * @returns ISO 8601 formatted date string
   */
  private validateDateValue(value: string | null | undefined): string {
    if (!value) {
      return new Date().toISOString();
    }

    try {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        this.logger.warn(`Invalid date value: ${value}, using current date`);
        return new Date().toISOString();
      }
      return date.toISOString();
    } catch {
      this.logger.warn(
        `Error parsing date value: ${value}, using current date`,
      );
      return new Date().toISOString();
    }
  }

  /**
   * Build order metafields from ACLAS payload
   * @param payload ACLAS transaction payload
   * @returns Array of order metafields
   */
  buildOrderMetafields(
    payload: AclasTransactionPayload,
  ): Array<OrderMetafield> {
    const metafields = [
      {
        namespace: '$app',
        key: 'location_id',
        value: payload.storeNo || payload.summary?.storeNo || '',
        type: 'single_line_text_field',
      },
      {
        namespace: '$app',
        key: 'staff_id',
        value: payload.summary?.staffNo || '',
        type: 'single_line_text_field',
      },
      {
        namespace: '$app',
        key: 'order_date',
        value: this.validateDateValue(payload.summary?.transTime),
        type: 'date_time',
      },
      {
        namespace: '$app',
        key: 'order_id',
        value: payload.summary?.orderNo || '',
        type: 'single_line_text_field',
      },
      {
        namespace: '$app',
        key: 'invoice',
        value: payload.summary?.invoiceNo || '',
        type: 'single_line_text_field',
      },
      {
        namespace: '$app',
        key: 'total_order_amount',
        value: this.validateDecimalValue(
          payload.summary?.totalAmountIncTax || 0,
        ),
        type: 'number_decimal',
      },
      {
        namespace: '$app',
        key: 'actual_order_amount',
        value: this.validateDecimalValue(
          payload.summary?.totalAmountIncTax || 0,
        ),
        type: 'number_decimal',
      },
      {
        namespace: '$app',
        key: 'discount_amount',
        value: this.validateDecimalValue(payload.summary?.discountAmount || 0),
        type: 'number_decimal',
      },
      {
        namespace: '$app',
        key: 'redeem_points',
        value: this.validateIntegerValue(undefined), // Empty string as requested
        type: 'number_integer',
      },
      {
        namespace: '$app',
        key: 'reward_points',
        value: this.validateIntegerValue(undefined), // Empty string as requested
        type: 'number_integer',
      },
    ];

    this.logger.log(
      `Built ${metafields.length} metafields for order ${payload.summary?.orderNo}`,
    );
    return metafields;
  }

  /**
   * Get default push options for ACLAS transactions
   * @param payload ACLAS transaction payload
   * @returns Default push options
   */
  getDefaultPushOptions(payload: AclasTransactionPayload): PushOptions {
    return {
      currency: 'TWD', // Default to TWD, could be configurable
      locationNameHint: payload.storeNo,
      inventoryBehaviour: 'BYPASS',
      sendReceipt: false,
      sendFulfillmentReceipt: false,
      setOrderNameFromOrderNo: true,
      test: false,
    };
  }
}
