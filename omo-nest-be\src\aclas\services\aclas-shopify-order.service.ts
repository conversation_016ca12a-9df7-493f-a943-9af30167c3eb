import type { ApolloClient, NormalizedCacheObject } from '@apollo/client';
import { Injectable, Logger } from '@nestjs/common';
import {
  AclasTransactionPayload,
  OrderCreateInput,
  PushOptions,
  ShopifyOrderResult,
} from '../type';
import { AclasGraphqlService } from './aclas-graphql.service';
import { AclasMapperService } from './aclas-mapper.service';

@Injectable()
export class AclasShopifyOrderService {
  private readonly logger = new Logger(AclasShopifyOrderService.name);

  constructor(
    private readonly graphqlService: AclasGraphqlService,
    private readonly mapperService: AclasMapperService,
  ) {}

  /**
   * Create a Shopify Order from an ACLAS payload using ApolloClient (Admin GraphQL 2025-04)
   * @param client Apollo GraphQL client
   * @param payload ACLAS transaction payload
   * @param opts Push options
   * @returns Promise<ShopifyOrderResult> Created order
   */
  async createShopifyOrder(
    client: ApolloClient<NormalizedCacheObject>,
    payload: AclasTransactionPayload,
    opts: PushOptions,
  ): Promise<ShopifyOrderResult> {
    const {
      currency,
      inventoryBehaviour = 'BYPASS',
      sendReceipt = false,
      sendFulfillmentReceipt = false,
      setOrderNameFromOrderNo = true,
      test = false,
    } = opts;

    const processedAt = payload.summary?.transTime || new Date().toISOString();
    const locationId = undefined;

    // Build line items (prefer variant by barcode; otherwise create a custom line with priceSet + taxLines)
    const lineItems = await Promise.all(
      payload.items.map(async (item) => {
        const variantId = await this.graphqlService.findVariantByBarcode(
          client,
          item.barcode,
        );
        return this.mapperService.buildLineItem(item, currency, variantId);
      }),
    );

    // Map payments -> transactions (multi-tender) at creation time
    const transactions = this.mapperService.buildOrderTransactions(
      payload.payments,
      currency,
      processedAt,
      locationId,
      test,
    );

    const orderInput: OrderCreateInput = {
      currency,
      processedAt,
      poNumber: payload.summary?.invoiceNo,
      sourceName: 'aclas-pos',
      sourceIdentifier: payload.requestId || payload.summary?.orderNo,
      note: payload.summary?.note || undefined,
      test,
      lineItems,
      transactions,
      ...(setOrderNameFromOrderNo && payload.summary?.orderNo
        ? { name: payload.summary.orderNo }
        : {}),
      customAttributes: this.mapperService.buildCustomAttributes(payload),
      tags: this.mapperService.buildTags(payload),
      metafields: this.mapperService
        .buildOrderMetafields(payload)
        .filter((m) => m.value !== undefined && m.value !== null),
    };

    const optionsInput = {
      inventoryBehaviour,
      sendReceipt,
      sendFulfillmentReceipt,
    };

    // Execute mutation
    const order = await this.graphqlService.createOrder(
      client,
      orderInput,
      optionsInput,
    );

    this.logger.log(`Successfully created Shopify order: ${order.id}`);
    return order;
  }
}
