import { Injectable, Logger } from '@nestjs/common';
import { AclasPosConfig } from 'src/shop/entities/aclas-pos-config.entity';
import { ShopifyService } from 'src/shop/shopify.service';
import {
  AclasTransactionPayload,
  PushOptions,
  ShopifyOrderResult,
} from '../type';
import { AclasMapperService } from './aclas-mapper.service';
import { AclasShopifyOrderService } from './aclas-shopify-order.service';

@Injectable()
export class AclasTransactionService {
  private readonly logger = new Logger(AclasTransactionService.name);

  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly mapperService: AclasMapperService,
    private readonly shopifyOrderService: AclasShopifyOrderService,
  ) {}

  /**
   * Process ACLAS transaction and create Shopify order
   * @param payload ACLAS transaction payload
   * @param config ACLAS POS configuration
   * @returns Promise<ShopifyOrderResult> Created order details
   */
  async processAclasTransaction(
    payload: AclasTransactionPayload,
    config: AclasPosConfig,
  ): Promise<ShopifyOrderResult> {
    this.logger.log(`Processing ACLAS transaction: ${payload.requestId}`);

    if (!config.shop) {
      throw new Error('Shop information not found in ACLAS config');
    }

    if (!config.shop.shopToken) {
      throw new Error('Shop token not found in shop configuration');
    }

    // Get Shopify admin client
    const { client } = this.shopifyService.getAdminClient({
      myshopifyDomain: config.shop.myshopifyDomain,
      shopToken: config.shop.shopToken,
    });

    // Get default options for this transaction
    const options: PushOptions =
      this.mapperService.getDefaultPushOptions(payload);

    // Create Shopify order
    const order = await this.shopifyOrderService.createShopifyOrder(
      client,
      payload,
      options,
    );

    this.logger.log(`Successfully created Shopify order: ${order.id}`);
    return order;
  }
}
