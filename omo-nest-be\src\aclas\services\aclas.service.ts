import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CustomerCreatePayload } from 'src/customers/type';
import { AclasPosConfig } from 'src/shop/entities/aclas-pos-config.entity';
import { ShopifyService } from 'src/shop/shopify.service';
import { Repository } from 'typeorm';
import { AclasResponse } from '../type';
import { AclasApiService } from './aclas-api.service';
import { AclasConfigService } from './aclas-config.service';
import { AclasCustomerService } from './aclas-customer.service';

@Injectable()
export class AclasService {
  constructor(
    private readonly aclasConfigService: AclasConfigService,
    private readonly aclasCustomerService: AclasCustomerService,
    private readonly aclasApiService: AclasApiService,
    private readonly shopifyService: ShopifyService,
    @InjectRepository(AclasPosConfig)
    private readonly aclasPosConfigRepository: Repository<AclasPosConfig>,
  ) {}

  private readonly logger = new Logger(AclasService.name);

  /**
   * Check if a shop has active Aclas configuration
   * @param shopDomain The shop domain to check
   * @returns Promise<boolean> indicating if shop has valid Aclas config
   */
  async hasAclasConfig(shopDomain: string): Promise<boolean> {
    try {
      // Step 1: Find the shop by domain
      const shop = await this.shopifyService.findShopByDomain(shopDomain);
      if (!shop) {
        this.logger.warn(`Shop not found: ${shopDomain}`);
        return false;
      }

      // Step 2: Check if shop has Aclas configuration
      const config = await this.aclasPosConfigRepository.findOne({
        where: { shopId: shop.id },
      });

      if (!config) {
        this.logger.log(`No Aclas config found for shop: ${shopDomain}`);
        return false;
      }

      // Step 3: Verify config is active and has required credentials
      if (!config.isActive) {
        this.logger.log(`Aclas config is not active for shop: ${shopDomain}`);
        return false;
      }

      if (!config.appSecret) {
        this.logger.warn(
          `Aclas config missing credentials for shop: ${shopDomain}`,
        );
        return false;
      }

      this.logger.log(`Valid Aclas config found for shop: ${shopDomain}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Error checking Aclas config for shop ${shopDomain}:`,
        error,
      );
      return false;
    }
  }

  async addMember(
    payload: CustomerCreatePayload & { shop: string; accessToken: string },
  ): Promise<AclasResponse> {
    /**
     * 1. Get config from AclasConfigService
     * 2. Map data from payload to AclasRequest using AclasCustomerService
     * 3. Add member to Aclas using AclasApiService
     */

    try {
      // Step 1: Get config from AclasConfigService
      const config = await this.aclasConfigService.getAclasConfig(payload.shop);

      if (!config) {
        throw new NotFoundException(
          `Aclas POS config not found for shop: ${payload.shop}`,
        );
      }

      if (!config.isActive) {
        throw new BadRequestException(
          `Aclas POS config is not active for shop: ${payload.shop}`,
        );
      }

      if (!config.appSecret) {
        throw new BadRequestException(
          `Aclas POS config is missing required credentials for shop: ${payload.shop}`,
        );
      }

      // Step 2: Map data from payload to AclasRequest using AclasCustomerService
      const aclasPayload =
        await this.aclasCustomerService.mapCustomerToAclasPayload(
          payload,
          config,
        );

      // Step 3: Add member to Aclas using AclasApiService
      const apiUrl = 'https://api.wisdor.com/pos/openapi/member/add';

      this.logger.log(`Adding member to Aclas for shop: ${payload.shop}`);

      const response = await this.aclasApiService.addMember(
        aclasPayload,
        apiUrl,
      );

      this.logger.log(`Successfully added member to Aclas: ${response.code}`);
      return response;
    } catch (error) {
      this.logger.error(
        `Error adding member to Aclas for shop ${payload.shop}: ${error}`,
      );
      throw error;
    }
  }
}
