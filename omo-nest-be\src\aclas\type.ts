export interface AclasMemberAddPayload {
  requestId: string;
  merchantNo: string;
  appId: string;
  appSecret: string;
  memberNo: string;
  beginDate?: string | null;
  endDate?: string | null;
  memberName: string;
  shortcutCode: string;
  firstName: string;
  lastName: string;
  passwd: string;
  isNeedPwd: boolean;
  birthday: string | null;
  idCard: string;
  email: string;
  phoneNo: string;
  address: string;
  postCode: string;
  gender: 0 | 1 | 2;
  memberTierNo: string;
  notes: string;
}

export interface AclasResponse {
  code: string;
  message: null | string;
  payload: object;
}

export interface AclasTransactionPayload {
  requestId: string;
  merchantNo: string;
  storeNo: string;
  posNo: string;
  deviceId: string;
  summary: {
    orderNo: string;
    transTime: string;
    storeNo: string;
    posNo: string;
    discountRate: number;
    discountAmount: number;
    totalAmountExTax: number;
    totalAmountIncTax: number;
    orderType: number;
    totalTaxAmount: number;
    roundingAmount: number;
    receivableAmount: number;
    receivedAmount: number;
    change: number;
    memberNo: string;
    invoiceNo?: string | null;
    invoiceMemo?: string | null;
    type: number;
    note?: string | null;
    orderSn: number;
    originalOrderNo?: string | null;
    staffNo: string;
    staffName?: string | null;
    ceateTime: string;
  };
  items: Array<{
    itemNo: number;
    barcode: string;
    productCode: string;
    productName: string;
    categoryNo: string;
    unit?: string | null;
    discountRate: number;
    discountAmount: number;
    qty: number;
    price: number;
    amount: number;
    totalAmountExTax: number;
    totalAmountIncTax: number;
    taxAmount: number;
    saleTaxRate: number;
    saleTaxRate1: number;
    saleTaxRate2: number;
    saleTaxRate3: number;
    saleTaxRate4: number;
    saleTax1: number;
    saleTax2: number;
    saleTax3: number;
    saleTax4: number;
    saleTaxRateName1?: string | null;
    saleTaxRateName2?: string | null;
    saleTaxRateName3?: string | null;
    saleTaxRateName4?: string | null;
    note?: string | null;
  }>;
  payments: Array<{
    itemNo: number;
    paymentMethodNo: string;
    paymentMethodName: string;
    receivableAmount: number;
    receivedAmount: number;
    couponNo?: string | null;
    memberNo?: string | null;
    note?: string | null;
  }>;
}

// Shopify Transaction Service Types
export interface PushOptions {
  currency: string; // ISO 4217 code, e.g. "VND" / "CNY" / "USD"
  locationNameHint?: string;
  inventoryBehaviour?: 'BYPASS' | 'IGNORE' | 'FOLLOW';
  sendReceipt?: boolean;
  sendFulfillmentReceipt?: boolean;
  setOrderNameFromOrderNo?: boolean; // default true
  test?: boolean; // create order/test transactions
}

export interface ShopifyOrderResult {
  id: string;
  name?: string | null;
  poNumber?: string | null;
  currencyCode: string;
  processedAt: string;
}

export interface LineItemProperty {
  name: string;
  value: string;
}

export interface MoneyBagInput {
  shopMoney: {
    amount: string;
    currencyCode: string;
  };
}

export interface TaxLine {
  title: string;
  rate: number;
  priceSet: MoneyBagInput;
}

export interface LineItemBase {
  quantity: number;
  priceSet: MoneyBagInput;
  taxLines: TaxLine[];
  properties: LineItemProperty[];
}

export interface VariantLineItem extends LineItemBase {
  variantId: string;
}

export interface CustomLineItem extends LineItemBase {
  title: string;
  sku: string;
  taxable: boolean;
}

export type LineItem = VariantLineItem | CustomLineItem;

export interface OrderTransaction {
  kind: string;
  status: string;
  gateway: string;
  amountSet: MoneyBagInput;
  processedAt: string;
  locationId?: string;
  test: boolean;
  receiptJson: {
    paymentMethodNo: string;
    itemNo: number;
    couponNo?: string | null;
    note?: string | null;
  };
}

export interface CustomAttribute {
  key: string;
  value: string;
}

export interface OrderCreateInput {
  currency: string;
  processedAt: string;
  poNumber?: string | null;
  sourceName: string;
  sourceIdentifier: string;
  note?: string;
  test: boolean;
  lineItems: LineItem[];
  transactions: OrderTransaction[];
  name?: string;
  customAttributes: CustomAttribute[];
  tags: string[];
  metafields?: OrderMetafield[];
}

export interface OrderCreateOptionsInput {
  inventoryBehaviour?: 'BYPASS' | 'IGNORE' | 'FOLLOW';
  sendReceipt?: boolean;
  sendFulfillmentReceipt?: boolean;
}

export interface OrderMetafield {
  namespace: string;
  key: string;
  value: string | undefined | number;
  type: string;
}

export enum OrderMetafieldKey {
  LOCATION_ID = 'location_id',
  STAFF_ID = 'staff_id',
  ORDER_DATE = 'order_date',
  ORDER_ID = 'order_id',
  INVOICE = 'invoice',
  TOTAL_ORDER_AMOUNT = 'total_order_amount',
  ACTUAL_ORDER_AMOUNT = 'actual_order_amount',
  DISCOUNT_AMOUNT = 'discount_amount',
  REDEEM_POINTS = 'redeem_points',
  REWARD_POINTS = 'reward_points',
}

export enum MetafieldType {
  SINGLE_LINE_TEXT_FIELD = 'single_line_text_field',
  NUMBER_DECIMAL = 'number_decimal',
  NUMBER_INTEGER = 'number_integer',
  DATE_TIME = 'date_time',
}
