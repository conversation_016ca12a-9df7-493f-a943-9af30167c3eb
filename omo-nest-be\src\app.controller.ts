import { Controller, Get, Post } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { AppService } from './app.service';
import { Queue } from 'bullmq';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    @InjectQueue('example') private exampleQueue: Queue,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('test-queue')
  async testQueue() {
    const job = await this.exampleQueue.add('example-job', {
      data: 'This is a test job',
      timestamp: new Date().toISOString(),
    });

    return {
      message: 'Job added to queue',
      jobId: job.id,
      timestamp: new Date().toISOString(),
    };
  }
}
