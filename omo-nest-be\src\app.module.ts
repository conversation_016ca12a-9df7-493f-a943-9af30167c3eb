import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as Joi from 'joi';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CustomersModule } from './customers/customers.module';
import { DatabaseModule } from './database/database.module';
import { BullBoardQueueModule } from './queue/bull-board/bull-board.module';
import { QueueModule } from './queue/queue.module';

// 👇 add these
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';
import { AclasModule } from './aclas/aclas.module';
// winston-loki is CJS; require() is the safest import

import LokiTransport from 'winston-loki';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test')
          .default('development'),
        PORT: Joi.number().default(3000),
        DB_HOST: Joi.string().required(),
        DB_PORT: Joi.number().default(3306),
        DB_USERNAME: Joi.string().required(),
        DB_PASSWORD: Joi.string().allow('').optional(),
        DB_DATABASE: Joi.string().required(),
        DB_SYNCHRONIZE: Joi.boolean().default(false),
        DB_LOGGING: Joi.boolean().default(false),

        // Redis configuration
        REDIS_HOST: Joi.string().default('localhost'),
        REDIS_PORT: Joi.number().default(6379),

        // 👇 add logging-related envs
        LOG_LEVEL: Joi.string()
          .valid('error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly')
          .default('info'),
        SERVICE_NAME: Joi.string().default('shopify-omo'),
        LOKI_URL: Joi.string().default('http://localhost:3100'),
      }),
      validationOptions: { allowUnknown: true, abortEarly: true },
      envFilePath: '.env',
    }),

    // 👇 register Winston + Loki
    WinstonModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        const level = config.get<string>('LOG_LEVEL', 'info');
        const service = config.get<string>('SERVICE_NAME', 'shopify-omo');
        const env = config.get<string>('NODE_ENV', 'development');
        const lokiUrl = config.get<string>('LOKI_URL', 'http://localhost:3100');

        const transports: winston.transport[] = [
          new winston.transports.Console({
            level,
          }),
          new LokiTransport({
            host: lokiUrl, // Loki HTTP API (push endpoint is /loki/api/v1/push)
            labels: { app: service, env }, // keep labels low-cardinality
            json: true, // use JSON payloads
            interval: 5, // batch interval (seconds)
            useWinstonMetaAsLabels: true, // map defaultMeta/meta -> Loki labels
            replaceTimestamp: true,
            timeout: 5000, // 5 second timeout
            onConnectionError: (err: unknown) =>
              // avoid crashing if Loki is down
              console.error('Loki connection error', err),
          }),
        ];

        return {
          defaultMeta: { service, env },
          transports,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json(),
          ),
        };
      },
    }),

    DatabaseModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.get<string>('REDIS_HOST'),
          port: configService.get<number>('REDIS_PORT'),
        },
      }),
    }),
    QueueModule,
    BullBoardQueueModule.register(),
    CustomersModule,
    AclasModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
