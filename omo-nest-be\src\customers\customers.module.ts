import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WaysEarnReward } from '../loyalty/entities/ways-earn-reward.entity';
import { CustomerTimeline } from '../shared/entities/customer-timeline.entity';
import { ShopModule } from '../shop/shop.module';
import { CustomersService } from './customers.service';
import { AmountOffRewardService } from './services/amount-off-reward.service';
import { CustomerTimelineService } from './services/customer-timeline.service';
import { FreeShippingRewardService } from './services/free-shipping-reward.service';
import { PointsRewardService } from './services/points-reward.service';
import { RewardProcessorService } from './services/reward-processor.service';
import { StoreCreditRewardService } from './services/store-credit-reward.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([CustomerTimeline, WaysEarnReward]),
    ShopModule,
  ],
  providers: [
    CustomersService,
    CustomerTimelineService,
    RewardProcessorService,
    PointsRewardService,
    AmountOffRewardService,
    FreeShippingRewardService,
    StoreCreditRewardService,
  ],
  exports: [CustomersService],
})
export class CustomersModule {}
