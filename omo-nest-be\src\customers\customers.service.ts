import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WaysEarnRewardType } from 'src/loyalty/entities/default-ways-earn-reward-type.entity';
import { ShopifyService } from 'src/shop/shopify.service';
import { Repository } from 'typeorm';
import { WaysEarnReward } from '../loyalty/entities/ways-earn-reward.entity';
import { RewardProcessorService } from './services/reward-processor.service';
import { CustomerCreatePayload } from './type';

@Injectable()
export class CustomersService {
  private readonly logger = new Logger(CustomersService.name);

  constructor(
    @InjectRepository(WaysEarnReward)
    private readonly waysEarnRewardRepository: Repository<WaysEarnReward>,
    private readonly shopifyService: ShopifyService,
    private readonly rewardProcessorService: RewardProcessorService,
  ) {}

  /**
   * Handles customer creation webhook from Shopify
   * @param shopDomain The domain of the shop
   * @param customerCreatePayload The customer data containing admin_graphql_api_id
   */
  async handleCustomerCreate(
    shopDomain: string,
    customerCreatePayload: CustomerCreatePayload & {
      accessToken: string;
      shop: string;
    },
  ): Promise<void> {
    try {
      // Find the shop in our database
      const shop = await this.shopifyService.findShopByDomain(shopDomain);
      if (!shop?.shopId) {
        this.logger.error(`Shop not found for domain: ${shopDomain}`);
        throw new Error('Shop not found');
      }

      const shopId = shop.shopId;
      const customerId = customerCreatePayload.admin_graphql_api_id;

      if (!customerId) {
        this.logger.warn(
          `Customer ID not found in customer data for shop: ${shopId} ${customerId}`,
        );
        return;
      }

      // Get admin client for the shop
      const { client } = this.shopifyService.getAdminClient({
        myshopifyDomain: shopDomain,
        shopToken: customerCreatePayload.accessToken,
      });

      const wayToEarn = await this.waysEarnRewardRepository.findOne({
        where: {
          shopId: shop.id,
          typeEarnReward: WaysEarnRewardType.SIGN_UP,
          isActive: true,
        },
        relations: {
          rewards: true,
        },
      });

      if (!wayToEarn?.rewards?.length) {
        this.logger.log(`No active sign-up rewards found for shop: ${shopId}`);
        return;
      }

      // Process each reward using the reward processor service
      for (const reward of wayToEarn.rewards) {
        if (!reward.rewardType) {
          this.logger.warn('Reward type is missing for reward');
          continue;
        }

        if (client) {
          try {
            await this.rewardProcessorService.processReward({
              admin: client,
              customer: customerCreatePayload,
              reward: reward,
              shopId: shop.id,
            });
          } catch (error: unknown) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(
              `Error processing reward for customer ${customerId}: ${errorMessage} ${errorStack}`,
            );

            throw error;
          }
        }
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error in handleCustomerCreate: ${errorMessage} ${errorStack}`,
      );
      throw error;
    }
  }
}
