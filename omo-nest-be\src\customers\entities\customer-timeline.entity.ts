import { Shop } from 'src/shop/entities/shop.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum CustomerTimelineType {
  OTHER = 'OTHER',
  // Add other types as needed
}

@Entity('CustomerTimeline')
export class CustomerTimeline {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'shop_id' })
  shopId: number;

  @ManyToOne(() => Shop, (shop: Shop) => shop.customerTimelines, {
    onDelete: 'CASCADE',
  })
  shop: Shop;

  @Column({ name: 'customer_id' })
  @Index()
  customerId: string;

  @Column('mediumtext')
  message: string;

  @Column({
    type: 'enum',
    enum: CustomerTimelineType,
    default: CustomerTimelineType.OTHER,
  })
  type: CustomerTimelineType;

  @Column()
  date: Date;

  @Column('json', { nullable: true })
  metafields: Record<string, unknown> | null;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', nullable: true })
  updatedAt: Date | null;
}
