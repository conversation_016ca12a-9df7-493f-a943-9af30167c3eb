import { ApolloClient, NormalizedCacheObject } from '@apollo/client/core';
import { SignupReward } from '../../loyalty/entities/signup-reward.entity';
import { CustomerCreatePayload } from '../type';

export interface IRewardHandler {
  handleReward(params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: CustomerCreatePayload;
    reward: SignupReward;
    shopId: number;
  }): Promise<void>;
}
