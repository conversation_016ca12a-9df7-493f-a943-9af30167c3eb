import { ApolloClient, gql, NormalizedCacheObject } from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import dayjs from 'dayjs';
import {
  DiscountType,
  RequirementType,
  SignupReward,
} from '../../loyalty/entities/signup-reward.entity';
import { TimelineEventType } from '../../shared/entities/customer-timeline.entity';
import { IRewardHandler } from '../interfaces/reward-handler.interface';
import { CustomerCreatePayload, DiscountCodePrefix } from '../type';
import { generateRandomCode } from '../utils';
import { CustomerTimelineService } from './customer-timeline.service';

@Injectable()
export class AmountOffRewardService implements IRewardHandler {
  private readonly logger = new Logger(AmountOffRewardService.name);

  constructor(
    private readonly customerTimelineService: CustomerTimelineService,
  ) {}

  async handleReward(params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: CustomerCreatePayload;
    reward: SignupReward;
    shopId: number;
  }): Promise<void> {
    const { admin, customer, reward, shopId } = params;
    const customerId = customer.admin_graphql_api_id;

    try {
      await this.processAmountOffReward({
        admin,
        customerId,
        shopId,
        reward,
      });

      this.logger.log(
        `Successfully processed amount off reward for customer ${customerId}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to process amount off reward for customer ${customerId}: ${errorMessage} ${errorStack}`,
      );
      throw new Error(`Failed to process amount off reward: ${errorMessage}`);
    }
  }

  private async processAmountOffReward({
    admin,
    customerId,
    shopId,
    reward,
  }: {
    admin: ApolloClient<NormalizedCacheObject>;
    customerId: string;
    shopId: number;
    reward: SignupReward;
  }): Promise<void> {
    const discountCode = DiscountCodePrefix.AMOUNT_OFF + generateRandomCode();

    const {
      discountType,
      value,
      minimumRequirement,
      minimumValue,
      title,
      productDiscounts,
      orderDiscounts,
      shippingDiscounts,
    } = reward;

    const basicCodeDiscount = {
      code: discountCode,
      title,
      customerSelection: {
        customers: {
          add: [customerId],
        },
      },
      customerGets: {
        value:
          discountType === DiscountType.PERCENTAGE
            ? { percentage: Number((Number(value) / 100).toFixed(2)) }
            : { discountAmount: { amount: Number(value) } },
        items: { all: true },
      },
      minimumRequirement:
        minimumRequirement === RequirementType.AMOUNT
          ? { subtotal: { greaterThanOrEqualToSubtotal: Number(minimumValue) } }
          : minimumRequirement === RequirementType.QUANTITY
            ? {
                quantity: {
                  greaterThanOrEqualToQuantity: Number(minimumValue),
                },
              }
            : {},
      combinesWith: {
        productDiscounts: productDiscounts ?? false,
        orderDiscounts: orderDiscounts ?? false,
        shippingDiscounts: shippingDiscounts ?? false,
      },
      usageLimit: 1,
      appliesOncePerCustomer: true,
      startsAt: dayjs().toISOString(),
    };

    await admin
      .mutate({
        mutation: gql`
          mutation CreateDiscountCode(
            $basicCodeDiscount: DiscountCodeBasicInput!
          ) {
            discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
              codeDiscountNode {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: {
          basicCodeDiscount,
        },
      })
      .then(async () => {
        this.logger.log(
          `Successfully created discount code for customer ${customerId}: ${discountCode}`,
        );
        await this.customerTimelineService.addCustomerTimeline({
          shopId,
          customerId,
          message: `<p>Discount code <strong>${discountCode}</strong> has been created by signup</p>`,
          type: TimelineEventType.DISCOUNT,
        });
      })
      .catch((error) => {
        this.logger.error(`Error creating discount code: ${error}`);
        throw error;
      });
  }
}
