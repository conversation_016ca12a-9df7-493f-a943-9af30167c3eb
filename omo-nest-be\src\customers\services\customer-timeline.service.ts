import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CustomerTimeline,
  TimelineEventType,
} from '../../shared/entities/customer-timeline.entity';

@Injectable()
export class CustomerTimelineService {
  private readonly logger = new Logger(CustomerTimelineService.name);

  constructor(
    @InjectRepository(CustomerTimeline)
    private readonly customerTimelineRepository: Repository<CustomerTimeline>,
  ) {}

  /**
   * Adds an entry to the customer timeline
   * @param params Parameters for the timeline entry
   */
  async addCustomerTimeline(params: {
    shopId: number;
    customerId: string;
    message: string;
    type: TimelineEventType;
  }): Promise<void> {
    try {
      const timeline = new CustomerTimeline();
      timeline.shopId = params.shopId;
      timeline.customerId = params.customerId;
      timeline.message = params.message;
      timeline.type = params.type;
      timeline.date = new Date();

      await this.customerTimelineRepository.save(timeline);

      this.logger.log(
        `Timeline entry added for customer ${params.customerId}: ${params.type} ${params.shopId}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to add timeline entry for customer ${params.customerId}: ${errorMessage} ${errorStack}`,
      );
      throw error;
    }
  }
}
