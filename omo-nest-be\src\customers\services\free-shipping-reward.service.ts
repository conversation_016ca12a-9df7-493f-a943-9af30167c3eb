import { ApolloClient, gql, NormalizedCacheObject } from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import dayjs from 'dayjs';
import {
  RequirementType,
  SignupReward,
} from '../../loyalty/entities/signup-reward.entity';
import { TimelineEventType } from '../../shared/entities/customer-timeline.entity';
import { IRewardHandler } from '../interfaces/reward-handler.interface';
import { CustomerCreatePayload, DiscountCodePrefix } from '../type';
import { generateRandomCode } from '../utils';
import { CustomerTimelineService } from './customer-timeline.service';

@Injectable()
export class FreeShippingRewardService implements IRewardHandler {
  private readonly logger = new Logger(FreeShippingRewardService.name);

  constructor(
    private readonly customerTimelineService: CustomerTimelineService,
  ) {}

  async handleReward(params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: CustomerCreatePayload;
    reward: SignupReward;
    shopId: number;
  }): Promise<void> {
    const { admin, customer, reward, shopId } = params;
    const customerId = customer.admin_graphql_api_id;

    try {
      await this.processFreeShippingReward({
        admin,
        customerId,
        shopId,
        reward,
      });

      this.logger.log(
        `Successfully processed free shipping reward for customer ${customerId}`,
        { rewardId: reward.id, shopId },
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to process free shipping reward for customer ${customerId}: ${errorMessage} ${errorStack}`,
      );
      throw new Error(
        `Failed to process free shipping reward: ${errorMessage}`,
      );
    }
  }

  private async processFreeShippingReward({
    admin,
    customerId,
    shopId,
    reward,
  }: {
    admin: ApolloClient<NormalizedCacheObject>;
    customerId: string;
    shopId: number;
    reward: SignupReward;
  }): Promise<void> {
    const discountCode = DiscountCodePrefix.FREE_SHIP + generateRandomCode();

    const {
      productDiscounts,
      orderDiscounts,
      title,
      minimumRequirement,
      minimumValue,
    } = reward;

    const minimumRequirementInput =
      minimumRequirement === RequirementType.QUANTITY
        ? {
            quantity: {
              greaterThanOrEqualToQuantity: minimumValue?.toString(),
            },
          }
        : minimumRequirement === RequirementType.AMOUNT
          ? {
              subtotal: {
                greaterThanOrEqualToSubtotal: minimumValue?.toString(),
              },
            }
          : null;

    const input = {
      code: discountCode,
      title,
      appliesOncePerCustomer: true,
      usageLimit: 1,
      customerSelection: {
        customers: {
          add: [customerId],
        },
      },
      startsAt: dayjs().toISOString(),
      combinesWith: {
        productDiscounts: productDiscounts ?? false,
        orderDiscounts: orderDiscounts ?? false,
      },
      minimumRequirement: minimumRequirementInput,
    };

    await admin
      .mutate({
        mutation: gql`
          mutation CreateFreeShippingCode(
            $input: DiscountCodeFreeShippingInput!
          ) {
            discountCodeFreeShippingCreate(freeShippingCodeDiscount: $input) {
              codeDiscountNode {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: {
          input,
        },
      })
      .then(async () => {
        this.logger.log(
          `Successfully created free shipping discount code for customer ${customerId}: ${discountCode}`,
        );

        await this.customerTimelineService
          .addCustomerTimeline({
            shopId,
            customerId,
            message: `<p>Discount code <strong>${discountCode}</strong> has been created by signup</p>`,
            type: TimelineEventType.DISCOUNT,
          })
          .then(() => {
            this.logger.log(
              `Successfully added free shipping discount code to customer timeline for customer ${customerId}: ${discountCode}`,
            );
          });
      })
      .catch((error) => {
        this.logger.error(
          `Error creating free shipping discount code: ${error}`,
        );
        throw error;
      });
  }
}
