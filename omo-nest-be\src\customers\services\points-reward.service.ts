import { ApolloClient, gql, NormalizedCacheObject } from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import { SignupReward } from '../../loyalty/entities/signup-reward.entity';
import { TimelineEventType } from '../../shared/entities/customer-timeline.entity';
import { IRewardHandler } from '../interfaces/reward-handler.interface';
import {
  CustomerCreatePayload,
  CustomerMetafield,
  MetafieldType,
} from '../type';
import { CustomerTimelineService } from './customer-timeline.service';

@Injectable()
export class PointsRewardService implements IRewardHandler {
  private readonly logger = new Logger(PointsRewardService.name);

  constructor(
    private readonly customerTimelineService: CustomerTimelineService,
  ) {}

  async handleReward(params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: CustomerCreatePayload;
    reward: SignupReward;
    shopId: number;
  }): Promise<void> {
    const { admin, customer, reward, shopId } = params;
    const customerId = customer.admin_graphql_api_id;
    const points = Number(reward.value);

    if (!points || points <= 0) {
      this.logger.warn(
        `Invalid points value for customer ${customerId}: ${reward.value}`,
      );
      return;
    }

    try {
      await this.processPointsReward({
        admin,
        customerId,
        points,
        shopId,
      });

      this.logger.log(
        `Successfully processed points reward for customer ${customerId} ${points} ${shopId}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to process points reward for customer ${customerId}: ${errorMessage} ${errorStack}`,
      );
      throw new Error(`Failed to process points reward: ${errorMessage}`);
    }
  }

  private async processPointsReward({
    admin,
    customerId,
    points,
    shopId,
  }: {
    admin: ApolloClient<NormalizedCacheObject>;
    customerId: string;
    points: number;
    shopId: number;
  }): Promise<void> {
    await admin
      .mutate({
        mutation: gql`
          mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
            metafieldsSet(metafields: $metafields) {
              metafields {
                key
                namespace
                value
                createdAt
                updatedAt
              }
              userErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: {
          metafields: [
            {
              key: CustomerMetafield.POINTS,
              namespace: '$app',
              ownerId: customerId,
              type: MetafieldType.NUMBER_INTEGER,
              value: points.toString(),
            },
          ],
        },
      })
      .then(async () => {
        this.logger.log(
          `Awarding ${points} points to customer ${customerId} ${shopId}`,
        );

        await this.customerTimelineService.addCustomerTimeline({
          shopId,
          customerId,
          message: `Awarded ${points} points as sign-up bonus`,
          type: TimelineEventType.POINTS,
        });
      })
      .catch((error) => {
        this.logger.error(`Error creating points metafield: ${error}`);
        throw error;
      });
  }
}
