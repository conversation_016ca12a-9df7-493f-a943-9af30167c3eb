import { ApolloClient, NormalizedCacheObject } from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import {
  SignupReward,
  SignupRewardType,
} from '../../loyalty/entities/signup-reward.entity';
import { CustomerCreatePayload } from '../type';
import { AmountOffRewardService } from './amount-off-reward.service';
import { FreeShippingRewardService } from './free-shipping-reward.service';
import { PointsRewardService } from './points-reward.service';
import { StoreCreditRewardService } from './store-credit-reward.service';

@Injectable()
export class RewardProcessorService {
  private readonly logger = new Logger(RewardProcessorService.name);

  constructor(
    private readonly pointsRewardService: PointsRewardService,
    private readonly amountOffRewardService: AmountOffRewardService,
    private readonly freeShippingRewardService: FreeShippingRewardService,
    private readonly storeCreditRewardService: StoreCreditRewardService,
  ) {}

  /**
   * Process a signup reward based on its type
   * @param params Parameters for reward processing
   */
  async processReward(params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: CustomerCreatePayload;
    reward: SignupReward;
    shopId: number;
  }): Promise<void> {
    const { reward } = params;
    const { rewardType } = reward;

    this.logger.debug(
      `Processing reward type: ${rewardType} ${params.customer.admin_graphql_api_id} ${params.shopId}`,
    );

    try {
      switch (rewardType) {
        case SignupRewardType.POINTS:
          await this.pointsRewardService.handleReward(params);
          break;

        case SignupRewardType.AMOUNT_OFF:
          await this.amountOffRewardService.handleReward(params);
          break;

        case SignupRewardType.FREE_SHIPPING:
          await this.freeShippingRewardService.handleReward(params);
          break;

        case SignupRewardType.STORE_CREDIT:
          await this.storeCreditRewardService.handleReward(params);
          break;

        default:
          this.logger.warn(
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            `Unsupported reward type: ${rewardType} ${params.customer.admin_graphql_api_id} ${params.shopId}`,
          );
          return;
      }

      this.logger.log(
        `Successfully processed ${rewardType} reward ${params.customer.admin_graphql_api_id} ${params.shopId}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to process ${rewardType} reward: ${errorMessage} ${errorStack}`,
      );
      throw error;
    }
  }
}
