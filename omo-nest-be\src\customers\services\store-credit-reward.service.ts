import { ApolloClient, gql, NormalizedCacheObject } from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import { SignupReward } from '../../loyalty/entities/signup-reward.entity';
import { TimelineEventType } from '../../shared/entities/customer-timeline.entity';
import { I<PERSON>ewardHandler } from '../interfaces/reward-handler.interface';
import { CustomerCreatePayload } from '../type';
import { CustomerTimelineService } from './customer-timeline.service';

@Injectable()
export class StoreCreditRewardService implements IRewardHandler {
  private readonly logger = new Logger(StoreCreditRewardService.name);

  constructor(
    private readonly customerTimelineService: CustomerTimelineService,
  ) {}

  async handleReward(params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: CustomerCreatePayload;
    reward: SignupReward;
    shopId: number;
  }): Promise<void> {
    const { admin, customer, reward, shopId } = params;
    const customerId = customer.admin_graphql_api_id;
    const amount = Number(reward.value);

    if (!amount || amount <= 0) {
      this.logger.warn(
        `Invalid store credit amount for customer ${customerId}: ${reward.value}`,
      );
      return;
    }

    try {
      await this.processStoreCreditReward({
        admin,
        customerId,
        amount,
        shopId,
      });

      this.logger.log(
        `Successfully processed store credit reward for customer ${customerId} ${shopId} ${amount} `,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to process store credit reward for customer ${customerId}: ${errorMessage} ${errorStack}`,
      );
      throw new Error(`Failed to process store credit reward: ${errorMessage}`);
    }
  }

  private async processStoreCreditReward({
    admin,
    customerId,
    amount,
    shopId,
  }: {
    admin: ApolloClient<NormalizedCacheObject>;
    customerId: string;
    amount: number;
    shopId: number;
  }): Promise<void> {
    await admin
      .mutate({
        mutation: gql`
          mutation storeCreditAccountCredit(
            $id: ID!
            $creditInput: StoreCreditAccountCreditInput!
          ) {
            storeCreditAccountCredit(id: $id, creditInput: $creditInput) {
              storeCreditAccountTransaction {
                amount {
                  amount
                  currencyCode
                }
                account {
                  id
                  balance {
                    amount
                    currencyCode
                  }
                }
              }
              userErrors {
                message
                field
              }
            }
          }
        `,
        variables: {
          id: customerId,
          creditInput: {
            creditAmount: {
              amount: amount.toString(),
              currencyCode: 'TWD',
            },
          },
        },
      })
      .then(async () => {
        this.logger.log(
          `Successfully created store credit for customer ${customerId}: ${amount}`,
        );
        await this.customerTimelineService.addCustomerTimeline({
          shopId,
          customerId,
          message: `<p>This customer get <strong>${amount} TWD</strong> store credit by signup</p>`,
          type: TimelineEventType.OTHER,
        });
      })
      .catch((error) => {
        this.logger.error(`Error creating store credit :  ${error}`);
        throw error;
      });
  }
}
