import { ApolloClient, NormalizedCacheObject } from '@apollo/client/core';

export type AdminClient = {
  graphql: (
    query: string,
    variables?: Record<string, any>,
  ) => Promise<{ data: any }>;
};

export interface Reward {
  id?: string | number;
  rewardType: string;
  pointsAmount?: number;
  discountCode?: string;
  discountPercentage?: number;
  validFrom?: Date;
  validTo?: Date;
  isActive?: boolean;
  [key: string]: any;
}

export type RewardHandler = {
  handleReward: (params: {
    admin: ApolloClient<NormalizedCacheObject>;
    customer: { admin_graphql_api_id: string };
    reward: Reward;
    shopId: number;
  }) => Promise<void>;
};

export interface CustomerCreatePayload {
  id: string;
  created_at: string;
  updated_at: string;
  first_name?: string;
  last_name?: string;
  state: string;
  note?: string;
  verified_email: boolean;
  multipass_identifier?: string | null;
  tax_exempt: boolean;
  email: string;
  phone?: string;
  currency: string;
  addresses: Array<{
    id: number;
    customer_id: number;
    first_name: string;
    last_name: string;
    company?: string | null;
    address1: string;
    address2?: string | null;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone: string;
    name: string;
    province_code: string;
    country_code: string;
    country_name: string;
    default: boolean;
  }>;
  tax_exemptions: any[];
  admin_graphql_api_id: string;
  default_address: {
    id: number;
    customer_id: number;
    first_name: string;
    last_name: string;
    company?: string | null;
    address1: string;
    address2?: string | null;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone: string;
    name: string;
    province_code: string;
    country_code: string;
    country_name: string;
    default: boolean;
  };
}

export enum OrderMetafield {
  REDEEM_POINTS = 'redeem_points',
  REWARD_POINTS = 'reward_points',
  ORDER_DATE = 'order_date',
  INVOICE = 'invoice',
  ORDER_ID = 'order_id',
  LOCATION_ID = 'location_id',
  STAFF_ID = 'staff_id',
  DISCOUNT_AMOUNT = 'discount_amount',
  ACTUAL_ORDER_AMOUNT = 'actual_order_amount',
  TOTAL_ORDER_AMOUNT = 'total_order_amount',
}
export enum CustomerMetafield {
  STATUS = 'status',
  VIP_TIER = 'vip_tier',
  POINTS = 'points',
  IS_COMPLETED_PROFILE = 'is_completed_profile',
  BIRTH_DATE = 'birth_date',
  GENDER = 'gender',
  STORE_CREDIT = 'store_credit',
  REGISTER_DATE = 'register_date',
  REGISTER_EMPLOYEE = 'register_employee',
  REGISTER_LOCATION = 'register_location',
  FULL_NAME = 'full_name',
  TOTAL_POINTS = 'total_points',
  ID_CARD = 'id_card',
}

export enum MetafieldType {
  SINGLE_LINE_TEXT_FIELD = 'single_line_text_field',
  BOOLEAN = 'boolean',
  NUMBER_INTEGER = 'number_integer',
  NUMBER_DECIMAL = 'number_decimal',
  DATE_TIME = 'date_time',
  DATE = 'date',
}

export enum DiscountCodePrefix {
  AMOUNT_OFF = 'AMOUNT_OFF_',
  FREE_SHIP = 'FREE_SHIP_',
  REDEEM = 'OMO_POINTS_',
}
