import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from '../../shop/entities/shop.entity';
import { BirthdayReward } from './birthday-reward.entity';

@Entity('BirthdayProgramSettings')
export class BirthdayProgramSettings extends BaseEntityWithTimestamps {
  @OneToOne(() => Shop, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shopId' })
  shop: Shop;

  @Column({ unique: true })
  shopId: number;

  @Column({ default: 'Celebrate a birthday' })
  pageTitle: string;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => BirthdayReward, (reward) => reward.settings)
  rewards: BirthdayReward[];
}
