import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { BirthdayProgramSettings } from './birthday-program-settings.entity';
import { LoyaltyVIPTier } from './loyalty-vip-tier.entity';

export enum BirthdayRewardType {
  POINTS = 'points',
  STORE_CREDIT = 'store-credit',
  AMOUNT_OFF = 'amount-off',
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
}

export enum RequirementType {
  NONE = 'NONE',
  AMOUNT = 'AMOUNT',
  QUANTITY = 'QUANTITY',
}

@Entity('BirthdayReward')
export class BirthdayReward extends BaseEntityWithTimestamps {
  @ManyToOne(() => BirthdayProgramSettings, (settings) => settings.rewards, {
    onDelete: 'CASCADE',
  })
  settings: BirthdayProgramSettings;

  @Column()
  settingsId: number;

  @Column({
    type: 'enum',
    enum: BirthdayRewardType,
  })
  birthdayRewardType: BirthdayRewardType;

  @Column()
  title: string;

  @Column({ nullable: true })
  expiryMonths?: number;

  @Column({ type: 'float', nullable: true })
  rewardValue?: number;

  @Column({
    type: 'enum',
    enum: DiscountType,
    nullable: true,
  })
  discountType?: DiscountType;

  @Column({
    type: 'enum',
    enum: RequirementType,
    nullable: true,
  })
  minimumRequirement?: RequirementType;

  @Column({ type: 'float', nullable: true })
  minimumValue?: number;

  @Column({ nullable: true })
  productDiscounts?: boolean;

  @Column({ nullable: true })
  orderDiscounts?: boolean;

  @Column({ nullable: true })
  shippingDiscounts?: boolean;

  @Column({ nullable: true })
  shopifyDiscountId?: string;

  @Column({ nullable: true })
  discountCode?: string;

  @Column({ nullable: true })
  tierGroup?: string;

  @ManyToOne(() => LoyaltyVIPTier, (tier) => tier.BirthdayReward, {
    nullable: true,
  })
  vipTier?: LoyaltyVIPTier;

  @Column({ nullable: true })
  vipTierId?: number;
}
