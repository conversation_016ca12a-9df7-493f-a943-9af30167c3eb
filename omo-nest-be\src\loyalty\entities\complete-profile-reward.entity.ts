import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { CompleteProfileSettings } from './complete-profile-settings.entity';

export enum RewardType {
  POINTS = 'POINTS',
  STORE_CREDIT = 'STORE_CREDIT',
  AMOUNT_OFF = 'AMOUNT_OFF',
  FREE_SHIPPING = 'FREE_SHIPPING',
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
}

export enum MinRequirementType {
  NO_MINIMUM = 'NO_MINIMUM',
  MIN_PURCHASE_AMOUNT = 'MIN_PURCHASE_AMOUNT',
  MIN_QUANTITY_ITEMS = 'MIN_QUANTITY_ITEMS',
}

@Entity('CompleteProfileReward')
export class CompleteProfileReward extends BaseEntityWithTimestamps {
  @Column()
  title: string;

  @Column({
    type: 'enum',
    enum: RewardType,
  })
  type: RewardType;

  @Column({ nullable: true })
  value?: string;

  @Column({
    type: 'enum',
    enum: DiscountType,
    nullable: true,
  })
  discountType?: DiscountType;

  @Column({
    type: 'enum',
    enum: MinRequirementType,
    nullable: true,
  })
  minRequirementType?: MinRequirementType;

  @Column({ type: 'float', nullable: true })
  minRequirementValue?: number;

  @Column({ nullable: true })
  productDiscounts?: boolean;

  @Column({ nullable: true })
  orderDiscounts?: boolean;

  @Column({ nullable: true })
  shippingDiscounts?: boolean;

  @ManyToOne(() => CompleteProfileSettings, (settings) => settings.rewards, {
    nullable: true,
  })
  CompleteProfileSettings?: CompleteProfileSettings;

  @Column({ nullable: true })
  completeProfileSettingsId?: number;
}
