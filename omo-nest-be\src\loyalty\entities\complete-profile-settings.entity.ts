import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from '../../shop/entities/shop.entity';
import { CompleteProfileReward } from './complete-profile-reward.entity';
import { CustomProfile } from './custom-profile.entity';

@Entity('CompleteProfileSettings')
export class CompleteProfileSettings extends BaseEntityWithTimestamps {
  @OneToOne(() => Shop, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shopId' })
  shop: Shop;

  @Column({ unique: true })
  shopId: number;

  @Column({ default: 'Complete your profile' })
  pageTitle: string;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(
    () => CompleteProfileReward,
    (reward) => reward.CompleteProfileSettings,
  )
  rewards: CompleteProfileReward[];

  @OneTo<PERSON><PERSON>(() => CustomProfile, (profile) => profile.completeProfileSettings, {
    nullable: true,
  })
  @JoinColumn({ name: 'customProfileId' })
  customProfile?: CustomProfile;

  @Column({ nullable: true })
  customProfileId?: number;
}
