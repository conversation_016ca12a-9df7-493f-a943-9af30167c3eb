import { Column, <PERSON><PERSON>ty, OneToMany, OneToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { CompleteProfileSettings } from './complete-profile-settings.entity';
import { Gender } from './gender.entity';

@Entity('CustomProfile')
export class CustomProfile extends BaseEntityWithTimestamps {
  @Column({ default: true })
  editable: boolean;

  @OneToMany(() => Gender, (gender) => gender.customProfile)
  gender: Gender[];

  @OneToOne(() => CompleteProfileSettings, (settings) => settings.customProfile)
  completeProfileSettings?: CompleteProfileSettings;
}
