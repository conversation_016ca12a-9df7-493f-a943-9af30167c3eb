import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { WaysEarnReward } from './ways-earn-reward.entity';

export enum WaysEarnRewardType {
  SIGN_UP = 'SIGN_UP',
  REFERRAL = 'REFERRAL',
  CUSTOM_REWARD = 'CUSTOM_REWARD',
  COMPLETE_PROFILE = 'COMPLETE_PROFILE',
  PURCHASE = 'PURCHASE',
  CELEBRATE_BIRTHDAY = 'CELEBRATE_BIRTHDAY',
}

@Entity('DefaultWaysEarnRewardType')
export class DefaultWaysEarnRewardType extends BaseEntityWithTimestamps {
  @Column({
    type: 'enum',
    enum: WaysEarnRewardType,
    unique: true,
  })
  code: WaysEarnRewardType;

  @OneToMany(() => WaysEarnReward, (ways) => ways.defaultWaysEarnRewardType)
  ways: WaysEarnReward[];
}
