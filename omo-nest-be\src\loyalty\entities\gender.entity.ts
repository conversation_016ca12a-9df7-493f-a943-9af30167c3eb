import { Column, <PERSON>tity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { CustomProfile } from './custom-profile.entity';

@Entity('Gender')
export class Gender extends BaseEntityWithTimestamps {
  @Column({ unique: true })
  name: string;

  @ManyToOne(() => CustomProfile, (profile) => profile.gender)
  customProfile: CustomProfile;

  @Column()
  customProfileId: number;
}
