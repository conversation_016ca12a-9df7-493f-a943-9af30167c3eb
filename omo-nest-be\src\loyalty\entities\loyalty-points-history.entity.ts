import { BaseEntityWithTimestamps } from 'src/shared/entities/base.entity';
import { Column, Entity } from 'typeorm';

@Entity('LoyaltyPointsHistory')
export class LoyaltyPointsHistory extends BaseEntityWithTimestamps {
  @Column()
  customerId: string;

  @Column()
  orderId: string;

  @Column()
  points: number;

  @Column()
  earnedAt: Date;

  @Column()
  issueAt: Date;

  @Column()
  status: string; // "PENDING" | "ISSUED"

  @Column({ nullable: true })
  issuedAt?: Date;
}
