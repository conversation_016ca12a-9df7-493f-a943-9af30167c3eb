import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, OneToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { LoyaltyProgram } from './loyalty-program.entity';

export enum PointsIssueType {
  IMMEDIATE = 'IMMEDIATE',
  DELAYED = 'DELAYED',
  MANUAL = 'MANUAL',
}

export enum OrderStatus {
  PAID = 'PAID',
  FULFILLED = 'FULFILLED',
  PAID_FULFILLED = 'PAID_FULFILLED',
}

export enum RefundType {
  FULL = 'FULL',
  PROPORTIONAL = 'PROPORTIONAL',
  NONE = 'NONE',
}

@Entity('LoyaltyPoints')
export class LoyaltyPoints extends BaseEntityWithTimestamps {
  @OneToOne(() => LoyaltyProgram, (program) => program.points, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  loyaltyProgram: LoyaltyProgram;

  @Column()
  programName: string;

  @Column({ default: 'Point' })
  pointSingular: string;

  @Column({ default: 'Points' })
  pointPlural: string;

  @Column({ type: 'float', default: 1.0 })
  pointsPerCurrency: number;

  @Column({ type: 'float', default: 1.0 })
  currencyAmount: number;

  @Column({ type: 'float', default: 100 })
  pointsRedemptionAmount: number;

  @Column({ type: 'float', default: 0.01 })
  pointsRedemptionValue: number;

  @Column({ type: 'int', default: 30 })
  maxRedeemPercentage: number;

  @Column({ type: 'float', nullable: true })
  minPurchaseAmount?: number;

  @Column({ nullable: true })
  roundingMethod?: string;

  @Column({ default: false })
  isConvertPointsByPercentage: boolean;

  // Order total calculation
  @Column({ default: true })
  includeProductTotal: boolean;

  @Column({ default: false })
  includeShipping: boolean;

  @Column({ default: false })
  includeTaxes: boolean;

  // Points issue settings
  @Column({
    type: 'enum',
    enum: PointsIssueType,
    default: PointsIssueType.DELAYED,
  })
  pointsIssueType: PointsIssueType;

  @Column({ default: 14 })
  issueDays: number;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PAID_FULFILLED,
  })
  orderStatus: OrderStatus;

  // Refund handling
  @Column({
    type: 'enum',
    enum: RefundType,
    default: RefundType.PROPORTIONAL,
  })
  orderRefundType: RefundType;

  @Column({
    type: 'enum',
    enum: RefundType,
    default: RefundType.PROPORTIONAL,
  })
  redeemedRefundType: RefundType;
}
