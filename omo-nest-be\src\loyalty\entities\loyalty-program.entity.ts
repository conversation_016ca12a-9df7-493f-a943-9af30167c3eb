import { Column, Entity, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from '../../shop/entities/shop.entity';
import { LoyaltyPoints } from './loyalty-points.entity';
import { LoyaltyReferral } from './loyalty-referral.entity';
import { LoyaltyStoreCredit } from './loyalty-store-credit.entity';
import { LoyaltyVIPSettings } from './loyalty-vip-settings.entity';
import { LoyaltyVIPTier } from './loyalty-vip-tier.entity';

export enum LoyaltyProgramType {
  POINTS = 'POINTS',
  VIP = 'VIP',
  REFERRAL = 'REFERRAL',
  STORE_CREDIT = 'STORE_CREDIT',
}

@Entity('LoyaltyProgram')
export class LoyaltyProgram extends BaseEntityWithTimestamps {
  @ManyToOne(() => Shop, (shop) => shop.loyaltyPrograms, {
    onDelete: 'CASCADE',
  })
  shop: Shop;

  @Column()
  shopId: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({
    type: 'enum',
    enum: LoyaltyProgramType,
    default: LoyaltyProgramType.POINTS,
  })
  programType: LoyaltyProgramType;

  @OneToOne(() => LoyaltyPoints, (points) => points.loyaltyProgram, {
    cascade: true,
  })
  points: LoyaltyPoints;

  @OneToOne(
    () => LoyaltyVIPSettings,
    (vipSettings) => vipSettings.loyaltyProgram,
    { cascade: true },
  )
  vipSettings: LoyaltyVIPSettings;

  @OneToMany(() => LoyaltyVIPTier, (tier) => tier.loyaltyProgram, {
    cascade: true,
  })
  vipTiers: LoyaltyVIPTier[];

  @OneToMany(
    () => LoyaltyStoreCredit,
    (storeCredit) => storeCredit.loyaltyProgram,
    { cascade: true },
  )
  storeCredits: LoyaltyStoreCredit[];

  @OneToMany(() => LoyaltyReferral, (referral) => referral.loyaltyProgram, {
    cascade: true,
  })
  referrals: LoyaltyReferral[];
}
