import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { LoyaltyProgram } from './loyalty-program.entity';

@Entity('LoyaltyReferral')
export class LoyaltyReferral extends BaseEntityWithTimestamps {
  @ManyToOne(() => LoyaltyProgram, (program) => program.referrals, {
    onDelete: 'CASCADE',
  })
  loyaltyProgram: LoyaltyProgram;

  @Column()
  loyaltyProgramId: number;

  @Column()
  referrerCustomerId: number;

  @Column()
  refereeCustomerId: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  referrerRewardAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  refereeRewardAmount: number;

  @Column({ default: 'PENDING' })
  status: string;
}
