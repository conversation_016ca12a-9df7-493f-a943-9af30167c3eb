import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { LoyaltyProgram } from './loyalty-program.entity';

@Entity('LoyaltyStoreCredit')
export class LoyaltyStoreCredit extends BaseEntityWithTimestamps {
  @ManyToOne(() => LoyaltyProgram, (program) => program.storeCredits, {
    onDelete: 'CASCADE',
  })
  loyaltyProgram: LoyaltyProgram;

  @Column()
  loyaltyProgramId: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ nullable: true })
  expiryDate?: Date;

  @Column({ default: true })
  isActive: boolean;
}
