import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { LoyaltyVIPTier } from './loyalty-vip-tier.entity';

import { UnifiedRewardType } from '../../shared/types/reward.types';

export type RewardType = Extract<
  UnifiedRewardType,
  'POINTS' | 'STORE_CREDIT' | 'AMOUNT_OFF' | 'FREE_SHIPPING'
>;

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
}

export enum RequirementType {
  NONE = 'NONE',
  AMOUNT = 'AMOUNT',
  QUANTITY = 'QUANTITY',
}

@Entity('LoyaltyVIPReward')
export class LoyaltyVIPReward extends BaseEntityWithTimestamps {
  @ManyToOne(() => LoyaltyVIPTier, (tier) => tier.rewards, {
    onDelete: 'CASCADE',
  })
  vipTier: LoyaltyVIPTier;

  @Column()
  vipTierId: number;

  @Column({
    type: 'enum',
    enum: ['POINTS', 'STORE_CREDIT', 'AMOUNT_OFF', 'FREE_SHIPPING'],
    default: 'POINTS',
  })
  rewardType: RewardType;

  @Column({ type: 'int', nullable: true })
  pointsAmount?: number;

  @Column({ type: 'float', nullable: true })
  discountValue?: number;

  @Column({
    type: 'enum',
    enum: DiscountType,
    nullable: true,
  })
  discountType?: DiscountType;

  @Column({ default: false })
  isFreeShipping: boolean;

  @Column({
    type: 'enum',
    enum: RequirementType,
    default: RequirementType.NONE,
  })
  requirementType: RequirementType;

  @Column({ type: 'float', nullable: true })
  requirementAmount?: number;

  @Column({ type: 'int', nullable: true })
  requirementQuantity?: number;
}
