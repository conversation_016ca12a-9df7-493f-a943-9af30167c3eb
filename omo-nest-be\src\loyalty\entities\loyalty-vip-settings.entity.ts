import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { LoyaltyProgram } from './loyalty-program.entity';

@Entity('LoyaltyVIPSettings')
export class LoyaltyVIPSettings extends BaseEntityWithTimestamps {
  @OneToOne(() => LoyaltyProgram, (program) => program.vipSettings, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  loyaltyProgram: LoyaltyProgram;

  @Column({ default: 'points' })
  entryMethod: string;

  @Column({ default: false })
  ordersCount: boolean;

  @Column({ default: 'immediately' })
  validationType: string;

  @Column({ type: 'int', default: 0 })
  validationDays: number;

  @Column({ default: 'lifetime' })
  expirationType: string;

  @Column({ type: 'int', default: 0 })
  expirationDays: number;
}
