import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { BirthdayReward } from './birthday-reward.entity';
import { LoyaltyProgram } from './loyalty-program.entity';
import { LoyaltyVIPReward } from './loyalty-vip-reward.entity';
import { PlaceAnOrderReward } from './place-an-order-reward.entity';

@Entity('LoyaltyVIPTier')
export class LoyaltyVIPTier extends BaseEntityWithTimestamps {
  @ManyToOne(() => LoyaltyProgram, (program) => program.vipTiers, {
    onDelete: 'CASCADE',
  })
  loyaltyProgram: LoyaltyProgram;

  @Column()
  loyaltyProgramId: number;

  @Column()
  name: string;

  @Column({ type: 'float', nullable: true })
  spendRequirement?: number;

  @Column({ type: 'int', nullable: true })
  pointsRequirement?: number;

  @Column({ type: 'int', nullable: true })
  spendAmount?: number;

  @Column({ type: 'int', nullable: true })
  pointEarn?: number;

  @OneToMany(() => LoyaltyVIPReward, (reward) => reward.vipTier, {
    cascade: true,
  })
  rewards: LoyaltyVIPReward[];

  @OneToMany(() => PlaceAnOrderReward, (reward) => reward.LoyaltyVIPTier)
  PlaceAnOrderReward: PlaceAnOrderReward[];

  @Column({ default: false })
  basedOnDiffTier: boolean;

  @OneToMany(() => BirthdayReward, (reward) => reward.vipTier)
  BirthdayReward: BirthdayReward[];
}
