import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { LoyaltyVIPTier } from './loyalty-vip-tier.entity';
import { WaysEarnReward } from './ways-earn-reward.entity';

export enum RewardType {
  POINTS = 'POINTS',
  STORE_CREDIT = 'STORE_CREDIT',
  AMOUNT_OFF = 'AMOUNT_OFF',
  FREE_SHIPPING = 'FREE_SHIPPING',
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
}

export enum RequirementType {
  NONE = 'NONE',
  AMOUNT = 'AMOUNT',
  QUANTITY = 'QUANTITY',
}

@Entity('PlaceAnOrderReward')
export class PlaceAnOrderReward extends BaseEntityWithTimestamps {
  @ManyToOne(() => WaysEarnReward, (ways) => ways.purchaseRewards, {
    onDelete: 'CASCADE',
  })
  waysEarnReward: WaysEarnReward;

  @Column()
  waysEarnRewardId: number;

  @Column()
  title: string;

  @Column({
    type: 'enum',
    enum: RewardType,
  })
  rewardType: RewardType;

  @Column()
  value: string;

  @Column({
    type: 'enum',
    enum: DiscountType,
    nullable: true,
  })
  discountType?: DiscountType;

  @Column({
    type: 'enum',
    enum: RequirementType,
    nullable: true,
  })
  minimumRequirement?: RequirementType;

  @Column({ type: 'float', nullable: true })
  minimumValue?: number;

  @Column({ nullable: true })
  productDiscounts?: boolean;

  @Column({ nullable: true })
  orderDiscounts?: boolean;

  @Column({ nullable: true })
  shippingDiscounts?: boolean;

  @ManyToOne(() => LoyaltyVIPTier, (tier) => tier.PlaceAnOrderReward, {
    nullable: true,
  })
  LoyaltyVIPTier?: LoyaltyVIPTier;

  @Column({ nullable: true })
  loyaltyVIPTierId?: number;
}
