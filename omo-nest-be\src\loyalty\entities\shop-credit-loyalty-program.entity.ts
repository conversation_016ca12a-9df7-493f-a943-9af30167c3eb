import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from '../../shop/entities/shop.entity';

@Entity('ShopCreditLoyaltyProgram')
export class ShopCreditLoyaltyProgram extends BaseEntityWithTimestamps {
  @OneToOne(() => Shop, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shopId' })
  shop: Shop;

  @Column({ unique: true })
  shopId: number;

  @Column({ default: false })
  isActive: boolean;

  @Column()
  singularName: string;

  @Column({ nullable: true })
  pluralName?: string;
}
