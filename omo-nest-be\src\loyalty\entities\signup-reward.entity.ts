import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { WaysEarnReward } from './ways-earn-reward.entity';

export enum SignupRewardType {
  POINTS = 'POINTS',
  STORE_CREDIT = 'STORE_CREDIT',
  FREE_SHIPPING = 'FREE_SHIPPING',
  AMOUNT_OFF = 'AMOUNT_OFF',
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
}

export enum RequirementType {
  AMOUNT = 'AMOUNT',
  QUANTITY = 'QUANTITY',
  NONE = 'NONE',
}

@Entity('SignupReward')
export class SignupReward extends BaseEntityWithTimestamps {
  @ManyToOne(() => WaysEarnReward, (waysEarnReward) => waysEarnReward.rewards, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'waysEarnRewardId' })
  waysEarnReward: WaysEarnReward;

  @Column({ nullable: true })
  waysEarnRewardId?: number;

  @Column()
  title: string;

  @Column({
    type: 'enum',
    enum: SignupRewardType,
    default: 'POINTS',
  })
  rewardType: SignupRewardType;

  @Column({
    type: 'enum',
    enum: DiscountType,
    nullable: true,
  })
  discountType?: DiscountType;

  @Column({ nullable: true })
  value?: string;

  @Column({
    type: 'enum',
    enum: RequirementType,
    nullable: true,
  })
  minimumRequirement?: RequirementType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  minimumValue?: number;

  @Column({ nullable: true })
  productDiscounts?: boolean;

  @Column({ nullable: true })
  orderDiscounts?: boolean;

  @Column({ nullable: true })
  shippingDiscounts?: boolean;
}
