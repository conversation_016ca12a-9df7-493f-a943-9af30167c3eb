import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from '../../shop/entities/shop.entity';
import {
  DefaultWaysEarnRewardType,
  WaysEarnRewardType,
} from './default-ways-earn-reward-type.entity';
import { PlaceAnOrderReward } from './place-an-order-reward.entity';
import { SignupReward } from './signup-reward.entity';

@Entity('WaysEarnReward')
export class WaysEarnReward extends BaseEntityWithTimestamps {
  @Column({
    type: 'enum',
    enum: WaysEarnRewardType,
  })
  typeEarnReward: WaysEarnRewardType;

  @Column()
  title: string;

  @Column({ nullable: true })
  subtitle?: string;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Shop, (shop) => shop.WaysEarnReward, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  shop?: Shop;

  @Column({ nullable: true })
  shopId?: number;

  @ManyToOne(() => DefaultWaysEarnRewardType, (type) => type.ways, {
    nullable: true,
  })
  defaultWaysEarnRewardType?: DefaultWaysEarnRewardType;

  @Column({ nullable: true })
  defaultWaysEarnRewardTypeId?: number;

  @OneToMany(() => SignupReward, (reward) => reward.waysEarnReward)
  rewards: SignupReward[];

  @OneToMany(() => PlaceAnOrderReward, (reward) => reward.waysEarnReward)
  purchaseRewards: PlaceAnOrderReward[];
}
