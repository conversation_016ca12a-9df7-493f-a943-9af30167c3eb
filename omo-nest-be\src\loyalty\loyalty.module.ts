import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ShopModule } from '../shop/shop.module';
import { LoyaltyPoints } from './entities/loyalty-points.entity';
import { LoyaltyProgram } from './entities/loyalty-program.entity';
import { LoyaltyReferral } from './entities/loyalty-referral.entity';
import { LoyaltyStoreCredit } from './entities/loyalty-store-credit.entity';
import { LoyaltyVIPReward } from './entities/loyalty-vip-reward.entity';
import { LoyaltyVIPSettings } from './entities/loyalty-vip-settings.entity';
import { LoyaltyVIPTier } from './entities/loyalty-vip-tier.entity';
import { ShopCreditLoyaltyProgram } from './entities/shop-credit-loyalty-program.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LoyaltyProgram,
      LoyaltyPoints,
      LoyaltyVIPSettings,
      LoyaltyVIPTier,
      LoyaltyVIPReward,
      LoyaltyStoreCredit,
      LoyaltyReferral,
      ShopCreditLoyaltyProgram,
    ]),
    ShopModule,
  ],
  providers: [],
  controllers: [],
  exports: [],
})
export class LoyaltyModule {}
