import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { NestFactory } from '@nestjs/core';
import { Queue } from 'bullmq';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { AppModule } from './app.module';
import { QueueName } from './queue/constant';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for development
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  // Set global prefix
  app.setGlobalPrefix('api');

  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/admin/queues');

  // Redis connection configuration for BullMQ
  const redisConnection = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
  };

  createBullBoard({
    queues: [
      new BullMQAdapter(new Queue('example', { connection: redisConnection })),
      new BullMQAdapter(new Queue(QueueName.CUSTOMERS_CREATE, { connection: redisConnection })),
      new BullMQAdapter(new Queue(QueueName.CUSTOMER_TIMELINE, { connection: redisConnection })),
      new BullMQAdapter(new Queue(QueueName.ACLAS_MEMBER_ADD, { connection: redisConnection })),
      new BullMQAdapter(new Queue(QueueName.ACLAS_TRANSACTIONS, { connection: redisConnection })),
    ],
    serverAdapter,
  });

  app.use('/admin/queues', serverAdapter.getRouter());

  const port = process.env.PORT || 3000;
  await app.listen(port);

  console.log('Application is running on:', `http://localhost:${port}`);
  console.log(
    'Bull Board UI is available at:',
    `http://localhost:${port}/admin/queues`,
  );
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
