import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { BullBoardModule } from '@bull-board/nestjs';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueName } from '../constant';

@Module({
  imports: [
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
    }),
  ],
  exports: [BullBoardModule],
})
export class BullBoardQueueModule {
  constructor(private readonly configService: ConfigService) {}

  static register() {
    return {
      module: BullBoardQueueModule,
      imports: [
        ConfigModule,
        BullBoardModule.forFeature({
          name: 'example',
          adapter: BullMQAdapter,
        }),
        BullBoardModule.forFeature({
          name: QueueName.CUSTOMERS_CREATE,
          adapter: BullMQAdapter,
        }),
        BullBoardModule.forFeature({
          name: QueueName.CUSTOMER_TIMELINE,
          adapter: BullMQAdapter,
        }),
        BullBoardModule.forFeature({
          name: QueueName.ACLAS_MEMBER_ADD,
          adapter: BullMQAdapter,
        }),
        BullBoardModule.forFeature({
          name: QueueName.ACLAS_TRANSACTIONS,
          adapter: BullMQAdapter,
        }),
      ],
    };
  }
}
