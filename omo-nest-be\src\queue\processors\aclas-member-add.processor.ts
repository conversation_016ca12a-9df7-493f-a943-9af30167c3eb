import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { AclasService } from 'src/aclas/services/aclas.service';
import { CustomerCreatePayload } from 'src/customers/type';
import { QueueName } from '../constant';

@Processor(QueueName.ACLAS_MEMBER_ADD)
export class AclasMemberAddProcessor extends WorkerHost {
  constructor(private readonly aclasService: AclasService) {
    super();
  }

  private readonly logger = new Logger(AclasMemberAddProcessor.name);

  async process(
    job: Job<CustomerCreatePayload & { shop: string; accessToken: string }>,
  ): Promise<any> {
    this.logger.log(`Processing job: ${job.id}`);
    this.logger.log(`Job data:  ${JSON.stringify(job.data)}`);

    try {
      await this.aclasService.addMember(job.data);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }

    this.logger.log(`Job completed: ${job.id}`);
    return { status: 'completed' };
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(
      `Job ${job.id} completed with result: ${JSON.stringify(result)}`,
      result,
    );
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(
      `Job ${job.id} failed (attempt ${job.attemptsMade}/${job.opts.attempts ?? 1}): ${err.message}`,
    );
  }
}
