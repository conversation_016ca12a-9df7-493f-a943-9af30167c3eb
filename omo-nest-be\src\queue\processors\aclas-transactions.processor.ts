import { On<PERSON>orkerE<PERSON>, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { AclasConfigService } from 'src/aclas/services/aclas-config.service';
import { AclasTransactionService } from 'src/aclas/services/aclas-transaction.service';
import { AclasTransactionPayload } from 'src/aclas/type';

@Processor('aclas/transactions')
export class AclasTransactionsProcessor extends WorkerHost {
  private readonly logger = new Logger(AclasTransactionsProcessor.name);

  constructor(
    private readonly aclasConfigService: AclasConfigService,
    private readonly aclasTransactionService: AclasTransactionService,
  ) {
    super();
  }

  async process(
    job: Job<AclasTransactionPayload>,
    token?: string,
  ): Promise<any> {
    this.logger.log(`Processing ACLAS transaction job: ${job.id}`);
    this.logger.log(`Job token: ${token}`);

    // Log the complete ACLAS transaction data
    this.logger.log(
      `ACLAS Transaction Data: ${JSON.stringify(job.data, null, 2)}`,
    );

    // Extract key information for easier debugging
    const {
      requestId,
      merchantNo,
      storeNo,
      posNo,
      deviceId,
      summary,
      items,
      payments,
    } = job.data;

    this.logger.log(
      `txn requestId=${requestId} merchant=${merchantNo} store=${storeNo} pos=${posNo} device=${deviceId} order=${summary?.orderNo ?? 'N/A'} time=${summary?.transTime ?? 'N/A'} total=${summary?.totalAmountIncTax ?? 0} items=${items?.length ?? 0} payments=${payments?.length ?? 0}`,
    );

    // Validate merchant number against database
    this.logger.log(`Validating merchant number: ${merchantNo}`);
    const aclasConfig =
      await this.aclasConfigService.validateMerchantNo(merchantNo);

    if (!aclasConfig) {
      const errorMessage = `Invalid or inactive merchant number: ${merchantNo}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    this.logger.log(`Merchant validation successful for: ${merchantNo}`);
    this.logger.log(`Associated shop: ${aclasConfig.shop?.id || 'Unknown'}`);

    try {
      // Process ACLAS transaction and create Shopify order
      this.logger.log(`Creating Shopify order for transaction: ${requestId}`);
      const shopifyOrder =
        await this.aclasTransactionService.processAclasTransaction(
          job.data,
          aclasConfig,
        );

      this.logger.log(`Successfully created Shopify order: ${shopifyOrder.id}`);
      this.logger.log(`Order name: ${shopifyOrder.name}`);
      this.logger.log(`Order currency: ${shopifyOrder.currencyCode}`);

      return {
        status: 'completed',
        requestId,
        merchantNo,
        shopId: aclasConfig.shop?.id,
        shopifyOrderId: shopifyOrder.id,
        shopifyOrderName: shopifyOrder.name,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Error processing ACLAS transaction: ${errorMessage}`);
      if (errorStack) {
        this.logger.error(`Error stack: ${errorStack}`);
      }
      throw error;
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(
      `ACLAS transaction job ${job.id} completed successfully. Result: ${JSON.stringify(result)}`,
    );
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(
      `ACLAS transaction job ${job.id} failed (attempt ${job.attemptsMade}/${job.opts.attempts ?? 1}): ${err.message}`,
    );
    this.logger.error(`Job data: ${JSON.stringify(job.data)}`);
    this.logger.error(`Error stack: ${err.stack}`);
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`ACLAS transaction job ${job.id} started processing`);
  }

  @OnWorkerEvent('stalled')
  onStalled(job: Job) {
    this.logger.warn(`ACLAS transaction job ${job.id} stalled`);
  }
}
