import {
  InjectQueue,
  OnWorkerEvent,
  Processor,
  WorkerHost,
} from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { AclasService } from 'src/aclas/services/aclas.service';
import { CustomersService } from 'src/customers/customers.service';
import { CustomerCreatePayload } from 'src/customers/type';
import { QueueName } from '../constant';

@Processor(QueueName.CUSTOMERS_CREATE)
export class CustomerCreateProcessor extends WorkerHost {
  constructor(
    private readonly customerService: CustomersService,
    private readonly aclasService: AclasService,
    @InjectQueue(QueueName.ACLAS_MEMBER_ADD) private aclasMemberAddQueue: Queue,
    @InjectQueue(QueueName.EXAMPLE) private exampleQueue: Queue,
  ) {
    super();
  }

  private readonly logger = new Logger(CustomerCreateProcessor.name);

  async process(
    job: Job<CustomerCreatePayload & { shop: string; accessToken: string }>,
  ): Promise<any> {
    this.logger.log(`Processing customer create job: ${job.id}`);

    /**
     * 1. Check does have aclas config
     * 2. If not, skip
     * 3. If yes, add to aclas queue
     */

    await this.exampleQueue.add(
      `Example job from customer create: ${job.id}`,
      job.data,
      {
        jobId: `example-${job.id}`,
      },
    );

    // Check if shop has Aclas configuration
    const hasAclas = await this.aclasService.hasAclasConfig(job.data.shop);

    if (hasAclas) {
      this.logger.log(
        `Adding customer to Aclas queue for shop: ${job.data.shop}`,
      );
      await this.aclasMemberAddQueue.add(
        `Add customer to Aclas: ${job.id}`,
        job.data,
        {
          jobId: `aclas-member-add-${job.id}`,
        },
      );
    } else {
      this.logger.log(
        `Skipping Aclas integration for shop: ${job.data.shop} (no valid config)`,
      );
    }

    // Always process customer creation regardless of Aclas config
    await this.customerService.handleCustomerCreate(job.data.shop, job.data);

    this.logger.log(`Completed customer create job: ${job.id}`);
    return {
      status: 'completed',
      jobId: job.id,
      aclasProcessed: hasAclas,
    };
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(
      `Job ${job.id} completed with result: ${JSON.stringify(result)}`,
    );
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(
      `Job ${job.id} failed (attempt ${job.attemptsMade}/${job.opts.attempts ?? 1}): ${err.message}`,
    );
  }
}
