import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { QueueName } from '../constant';

@Processor(QueueName.CUSTOMER_TIMELINE)
export class CustomerTimelineProcessor extends WorkerHost {
  async process(job: Job, token?: string): Promise<any> {
    console.log('Processing job:', job.id);
    console.log('Job data:', job.data);
    console.log('Job token:', token);

    // Simulate some work
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log('Job completed:', job.id);
    return { status: 'completed' };
  }
}
