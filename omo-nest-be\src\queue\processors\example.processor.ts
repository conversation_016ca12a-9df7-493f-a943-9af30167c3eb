import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';

@Processor('example', { concurrency: 2 })
export class ExampleProcessor extends WorkerHost {
  private readonly logger = new Logger(ExampleProcessor.name);

  async process(job: Job, token?: string): Promise<any> {
    this.logger.log(`Processing job: ${job.id}`);
    this.logger.log(`Job data: `, job.data);
    this.logger.log(`Job token: ${token}`);

    // Simulate some work
    await new Promise((resolve) => setTimeout(resolve, 10_000));

    this.logger.log(`Job completed: ${job.id}`);
    return { status: 'completed' };
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(
      `Job ${job.id} completed with result: ${JSON.stringify(result)}`,
    );
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(
      `Job ${job.id} failed (attempt ${job.attemptsMade}/${job.opts.attempts ?? 1}): ${err.message}`,
    );
  }
}
