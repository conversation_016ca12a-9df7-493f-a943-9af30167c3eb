import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { AclasModule } from 'src/aclas/aclas.module';
import { CustomersModule } from 'src/customers/customers.module';
import { QueueName } from './constant';
import { AclasMemberAddProcessor } from './processors/aclas-member-add.processor';
import { AclasTransactionsProcessor } from './processors/aclas-transactions.processor';
import { CustomerCreateProcessor } from './processors/customer-create.processor';
import { CustomerTimelineProcessor } from './processors/customer-timeline.processor';
import { ExampleProcessor } from './processors/example.processor';

@Module({
  imports: [
    CustomersModule,
    AclasModule,
    BullModule.registerQueue({
      name: 'example',
    }),
    BullModule.registerQueue({
      name: QueueName.CUSTOMERS_CREATE,
    }),
    BullModule.registerQueue({
      name: QueueName.CUSTOMER_TIMELINE,
    }),
    BullModule.registerQueue({
      name: QueueName.ACLAS_MEMBER_ADD,
    }),
    BullModule.registerQueue({
      name: QueueName.ACLAS_TRANSACTIONS,
    }),
  ],
  providers: [
    ExampleProcessor,
    CustomerCreateProcessor,
    CustomerTimelineProcessor,
    AclasMemberAddProcessor,
    AclasTransactionsProcessor,
  ],
  exports: [BullModule, AclasModule],
})
export class QueueModule {}
