import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { SalesOrders } from './sales-orders.entity';

@Entity('ReturnOrders')
export class ReturnOrders extends BaseEntityWithTimestamps {
  @Column({ unique: true })
  orderId: string;

  @Column({ unique: true })
  retOrdShopId: string;

  @ManyToOne(() => SalesOrders, (salesOrder) => salesOrder.returnOrders)
  @JoinColumn({ name: 'salesOrdId' })
  salesOrder: SalesOrders;

  @Column()
  salesOrdId: number;
}
