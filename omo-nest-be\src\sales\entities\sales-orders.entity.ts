import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from '../../shop/entities/shop.entity';
import { ReturnOrders } from './return-orders.entity';

@Entity('SalesOrders')
export class SalesOrders extends BaseEntityWithTimestamps {
  @Column({ unique: true })
  @Index()
  orderPosId: string;

  @Column({ unique: true })
  ordShopiId: string;

  @ManyToOne(() => Shop, (shop) => shop.salesOrders, { onDelete: 'CASCADE' })
  shop: Shop;

  @Column()
  shopId: number;

  @OneToMany(() => ReturnOrders, (returnOrder) => returnOrder.salesOrder, {
    cascade: true,
  })
  returnOrders: ReturnOrders[];
}
