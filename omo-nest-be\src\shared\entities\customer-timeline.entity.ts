import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Shop } from '../../shop/entities/shop.entity';
import { BaseEntityWithTimestamps } from './base.entity';

export enum TimelineEventType {
  POINTS = 'POINTS',
  DISCOUNT = 'DISCOUNT',
  ORDER = 'ORDER',
  OTHER = 'OTHER',
}

@Entity('CustomerTimeline')
export class CustomerTimeline extends BaseEntityWithTimestamps {
  @ManyToOne(() => Shop, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shopId' })
  shop: Shop;

  @Column()
  shopId: number;

  @Column()
  @Index()
  customerId: string;

  @Column({
    type: 'enum',
    enum: TimelineEventType,
    default: TimelineEventType.OTHER,
  })
  type: TimelineEventType;

  @Column({ type: 'json', nullable: true })
  metafields?: Record<string, any>;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  date: Date;
}
