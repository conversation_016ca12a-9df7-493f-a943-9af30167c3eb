import { Column, Entity, Index, PrimaryColumn } from 'typeorm';

@Entity('Session')
export class Session {
  @PrimaryColumn()
  id: string;

  @Column()
  @Index()
  shop: string;

  @Column()
  state: string;

  @Column({ default: false })
  isOnline: boolean;
  @Column({ type: 'text', nullable: true })
  scope?: string;

  @Column({ nullable: true })
  expires?: Date;

  @Column()
  accessToken: string;

  @Column({ type: 'bigint', nullable: true })
  userId?: number;

  @Column({ nullable: true })
  firstName?: string;

  @Column({ nullable: true })
  lastName?: string;

  @Column({ nullable: true })
  email?: string;

  @Column({ default: false })
  accountOwner: boolean;

  @Column({ nullable: true })
  locale?: string;

  @Column({ nullable: true })
  collaborator?: boolean;

  @Column({ nullable: true })
  emailVerified?: boolean;
}
