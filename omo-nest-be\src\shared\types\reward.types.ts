/**
 * Shared reward types that can be used across the application
 */

export type UnifiedRewardType =
  | 'POINTS'
  | 'DISCOUNT'
  | 'GIFT'
  | 'FREE_SHIPPING'
  | 'STORE_CREDIT'
  | 'AMOUNT_OFF';

// Type guard to check if a value is a valid reward type
export function isRewardType(value: string): value is UnifiedRewardType {
  return [
    'POINTS',
    'DISCOUNT',
    'GIFT',
    'FREE_SHIPPING',
    'STORE_CREDIT',
    'AMOUNT_OFF',
  ].includes(value);
}

// Type for reward conversion
export type RewardTypeMap<T> = {
  [K in UnifiedRewardType]?: T;
};
