import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { Shop } from './shop.entity';

@Entity('AclasPosConfig')
export class AclasPosConfig extends BaseEntityWithTimestamps {
  @OneToOne(() => Shop, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shopId' })
  shop: Shop;

  @Column({ unique: true })
  shopId: number;

  @Column({ nullable: true })
  appSecret?: string;

  @Column({ nullable: true })
  appId?: string;

  @Column({ nullable: true })
  merchantNo?: string;

  @Column({ default: false })
  isActive: boolean;
}
