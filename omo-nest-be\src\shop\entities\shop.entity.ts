import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { BirthdayProgramSettings } from '../../loyalty/entities/birthday-program-settings.entity';
import { CompleteProfileSettings } from '../../loyalty/entities/complete-profile-settings.entity';
import { LoyaltyProgram } from '../../loyalty/entities/loyalty-program.entity';
import { ShopCreditLoyaltyProgram } from '../../loyalty/entities/shop-credit-loyalty-program.entity';
import { WaysEarnReward } from '../../loyalty/entities/ways-earn-reward.entity';
import { SalesOrders } from '../../sales/entities/sales-orders.entity';
import { BaseEntityWithTimestamps } from '../../shared/entities/base.entity';
import { CustomerTimeline } from '../../shared/entities/customer-timeline.entity';
import { AclasPosConfig } from './aclas-pos-config.entity';

@Entity('Shop')
export class Shop extends BaseEntityWithTimestamps {
  @Column({ unique: true })
  shopId: string;

  @Column()
  shopName: string;

  @Column({ unique: true })
  myshopifyDomain: string;

  @Column({ nullable: true })
  shopToken?: string;

  @Column({ nullable: true })
  apiKey?: string;

  @OneToOne(() => AclasPosConfig, (config) => config.shop, { cascade: true })
  aclasPosConfig?: AclasPosConfig;

  @OneToMany(() => LoyaltyProgram, (program) => program.shop)
  loyaltyPrograms: LoyaltyProgram[];

  @OneToOne(() => ShopCreditLoyaltyProgram, (program) => program.shop, {
    cascade: true,
  })
  shopLoyaltyProgram?: ShopCreditLoyaltyProgram;

  @OneToOne(() => BirthdayProgramSettings, (settings) => settings.shop, {
    cascade: true,
  })
  birthdaySettings?: BirthdayProgramSettings;

  @OneToMany(() => WaysEarnReward, (reward) => reward.shop)
  WaysEarnReward: WaysEarnReward[];

  @OneToOne(() => CompleteProfileSettings, (settings) => settings.shop, {
    cascade: true,
  })
  completeProfileSettings?: CompleteProfileSettings;

  @OneToMany(() => CustomerTimeline, (timeline) => timeline.shop)
  CustomerTimeline: CustomerTimeline[];

  @OneToMany(() => SalesOrders, (order) => order.shop)
  salesOrders: SalesOrders[];

  @OneToMany(() => CustomerTimeline, (timeline) => timeline.shop)
  customerTimelines: CustomerTimeline[];
}
