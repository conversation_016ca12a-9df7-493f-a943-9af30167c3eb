import {
  ApolloClient,
  InMemoryCache,
  NormalizedCacheObject,
} from '@apollo/client/core';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Shop } from './entities/shop.entity';

@Injectable()
export class ShopifyService {
  private readonly logger = new Logger(ShopifyService.name);

  constructor(
    @InjectRepository(Shop)
    private readonly shopRepository: Repository<Shop>,
  ) {}

  /**
   * Find a shop by its Shopify domain
   * @param domain The Shopify domain to search for
   * @returns The shop if found, null otherwise
   */
  async findShopByDomain(domain: string): Promise<Shop | null> {
    try {
      return await this.shopRepository.findOne({
        where: { myshopifyDomain: domain },
      });
    } catch (error) {
      this.logger.error(`Error finding shop by domain ${domain}: ${error}`);
      throw error;
    }
  }

  /**
   * Get the admin client for a shop
   * @param shop The shop entity
   * @returns An object with the admin client and session
   */
  getAdminClient(shop: Shop | { myshopifyDomain: string; shopToken: string }): {
    client: ApolloClient<NormalizedCacheObject>;
    session: any;
  } {
    const { myshopifyDomain, shopToken } = shop;
    const client = new ApolloClient({
      uri: `https://${myshopifyDomain}/admin/api/2025-04/graphql.json`,
      cache: new InMemoryCache(),
      headers: {
        'X-Shopify-Access-Token': shopToken || '',
      },
    });

    return {
      client,
      session: {
        shop: myshopifyDomain,
        accessToken: shopToken,
      },
    };
  }
}
