# Dependencies
node_modules
npm-debug.log*

# Production build
build/
dist/

# Development files
.env*
!.env.example

# Git
.git
.gitignore

# Docker
docker-compose*.yml
Dockerfile*
.dockerignore

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Next.js
.next

# Nuxt.js
.nuxt

# Testing
.coverage
.mocha

# Documentation
README.md
DOCKER_README.md
CHANGELOG.md
docs/

# Secrets and sensitive files
secrets/
*.key
*.pem
*.crt

# Backup files
*.bak
*.backup
backup/

# Temporary files
tmp/
temp/

# Lock files (will be copied separately in Dockerfile)
yarn.lock

# Static analysis
.eslintcache
