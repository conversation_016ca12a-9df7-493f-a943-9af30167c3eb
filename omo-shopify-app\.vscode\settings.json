{
  "editor.rulers": [
    80
  ],
  "editor.tabCompletion": "on",
  "editor.tabSize": 2,
  "editor.trimAutoWhitespace": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "files.exclude": {
    ".idea": true,
    "**/.DS_Store": true,
    "**/.git": true,
    "**/.hg": true,
    "**/.svn": true,
    "**/CVS": true,
    "**/node_modules": true,
    "build": true,
    "dist": true
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "files.eol": "\n",
  "typescript.tsdk": "./node_modules/typescript/lib",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false,
  "typescript.preferences.quoteStyle": "single",
  "eslint.run": "onSave",
  "eslint.nodePath": "./node_modules",
  "eslint.validate": [
    "javascript",
    "typescript"
  ],
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  },
  "i18n-ally.localesPaths": [
    "public/locales",
    // "extensions/*/locales"
  ],
  "i18n-ally.keystyle": "nested",
  // "i18n-ally.includeSubfolders": true,
  // "i18n-ally.pathMatcher": "*.json",
  "[liquid]": {
    "editor.defaultFormatter": "Shopify.theme-check-vscode"
  },
  "[toml]": {
    "editor.defaultFormatter": "tamasfe.even-better-toml"
  },
}
