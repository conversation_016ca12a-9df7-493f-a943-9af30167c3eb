FROM node:18-alpine as base

# Install system dependencies
RUN apk add --no-cache \
    openssl \
    curl \
    wget \
    dumb-init

# Set working directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S remix -u 1001

FROM base as dependencies

# Copy package files
COPY package.json package-lock.json* ./

# Install production dependencies
RUN npm ci --omit=dev --frozen-lockfile && npm cache clean --force

FROM base as build

# Copy package files
COPY package.json package-lock.json* ./

# Install all dependencies for building
RUN npm ci --frozen-lockfile

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

FROM base as production

# Set production environment
ENV NODE_ENV=production

# Copy production dependencies
COPY --from=dependencies --chown=remix:nodejs /app/node_modules ./node_modules

# Copy built application
COPY --from=build --chown=remix:nodejs /app/build ./build
COPY --from=build --chown=remix:nodejs /app/public ./public
COPY --from=build --chown=remix:nodejs /app/package.json ./package.json
COPY --from=build --chown=remix:nodejs /app/prisma ./prisma

# Copy entrypoint script
COPY --chown=remix:nodejs docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Expose port
EXPOSE 3000

# Switch to non-root user
USER remix

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ping || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

CMD ["./docker-entrypoint.sh"]
