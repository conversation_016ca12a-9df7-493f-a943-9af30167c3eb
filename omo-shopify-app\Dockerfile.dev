FROM node:18-alpine as base

# Install system dependencies
RUN apk add --no-cache \
    openssl \
    git \
    curl \
    wget

# Set working directory
WORKDIR /app

# Install global dependencies for development
RUN npm install -g @shopify/cli prisma

FROM base as development

# Set development environment
ENV NODE_ENV=development

# Copy package files
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies)
RUN npm ci --include=dev

# Copy application code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Expose ports for the app and HMR
EXPOSE 3000 64999

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S remix -u 1001

# Change ownership of the app directory
RUN chown -R remix:nodejs /app
USER remix

# Start development server with hot reload
CMD ["npm", "run", "dev"]
