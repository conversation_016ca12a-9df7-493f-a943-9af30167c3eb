import { BlockStack, Card, DatePicker, TextField } from "@shopify/polaris";
import dayjs from "dayjs";
import { useCallback, useState } from "react";

interface DateRange {
  start: Date;
  end: Date;
}

interface DatePickingProps {
  onChange?: (value: DateRange) => void;
  value?: Date;
  setValue: (value: Date) => void;
  label?: string;
}

export const DatePicking = ({ onChange, value, setValue, label = "Date" }: DatePickingProps) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [month, setMonth] = useState(value?.getMonth() || new Date().getMonth());
  const [year, setYear] = useState(value?.getFullYear() || new Date().getFullYear());
  const [textValue, setTextValue] = useState(value ? dayjs(value).format("YYYY-MM-DD") : "");

  const onDatePickerChange = useCallback(
    (dateRange: DateRange) => {
      const date = dayjs(dateRange.start);

      setTextValue(date.format("YYYY-MM-DD"));
      setMonth(date.month());
      setYear(date.year());
      setShowDatePicker(false);
      setValue(date.toDate());
      onChange?.(dateRange);
    },
    [onChange, setValue],
  );

  return (
    <BlockStack gap="200">
      <TextField
        label={label}
        value={textValue}
        autoComplete="off"
        onFocus={() => setShowDatePicker(true)}
      />

      {showDatePicker && (
        <Card>
          <DatePicker
            month={month}
            year={year}
            onChange={onDatePickerChange}
            selected={{ start: value || new Date(), end: value || new Date() }}
            onMonthChange={(newMonth, newYear) => {
              setMonth(newMonth);
              setYear(newYear);
            }}
          />
        </Card>
      )}
    </BlockStack>
  );
};
