import { BlockStack, InlineStack, Text } from "@shopify/polaris";
import React from "react";

export interface IconTextProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
}

const IconText: React.FC<IconTextProps> = ({ icon, title, subtitle }) => (
  // Wrapper div for padding
  <div>
    <InlineStack as="div" align="space-around" blockAlign="center" gap="400">
      {icon}
      <BlockStack as="div" inlineAlign="baseline">
        <Text as="span" fontWeight="bold">
          {title}
        </Text>
        {subtitle && (
          <Text as="span" tone="subdued">
            {subtitle}
          </Text>
        )}
      </BlockStack>
    </InlineStack>
  </div>
);

export default IconText;
