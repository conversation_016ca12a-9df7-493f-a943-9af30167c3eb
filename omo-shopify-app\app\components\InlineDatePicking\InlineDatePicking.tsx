import {
  BlockStack,
  Box,
  Card,
  DatePicker,
  Icon,
  Popover,
  PopoverCloseSource,
  TextField,
} from "@shopify/polaris";
import { CalendarIcon } from "@shopify/polaris-icons";
import { ReactNode, useEffect, useState } from "react";

interface InlineDatePickingProps {
  /** Label for the date input */
  label: string;
  /** Currently selected date */
  selected: Date;
  /** Callback invoked when a new date is selected */
  onSelect: (date: Date) => void;
  /** Optional children to render alongside the input */
  children?: ReactNode;
}

export const InlineDatePicking = ({
  label,
  selected,
  onSelect,
  children,
}: InlineDatePickingProps) => {
  // Popover visibility state
  const [popoverActive, setPopoverActive] = useState(false);
  // Month and year displayed in the calendar
  const [{ month, year }, setMonthYear] = useState({
    month: selected.getMonth(),
    year: selected.getFullYear(),
  });

  // Format date as YYYY-MM-DD for the input value
  const formattedValue = selected.toISOString().slice(0, 10);

  // Show the calendar popover
  const handleFocus = () => setPopoverActive(true);

  // Handle manual input changes (optional parsing logic)
  const handleInputChange = (value: string) => {
    // You could parse value here
  };

  // Close the popover when clicking outside
  const handleClose = (source: PopoverCloseSource) => {
    setPopoverActive(false);
  };

  // Update displayed month/year when the calendar navigates
  const handleMonthChange = (newMonth: number, newYear: number) => {
    setMonthYear({ month: newMonth, year: newYear });
  };

  // When a date is selected, update state and notify parent
  const handleDateSelection = ({ end: date }: { end: Date }) => {
    onSelect(date);
    setPopoverActive(false);
  };

  // Sync calendar display when selected date changes externally
  useEffect(() => {
    setMonthYear({
      month: selected.getMonth(),
      year: selected.getFullYear(),
    });
  }, [selected]);

  return (
    <BlockStack inlineAlign="center">
      <Box minWidth="276px" padding={{ xs: "200" }}>
        <Popover
          active={popoverActive}
          autofocusTarget="none"
          preferredAlignment="left"
          fullWidth
          preferInputActivator={false}
          preferredPosition="below"
          preventCloseOnChildOverlayClick
          onClose={handleClose}
          activator={
            <TextField
              role="combobox"
              label={label}
              prefix={<Icon source={CalendarIcon} />}
              value={formattedValue}
              onFocus={handleFocus}
              onChange={handleInputChange}
              autoComplete="off"
            />
          }
        >
          <Card>
            <DatePicker
              month={month}
              year={year}
              selected={{ start: selected, end: selected }}
              onMonthChange={handleMonthChange}
              onChange={handleDateSelection}
            />
          </Card>
        </Popover>
      </Box>
      {children}
    </BlockStack>
  );
};
