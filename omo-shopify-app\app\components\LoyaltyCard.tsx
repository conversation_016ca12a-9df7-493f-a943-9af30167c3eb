import { Link } from "@remix-run/react";
import { ChevronRight } from "lucide-react";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

interface LoyaltyCardProps {
  /**
   * The icon to display in the card
   */
  icon: ReactNode;

  /**
   * The title of the loyalty feature
   */
  title: string;

  /**
   * The status of the loyalty feature (Active, Inactive, etc.)
   */
  status: "Active" | "Inactive";

  /**
   * The URL to navigate to when clicked
   * If not provided, the card won't be wrapped in a Link
   */
  to?: string;

  disabled?: boolean;
}

/**
 * A card component for displaying loyalty program features with status badges
 */
export function LoyaltyCard({ icon, title, status, to, disabled }: Readonly<LoyaltyCardProps>) {
  const { t } = useTranslation();

  const content = (
    <div
      className={`border border-gray-200 rounded-lg p-4 flex items-center justify-between ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
    >
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 flex items-center justify-center text-gray-600">{icon}</div>
        <div className="flex items-center gap-2">
          <span className="text-lg font-medium">{title}</span>
          <span
            className={`px-2 py-0.5 text-xs rounded-full ${
              status === "Active" ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-700"
            }`}
          >
            {t(status === "Active" ? "loyalty.status.active" : "loyalty.status.inactive")}
          </span>
        </div>
      </div>
      <ChevronRight className="w-5 h-5 text-gray-400" />
    </div>
  );

  if (to) {
    return disabled ? content : <Link to={to}>{content}</Link>;
  }

  return content;
}
