import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Button,
  ButtonGroup,
  Checkbox,
  InlineGrid,
  RadioButton,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  AmountOffOrderRewardInterface,
  DISCOUNT_TYPES,
  REQUIREMENT_TYPES,
  REWARD_TYPES,
} from "./interface";

interface AddAmountOffOrderRewardModalProps {
  /**
   * Modal mode - "add" for creating new rewards, "edit" for updating existing ones
   */
  isEditMode?: boolean;

  /**
   * The reward to edit (required in edit mode)
   */
  existingReward?: AmountOffOrderRewardInterface;

  /**
   * Callback function when the reward is saved
   */
  onSave?: (reward: AmountOffOrderRewardInterface) => void;

  /**
   * Callback function when the modal is closed
   */
  onClose?: () => void;
}

export default function AddAmountOffOrderRewardModal({
  isEditMode = false,
  existingReward,
  onSave,
  onClose,
}: Readonly<AddAmountOffOrderRewardModalProps>) {
  const { t } = useTranslation();

  // Initialize with empty values for add mode, or existing values for edit mode
  const [title, setTitle] = useState(isEditMode && existingReward ? existingReward.title : "");
  const [value, setValue] = useState(isEditMode && existingReward ? existingReward.value : "");
  const [discountType, setDiscountType] = useState<
    typeof DISCOUNT_TYPES.PERCENTAGE | typeof DISCOUNT_TYPES.FIXED
  >(isEditMode && existingReward ? existingReward.discountType : DISCOUNT_TYPES.PERCENTAGE);
  const [minimumRequirement, setMinimumRequirement] = useState<
    | typeof REQUIREMENT_TYPES.NONE
    | typeof REQUIREMENT_TYPES.AMOUNT
    | typeof REQUIREMENT_TYPES.QUANTITY
  >(isEditMode && existingReward ? existingReward.minimumRequirement : REQUIREMENT_TYPES.NONE);
  const [minimumValue, setMinimumValue] = useState(
    isEditMode && existingReward && existingReward.minimumValue ? existingReward.minimumValue : "",
  );
  const [productDiscounts, setProductDiscounts] = useState(
    isEditMode && existingReward ? existingReward.combinations?.productDiscounts : false,
  );
  const [orderDiscounts, setOrderDiscounts] = useState(
    isEditMode && existingReward ? existingReward.combinations?.orderDiscounts : false,
  );
  const [shippingDiscounts, setShippingDiscounts] = useState(
    isEditMode && existingReward ? existingReward.combinations?.shippingDiscounts : false,
  );
  const [titleError, setTitleError] = useState("");
  const [valueError, setValueError] = useState("");

  // Update state when reward changes in edit mode
  useEffect(() => {
    if (isEditMode && existingReward) {
      setTitle(existingReward.title);
      setValue(existingReward.value);
      setDiscountType(existingReward.discountType);
      setMinimumRequirement(existingReward.minimumRequirement);
      setMinimumValue(existingReward.minimumValue || "");
      setProductDiscounts(existingReward.combinations?.productDiscounts);
      setOrderDiscounts(existingReward.combinations?.orderDiscounts);
      setShippingDiscounts(existingReward.combinations?.shippingDiscounts);
      // Reset errors when reward changes
      setTitleError("");
      setValueError("");
    }
  }, [isEditMode, existingReward]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTitleError("");
    setValueError("");

    // Validate title
    if (!title.trim()) {
      setTitleError(t("loyalties.rewards.validation.titleRequired"));
      isValid = false;
    }

    // Validate value
    if (!value.trim()) {
      setValueError(t("loyalties.rewards.validation.valueRequired"));
      isValid = false;
    } else if (isNaN(Number(value)) || Number(value) <= 0) {
      setValueError(t("loyalties.rewards.validation.valueInvalid"));
      isValid = false;
    }

    return isValid;
  }, [title, value, t]);

  const handleSave = useCallback(() => {
    if (validateForm() && onSave) {
      if (isEditMode && existingReward) {
        // Update existing reward
        const updatedReward: AmountOffOrderRewardInterface = {
          ...existingReward,
          title,
          value,
          discountType,
          minimumRequirement,
          minimumValue: minimumRequirement !== REQUIREMENT_TYPES.NONE ? minimumValue : undefined,
          combinations: {
            productDiscounts,
            orderDiscounts,
            shippingDiscounts,
          },
        };
        onSave(updatedReward);
      } else {
        // Create new reward
        const newReward: AmountOffOrderRewardInterface = {
          id: Date.now().toString(),
          type: REWARD_TYPES.AMOUNT_OFF,
          title,
          value,
          discountType,
          minimumRequirement,
          minimumValue: minimumRequirement !== REQUIREMENT_TYPES.NONE ? minimumValue : undefined,
          combinations: {
            productDiscounts,
            orderDiscounts,
            shippingDiscounts,
          },
        };
        onSave(newReward);
      }
    }
  }, [
    validateForm,
    onSave,
    isEditMode,
    existingReward,
    title,
    value,
    discountType,
    minimumRequirement,
    minimumValue,
    productDiscounts,
    orderDiscounts,
    shippingDiscounts,
  ]);
  // Determine modal ID based on mode
  const modalId = isEditMode
    ? "edit-amount-off-order-reward-modal"
    : "add-amount-off-order-reward-modal";

  // Determine modal title based on mode
  const modalTitle = isEditMode
    ? t("loyalties.rewards.editReward")
    : t("loyalties.rewards.addNewReward");

  // Determine heading based on mode
  const heading = isEditMode
    ? t("loyalties.rewards.editAmountOffOrderReward")
    : t("loyalties.rewards.amountOffOrderReward");

  return (
    <Modal id={modalId}>
      <div className="m-3">
        <BlockStack gap="400">
          <Text variant="headingMd" as="h2">
            {heading}
          </Text>
          <BlockStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitle")}
            </Text>

            <TextField
              label={t("loyalties.rewards.rewardTitle")}
              value={title}
              type="text"
              onChange={setTitle}
              autoComplete="off"
              labelHidden
              error={titleError}
            />
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitleDescription")}
            </Text>
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="h2">
              {t("loyalties.rewards.rewardValue")}
            </Text>

            <InlineGrid gap="400" columns={2}>
              <ButtonGroup fullWidth>
                <Button
                  pressed={discountType === DISCOUNT_TYPES.PERCENTAGE}
                  onClick={() => setDiscountType(DISCOUNT_TYPES.PERCENTAGE)}
                >
                  {t("loyalties.rewards.percentage")}
                </Button>
                <Button
                  pressed={discountType === DISCOUNT_TYPES.FIXED}
                  onClick={() => setDiscountType(DISCOUNT_TYPES.FIXED)}
                >
                  {t("loyalties.rewards.fixedAmount")}
                </Button>
              </ButtonGroup>

              <TextField
                label={t("loyalties.rewards.rewardValue")}
                type="number"
                autoComplete="off"
                labelHidden
                value={value}
                onChange={setValue}
                error={valueError}
              />
            </InlineGrid>
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="h2">
              {t("loyalties.rewards.minimumRequirements")}
            </Text>

            <RadioButton
              label={t("loyalties.rewards.noMinimumRequirements")}
              checked={minimumRequirement === REQUIREMENT_TYPES.NONE}
              onChange={() => setMinimumRequirement(REQUIREMENT_TYPES.NONE)}
            />

            <RadioButton
              label={t("loyalties.rewards.minimumPurchaseAmount")}
              checked={minimumRequirement === REQUIREMENT_TYPES.AMOUNT}
              onChange={() => setMinimumRequirement(REQUIREMENT_TYPES.AMOUNT)}
            />

            <RadioButton
              label={t("loyalties.rewards.minimumQuantityItems")}
              checked={minimumRequirement === REQUIREMENT_TYPES.QUANTITY}
              onChange={() => setMinimumRequirement(REQUIREMENT_TYPES.QUANTITY)}
            />

            {minimumRequirement !== REQUIREMENT_TYPES.NONE && (
              <TextField
                label={
                  minimumRequirement === REQUIREMENT_TYPES.AMOUNT
                    ? t("loyalties.rewards.minimumAmount")
                    : t("loyalties.rewards.minimumQuantity")
                }
                type="number"
                value={minimumValue}
                onChange={setMinimumValue}
                autoComplete="off"
              />
            )}
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="h2">
              {t("loyalties.rewards.combinations")}
            </Text>
            <Checkbox
              label={t("loyalties.rewards.productDiscounts")}
              checked={productDiscounts}
              onChange={setProductDiscounts}
            />
            <Checkbox
              label={t("loyalties.rewards.orderDiscounts")}
              checked={orderDiscounts}
              onChange={setOrderDiscounts}
            />
            <Checkbox
              label={t("loyalties.rewards.shippingDiscounts")}
              checked={shippingDiscounts}
              onChange={setShippingDiscounts}
            />
          </BlockStack>
        </BlockStack>
      </div>

      <TitleBar title={modalTitle}>
        <button onClick={handleSave} variant={"primary"}>
          {t("common.save")}
        </button>
        <button onClick={onClose}>{t("common.cancel")}</button>
      </TitleBar>
    </Modal>
  );
}
