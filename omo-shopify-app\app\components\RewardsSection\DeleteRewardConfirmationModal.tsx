import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { Text } from "@shopify/polaris";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { MODAL_IDS } from "./interface";

interface DeleteRewardConfirmationModalProps {
  rewardId?: string;
  rewardTitle?: string;
  onConfirm: (rewardId?: string) => void;
  onCancel: () => void;
}

export default function DeleteRewardConfirmationModal({
  rewardId,
  rewardTitle,
  onConfirm,
  onCancel,
}: Readonly<DeleteRewardConfirmationModalProps>) {
  const { t } = useTranslation();

  const handleConfirm = useCallback(() => {
    onConfirm(rewardId);
  }, [onConfirm, rewardId]);

  return (
    <Modal id={MODAL_IDS.DELETE_REWARD_CONFIRMATION}>
      <div className="m-3">
        <Text variant="bodyMd" as="p">
          {t("loyalties.rewards.deleteConfirmation.message", { rewardTitle })}
        </Text>
      </div>
      <TitleBar title={t("loyalties.rewards.deleteConfirmation.title")}>
        <button onClick={onCancel}>{t("common.cancel")}</button>
        <button onClick={handleConfirm}>{t("common.delete")}</button>
      </TitleBar>
    </Modal>
  );
}
