import { useShopifyToast } from "@/hooks/useShopifyToast";
import { useAppBridge } from "@shopify/app-bridge-react";
import { BlockStack, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import AddAmountOffOrderRewardModal from "./AddAmountOffOrderRewardModal";
import AddFreeShippingRewardModal from "./AddFreeShippingRewardModal";
import AddRewardModal from "./AddRewardModal";
import AddStoreCreditRewardModal from "./AddStoreCreditRewardModal";
import DeleteRewardConfirmationModal from "./DeleteRewardConfirmationModal";
import {
  AmountOffOrderRewardInterface,
  FreeShippingRewardInterface,
  MODAL_IDS,
  PointRewardInterface,
  REWARD_TYPES,
  RewardTypeInterface,
  StoreCreditRewardInterface,
} from "./interface";
import PointRewardModal from "./PointRewardModal";
import RewardCard from "./RewardCard";

interface RewardsSectionProps {
  isSubmitting: boolean;
  onRewardsChange: (rewards: RewardTypeInterface[]) => void;
  initialRewards?: RewardTypeInterface[];
  titleText: string;
  addNewText: string;
  descriptionText: string;
}

export default function RewardsSection({
  isSubmitting,
  onRewardsChange,
  initialRewards = [],
  titleText,
  addNewText,
  descriptionText,
}: Readonly<RewardsSectionProps>) {
  const { t } = useTranslation();
  const shopify = useAppBridge();
  const { showErrorToast, showSuccessToast } = useShopifyToast();

  // State for rewards
  const [rewards, setRewards] = useState<RewardTypeInterface[]>(initialRewards);

  // State for edit and delete modals
  const [selectedReward, setSelectedReward] = useState<RewardTypeInterface | null>(null);
  const [rewardToDelete, setRewardToDelete] = useState<{ id: string; title: string } | null>(null);

  // Update parent component when rewards change
  const updateRewards = useCallback(
    (newRewards: RewardTypeInterface[]) => {
      setRewards(newRewards);
      onRewardsChange(newRewards);
    },
    [onRewardsChange],
  );

  // Define callback functions for modal operations
  const handleOpenModal = useCallback(() => {
    shopify?.modal?.show(MODAL_IDS.ADD_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCloseModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAddReward = useCallback(
    (value: string) => {
      handleCloseModal();
      switch (value) {
        case REWARD_TYPES.POINTS:
          shopify?.modal?.show(MODAL_IDS.ADD_POINT_REWARD);
          break;
        case REWARD_TYPES.STORE_CREDIT:
          shopify?.modal?.show(MODAL_IDS.ADD_STORE_CREDIT_REWARD);
          break;
        case REWARD_TYPES.AMOUNT_OFF:
          shopify?.modal?.show(MODAL_IDS.ADD_AMOUNT_OFF_ORDER_REWARD);
          break;
        case REWARD_TYPES.FREE_SHIPPING:
          shopify?.modal?.show(MODAL_IDS.ADD_FREE_SHIPPING_REWARD);
          break;
        default:
          // Show toast for reward types that are not yet implemented
          showErrorToast(t("loyalties.vip.toastMessages.rewardNotImplemented", { type: value }));
          break;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleCloseModal, t],
  );

  // Handler functions for managing rewards
  const handleAddPointReward = useCallback(
    (reward: PointRewardInterface) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);

      // Show success toast when reward is added
      shopify?.toast.show(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }), {
        isError: false,
        duration: 3000,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleClosePointRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Edit reward handlers
  const handleEditReward = useCallback(
    (reward: RewardTypeInterface) => {
      setSelectedReward(reward);

      switch (reward.type) {
        case REWARD_TYPES.POINTS:
          shopify?.modal?.show(MODAL_IDS.EDIT_POINT_REWARD);
          break;
        case REWARD_TYPES.STORE_CREDIT:
          shopify?.modal?.show(MODAL_IDS.EDIT_STORE_CREDIT_REWARD);
          break;
        case REWARD_TYPES.AMOUNT_OFF:
          shopify?.modal?.show(MODAL_IDS.EDIT_AMOUNT_OFF_ORDER_REWARD);
          break;
        case REWARD_TYPES.FREE_SHIPPING:
          shopify?.modal?.show(MODAL_IDS.EDIT_FREE_SHIPPING_REWARD);
          break;
        default:
          // Show toast for reward types that are not yet implemented
          showErrorToast(
            t("loyalties.vip.toastMessages.rewardEditNotImplemented", {
              type: (reward as any).type,
            }),
          );
          break;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const handleUpdatePointReward = useCallback(
    (updatedReward: PointRewardInterface) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.EDIT_POINT_REWARD);
      setSelectedReward(null);

      // Show success toast when reward is updated
      shopify?.toast.show(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
        {
          isError: false,
          duration: 3000,
        },
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditPointRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_POINT_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Store Credit reward update handlers
  const handleUpdateStoreCreditReward = useCallback(
    (updatedReward: StoreCreditRewardInterface) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.EDIT_STORE_CREDIT_REWARD);
      setSelectedReward(null);

      // Show success toast when reward is updated
      showSuccessToast(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditStoreCreditRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_STORE_CREDIT_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Amount Off Order reward update handlers
  const handleUpdateAmountOffOrderReward = useCallback(
    (updatedReward: AmountOffOrderRewardInterface) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.EDIT_AMOUNT_OFF_ORDER_REWARD);
      setSelectedReward(null);

      console.log("Updated reward:", updatedReward);

      // Show success toast when reward is updated
      showSuccessToast(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditAmountOffOrderRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_AMOUNT_OFF_ORDER_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Free Shipping reward update handlers
  const handleUpdateFreeShippingReward = useCallback(
    (updatedReward: FreeShippingRewardInterface) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.EDIT_FREE_SHIPPING_REWARD);
      setSelectedReward(null);

      // Show success toast when reward is updated
      showSuccessToast(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditFreeShippingRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_FREE_SHIPPING_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Delete reward handlers
  const handleDeleteReward = useCallback(
    (rewardId: string) => {
      const rewardToDelete = rewards.find((r) => r.id === rewardId);
      if (rewardToDelete) {
        setRewardToDelete({ id: rewardId, title: rewardToDelete.title });
        shopify?.modal?.show(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards],
  );

  const handleConfirmDelete = useCallback(
    (rewardId?: string) => {
      // Store the title before removing the reward
      const rewardTitle = rewardToDelete?.title ?? t("loyalties.vip.newTier.rewards.title");

      const newRewards = rewards.filter((reward) => reward.id !== rewardId);
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
      setRewardToDelete(null);

      // Show success toast when reward is deleted
      showSuccessToast(t("loyalties.vip.toastMessages.rewardDeleted", { title: rewardTitle }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewardToDelete, rewards, t, updateRewards],
  );

  const handleCancelDelete = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
    setRewardToDelete(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Store Credit reward handlers
  const handleAddStoreCreditReward = useCallback(
    (reward: StoreCreditRewardInterface) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.ADD_STORE_CREDIT_REWARD);

      // Show success toast when reward is added
      showSuccessToast(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseStoreCreditRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_STORE_CREDIT_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Amount Off Order reward handlers
  const handleAddAmountOffOrderReward = useCallback(
    (reward: AmountOffOrderRewardInterface) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.ADD_AMOUNT_OFF_ORDER_REWARD);

      // Show success toast when reward is added
      showSuccessToast(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseAmountOffOrderRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_AMOUNT_OFF_ORDER_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Free Shipping reward handlers
  const handleAddFreeShippingReward = useCallback(
    (reward: FreeShippingRewardInterface) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.ADD_FREE_SHIPPING_REWARD);

      // Show success toast when reward is added
      showSuccessToast(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseFreeShippingRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_FREE_SHIPPING_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Card>
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h2">
              {titleText}
            </Text>
            <Button onClick={handleOpenModal} disabled={isSubmitting}>
              {addNewText}
            </Button>
          </InlineStack>

          <Text as="p" variant="bodyMd">
            {descriptionText}
          </Text>

          {rewards.length > 0 && (
            <BlockStack gap="300">
              <Text variant="headingMd" as="h3">
                {t("loyalties.vip.newTier.rewards.addedRewards")}
              </Text>
              {rewards.map((reward) => (
                <RewardCard
                  key={reward.id}
                  reward={reward}
                  onEdit={handleEditReward}
                  onDelete={handleDeleteReward}
                />
              ))}
            </BlockStack>
          )}
        </BlockStack>
      </Card>

      {/* Add Reward Modals */}
      <AddRewardModal
        id={MODAL_IDS.ADD_REWARD}
        onSubmit={handleAddReward}
        onClose={handleCloseModal}
        existingRewards={rewards}
      />
      <PointRewardModal
        mode="add"
        onSave={handleAddPointReward}
        onClose={handleClosePointRewardModal}
      />

      <PointRewardModal
        mode="edit"
        reward={selectedReward as PointRewardInterface}
        onSave={handleUpdatePointReward}
        onClose={handleCloseEditPointRewardModal}
      />

      <DeleteRewardConfirmationModal
        rewardId={rewardToDelete?.id}
        rewardTitle={rewardToDelete?.title}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />

      {/* Store Credit Reward Modals */}
      <AddStoreCreditRewardModal
        isEditMode={false}
        onSave={handleAddStoreCreditReward}
        onClose={handleCloseStoreCreditRewardModal}
      />
      <AddStoreCreditRewardModal
        isEditMode={true}
        existingReward={selectedReward as StoreCreditRewardInterface}
        onSave={handleUpdateStoreCreditReward}
        onClose={handleCloseEditStoreCreditRewardModal}
      />

      {/* Amount Off Order Reward Modals */}
      <AddAmountOffOrderRewardModal
        isEditMode={false}
        onSave={handleAddAmountOffOrderReward}
        onClose={handleCloseAmountOffOrderRewardModal}
      />
      <AddAmountOffOrderRewardModal
        isEditMode={true}
        existingReward={selectedReward as AmountOffOrderRewardInterface}
        onSave={handleUpdateAmountOffOrderReward}
        onClose={handleCloseEditAmountOffOrderRewardModal}
      />

      {/* Free Shipping Reward Modals */}
      <AddFreeShippingRewardModal
        isEditMode={false}
        onSave={handleAddFreeShippingReward}
        onClose={handleCloseFreeShippingRewardModal}
      />
      <AddFreeShippingRewardModal
        isEditMode={true}
        existingReward={selectedReward as FreeShippingRewardInterface}
        onSave={handleUpdateFreeShippingReward}
        onClose={handleCloseEditFreeShippingRewardModal}
      />
    </>
  );
}
