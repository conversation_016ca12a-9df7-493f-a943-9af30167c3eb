// Define constants for reward types
export const REWARD_TYPES = {
  POINTS: "points",
  STORE_CREDIT: "store-credit",
  AMOUNT_OFF: "amount-off",
  FREE_SHIPPING: "free-shipping",
} as const;

// Define constants for discount types
export const DISCOUNT_TYPES = {
  PERCENTAGE: "percentage",
  FIXED: "fixed",
} as const;

// Define constants for minimum requirement types
export const REQUIREMENT_TYPES = {
  NONE: "none",
  AMOUNT: "amount",
  QUANTITY: "quantity",
} as const;

// Define constants for modal IDs
export const MODAL_IDS = {
  ADD_REWARD: "add-reward-modal",
  ADD_POINT_REWARD: "add-point-reward-modal",
  ADD_STORE_CREDIT_REWARD: "add-store-credit-reward-modal",
  ADD_AMOUNT_OFF_ORDER_REWARD: "add-amount-off-order-reward-modal",
  ADD_FREE_SHIPPING_REWARD: "add-free-shipping-reward-modal",
  // Edit modals
  EDIT_POINT_REWARD: "edit-point-reward-modal",
  EDIT_STORE_CREDIT_REWARD: "edit-store-credit-reward-modal",
  EDIT_AMOUNT_OFF_ORDER_REWARD: "edit-amount-off-order-reward-modal",
  EDIT_FREE_SHIPPING_REWARD: "edit-free-shipping-reward-modal",
  // Delete confirmation
  DELETE_REWARD_CONFIRMATION: "delete-reward-confirmation-modal",
} as const;

// Define types for different reward types
export interface BaseRewardInterface {
  id: string;
  type: string;
  title: string;
}

export interface PointRewardInterface extends BaseRewardInterface {
  type: typeof REWARD_TYPES.POINTS;
  value: string;
}

export interface StoreCreditRewardInterface extends BaseRewardInterface {
  type: typeof REWARD_TYPES.STORE_CREDIT;
  value: string;
}

export interface AmountOffOrderRewardInterface extends BaseRewardInterface {
  type: typeof REWARD_TYPES.AMOUNT_OFF;
  value: string;
  discountType: typeof DISCOUNT_TYPES.PERCENTAGE | typeof DISCOUNT_TYPES.FIXED;
  minimumRequirement:
    | typeof REQUIREMENT_TYPES.NONE
    | typeof REQUIREMENT_TYPES.AMOUNT
    | typeof REQUIREMENT_TYPES.QUANTITY;
  minimumValue?: string;
  combinations: {
    productDiscounts: boolean;
    orderDiscounts: boolean;
    shippingDiscounts: boolean;
  };
}

export interface FreeShippingRewardInterface extends BaseRewardInterface {
  type: typeof REWARD_TYPES.FREE_SHIPPING;
  minimumRequirement:
    | typeof REQUIREMENT_TYPES.NONE
    | typeof REQUIREMENT_TYPES.AMOUNT
    | typeof REQUIREMENT_TYPES.QUANTITY;
  minimumValue?: string;
  combinations: {
    productDiscounts: boolean;
    orderDiscounts: boolean;
  };
}

export type RewardTypeInterface =
  | PointRewardInterface
  | StoreCreditRewardInterface
  | AmountOffOrderRewardInterface
  | FreeShippingRewardInterface;
