import { <PERSON><PERSON><PERSON>ck, <PERSON><PERSON>, <PERSON>ton<PERSON>roup, Card, InlineStack, Text } from "@shopify/polaris";
import { SquareParking } from "lucide-react";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { LoyaltyPointsData, REWARD_TYPES, RewardTypeInterface } from "./interface";

interface RewardCardProps {
  initialPointsData?: LoyaltyPointsData;
  reward: RewardTypeInterface;
  basedOnDiffTier: boolean;
  /**
   * Callback function when the Edit button is clicked
   */
  onEdit?: (reward: RewardTypeInterface) => void;
}

export default function RewardCard({
  reward,
  initialPointsData,
  basedOnDiffTier,
  onEdit,
}: Readonly<RewardCardProps>) {
  const { t } = useTranslation();

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(reward);
    }
  }, [onEdit, reward]);

  return (
    <Card key={reward.id}>
      <InlineStack align="space-between" blockAlign="center">
        <BlockStack gap="200">
          {reward.type === REWARD_TYPES.POINTS && (
            <InlineStack as="div" align="space-around" blockAlign="center" gap="400">
              <SquareParking />
              <BlockStack as="div" inlineAlign="baseline">
                <Text as="span" fontWeight="bold">
                  {t("placeOrder.pointsReward.title")}
                </Text>
                <Text as="span" tone="subdued">
                  {t("common.from")} {initialPointsData ? initialPointsData.pointsPerCurrency : "1"}{" "}
                  {t("loyalties.rewards.pointForSpent")}{" "}
                  {initialPointsData ? initialPointsData.currencyAmount : "1"}{" "}
                  {t("loyalties.rewards.spent")}
                  {basedOnDiffTier ? ` ${t("comon.basedOnDiffTier")}` : ""}
                </Text>
              </BlockStack>
            </InlineStack>
          )}

          {reward.type === REWARD_TYPES.STORE_CREDIT && (
            <Text variant="bodyMd" as="p">
              {reward.value} {t("loyalties.rewards.storeCreditsText")}
            </Text>
          )}
        </BlockStack>

        <ButtonGroup>
          <Button onClick={handleEdit}>{t("common.edit")}</Button>
        </ButtonGroup>
      </InlineStack>
    </Card>
  );
}
