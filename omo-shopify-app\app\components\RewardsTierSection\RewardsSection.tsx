import { useShopifyToast } from "@/hooks/useShopifyToast";
import { useAppBridge } from "@shopify/app-bridge-react";
import { BlockStack, <PERSON>ton, Card, InlineStack, Text } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import AddRewardModal from "./AddRewardModal";
import AddStoreCreditRewardModal from "./AddStoreCreditRewardModal";
import DeleteRewardConfirmationModal from "./DeleteRewardConfirmationModal";
import {
  LoyaltyPointsData,
  LoyaltyVIPTierInterface,
  MODAL_IDS,
  PointRewardInterface,
  REWARD_TYPES,
  RewardTypeInterface,
  StoreCreditRewardInterface,
} from "./interface";
import PointRewardModal from "./PointRewardModal";
import RewardCard from "./RewardCard";

interface RewardsSectionProps {
  isSubmitting: boolean;
  onRewardsChange: (rewards: RewardTypeInterface[]) => void;
  onVipTiersChange: (vipTiers: LoyaltyVIPTierInterface[]) => void;
  initialRewards?: RewardTypeInterface[];
  initialVipTiers?: LoyaltyVIPTierInterface[];
  initialPointsData?: LoyaltyPointsData[];
  titleText: string;
  addNewText: string;
  descriptionText: string;
}

export default function RewardsSection({
  isSubmitting,
  onRewardsChange,
  onVipTiersChange,
  initialRewards = [],
  initialVipTiers = [],
  initialPointsData = [],
  titleText,
  addNewText,
  descriptionText,
}: Readonly<RewardsSectionProps>) {
  const { t } = useTranslation();
  const shopify = useAppBridge();
  const { showErrorToast, showSuccessToast } = useShopifyToast();

  // State for rewards
  const [rewards, setRewards] = useState<RewardTypeInterface[]>(initialRewards);
  const [vipTiers, setVipTiers] = useState<LoyaltyVIPTierInterface[]>(initialVipTiers);

  // State for edit and delete modals
  const [selectedReward, setSelectedReward] = useState<RewardTypeInterface | null>(null);
  const [rewardToDelete, setRewardToDelete] = useState<{ id: string; title: string } | null>(null);

  // Update parent component when rewards change
  const updateRewards = useCallback(
    (newRewards: RewardTypeInterface[]) => {
      setRewards(newRewards);
      onRewardsChange(newRewards);
    },
    [onRewardsChange],
  );

  // Update parent component when vipTiers change
  const updateVipTiers = useCallback(
    (newVipTiers: LoyaltyVIPTierInterface[]) => {
      setVipTiers(newVipTiers);
      onVipTiersChange(newVipTiers);
    },
    [onVipTiersChange],
  );

  // Define callback functions for modal operations
  const handleOpenModal = useCallback(() => {
    shopify?.modal?.show(MODAL_IDS.ADD_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCloseModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAddReward = useCallback(
    (value: string) => {
      handleCloseModal();
      switch (value) {
        case REWARD_TYPES.POINTS:
          shopify?.modal?.show(MODAL_IDS.ADD_POINT_REWARD);
          break;
        case REWARD_TYPES.STORE_CREDIT:
          shopify?.modal?.show(MODAL_IDS.ADD_STORE_CREDIT_REWARD);
          break;
        default:
          // Show toast for reward types that are not yet implemented
          showErrorToast(t("loyalties.vip.toastMessages.rewardNotImplemented", { type: value }));
          break;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleCloseModal, t],
  );

  // Handler functions for managing rewards
  const handleAddPointReward = useCallback(
    (reward: PointRewardInterface, vipTier: LoyaltyVIPTierInterface[]) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);

      const newVipTiers = [...vipTiers, ...vipTier];
      updateVipTiers(newVipTiers);
      shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);

      // Show success toast when reward is added
      shopify?.toast.show(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }), {
        isError: false,
        duration: 3000,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards, vipTiers, updateVipTiers],
  );

  const handleClosePointRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Edit reward handlers
  const handleEditReward = useCallback(
    (reward: RewardTypeInterface) => {
      setSelectedReward(reward);

      switch (reward.type) {
        case REWARD_TYPES.POINTS:
          shopify?.modal?.show(MODAL_IDS.EDIT_POINT_REWARD);
          break;
        case REWARD_TYPES.STORE_CREDIT:
          shopify?.modal?.show(MODAL_IDS.EDIT_STORE_CREDIT_REWARD);
          break;
        default:
          // Show toast for reward types that are not yet implemented
          showErrorToast(
            t("loyalties.vip.toastMessages.rewardEditNotImplemented", {
              type: (reward as any).type,
            }),
          );
          break;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const handleUpdatePointReward = useCallback(
    (updatedReward: PointRewardInterface, updateTiers: LoyaltyVIPTierInterface[]) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);

      const newVipTiers = vipTiers.map((vipTier) => {
        const updated = updateTiers.find((ut) => ut.id === vipTier.id);
        return updated ? updated : vipTier;
      });
      updateVipTiers(newVipTiers);

      shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);

      shopify?.modal?.hide(MODAL_IDS.EDIT_POINT_REWARD);
      setSelectedReward(null);

      // Show success toast when reward is updated
      shopify?.toast.show(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
        {
          isError: false,
          duration: 3000,
        },
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditPointRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_POINT_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Store Credit reward update handlers
  const handleUpdateStoreCreditReward = useCallback(
    (updatedReward: StoreCreditRewardInterface) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.EDIT_STORE_CREDIT_REWARD);
      setSelectedReward(null);

      // Show success toast when reward is updated
      showSuccessToast(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditStoreCreditRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_STORE_CREDIT_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleConfirmDelete = useCallback(
    (rewardId?: string) => {
      // Store the title before removing the reward
      const rewardTitle = rewardToDelete?.title ?? t("loyalties.vip.newTier.rewards.title");

      const newRewards = rewards.filter((reward) => reward.id !== rewardId);
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
      setRewardToDelete(null);

      // Show success toast when reward is deleted
      showSuccessToast(t("loyalties.vip.toastMessages.rewardDeleted", { title: rewardTitle }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewardToDelete, rewards, t, updateRewards],
  );

  const handleCancelDelete = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
    setRewardToDelete(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Store Credit reward handlers
  const handleAddStoreCreditReward = useCallback(
    (reward: StoreCreditRewardInterface) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.ADD_STORE_CREDIT_REWARD);

      // Show success toast when reward is added
      showSuccessToast(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseStoreCreditRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_STORE_CREDIT_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Card>
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h2">
              {titleText}
            </Text>
            <Button onClick={handleOpenModal} disabled={isSubmitting}>
              {addNewText}
            </Button>
          </InlineStack>

          <Text as="p" variant="bodyMd">
            {descriptionText}
          </Text>

          {rewards.length > 0 && (
            <BlockStack gap="300">
              <Text variant="headingMd" as="h3">
                {t("loyalties.vip.newTier.rewards.addedRewards")}
              </Text>
              {rewards.map((reward) => (
                <RewardCard
                  key={reward.id}
                  initialPointsData={initialPointsData[0]}
                  basedOnDiffTier={initialVipTiers[0]?.basedOnDiffTier ?? false}
                  reward={reward}
                  onEdit={handleEditReward}
                />
              ))}
            </BlockStack>
          )}
        </BlockStack>
      </Card>

      {/* Add Reward Modals */}
      <AddRewardModal
        id={MODAL_IDS.ADD_REWARD}
        onSubmit={handleAddReward}
        onClose={handleCloseModal}
        existingRewards={rewards}
      />
      <PointRewardModal
        mode="add"
        onSave={handleAddPointReward}
        onClose={handleClosePointRewardModal}
        vipTiers={initialVipTiers}
        pointSettings={initialPointsData[0] || { pointsPerCurrency: 1, currencyAmount: 1 }}
      />

      <PointRewardModal
        mode="edit"
        reward={selectedReward as PointRewardInterface}
        onSave={handleUpdatePointReward}
        onClose={handleCloseEditPointRewardModal}
        vipTiers={initialVipTiers}
        pointSettings={initialPointsData[0] || { pointsPerCurrency: 1, currencyAmount: 1 }}
      />

      <DeleteRewardConfirmationModal
        rewardId={rewardToDelete?.id}
        rewardTitle={rewardToDelete?.title}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />

      {/* Store Credit Reward Modals */}
      <AddStoreCreditRewardModal
        isEditMode={false}
        onSave={handleAddStoreCreditReward}
        onClose={handleCloseStoreCreditRewardModal}
      />
      <AddStoreCreditRewardModal
        isEditMode={true}
        existingReward={selectedReward as StoreCreditRewardInterface}
        onSave={handleUpdateStoreCreditReward}
        onClose={handleCloseEditStoreCreditRewardModal}
      />
    </>
  );
}
