// Define constants for reward types
export const REWARD_TYPES = {
  POINTS: "points",
  STORE_CREDIT: "store-credit",
} as const;

// Define constants for discount types
export const DISCOUNT_TYPES = {
  PERCENTAGE: "percentage",
  FIXED: "fixed",
} as const;

// Define constants for minimum requirement types
export const REQUIREMENT_TYPES = {
  NONE: "none",
  AMOUNT: "amount",
  QUANTITY: "quantity",
} as const;

// Define constants for modal IDs
export const MODAL_IDS = {
  ADD_REWARD: "add-reward-modal",
  ADD_POINT_REWARD: "add-point-reward-modal",
  ADD_STORE_CREDIT_REWARD: "add-store-credit-reward-modal",
  // Edit modals
  EDIT_POINT_REWARD: "edit-point-reward-modal",
  EDIT_STORE_CREDIT_REWARD: "edit-store-credit-reward-modal",
  // Delete confirmation
  DELETE_REWARD_CONFIRMATION: "delete-reward-confirmation-modal",
} as const;

// Define types for different reward types
export interface BaseRewardInterface {
  id: string;
  type: string;
  title: string;
}

export interface PointRewardInterface extends BaseRewardInterface {
  type: typeof REWARD_TYPES.POINTS;
  value: string;
}

export interface StoreCreditRewardInterface extends BaseRewardInterface {
  type: typeof REWARD_TYPES.STORE_CREDIT;
  value: string;
}

export interface LoyaltyVIPTierInterface {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  loyaltyProgramId: number;
  name: string;
  spendRequirement: number | null;
  pointsRequirement: number | null;
  spendAmount: number | null;
  pointEarn: number | null;
  basedOnDiffTier: boolean;
}


export interface LoyaltyPointsData {
  id: number;
  pointsPerCurrency: number;
  currencyAmount: number;
}

export interface TiersRuleInterface extends LoyaltyVIPTierInterface {
  spendAmountStr: string;
  pointEarnStr: string;
}

export type RewardTypeInterface =
  | PointRewardInterface
  | StoreCreditRewardInterface;
