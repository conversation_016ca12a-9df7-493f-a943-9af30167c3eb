export enum CustomerStatus {
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export enum MemberVipTier {
  STARTER = "Starter",
  VIP = "VIP",
  SVIP = "SVIP",
  SSVIP = "SSVIP",
}
export enum OrderMetafield {
  REDEEM_POINTS = "redeem_points",
  REWARD_POINTS = "reward_points",
  ORDER_DATE = "order_date",
  INVOICE = "invoice",
  ORDER_ID = "order_id",
  LOCATION_ID = "location_id",
  STAFF_ID = "staff_id",
  DISCOUNT_AMOUNT = "discount_amount",
  ACTUAL_ORDER_AMOUNT = "actual_order_amount",
  TOTAL_ORDER_AMOUNT = "total_order_amount",
}
export enum CustomerMetafield {
  STATUS = "status",
  VIP_TIER = "vip_tier",
  POINTS = "points",
  IS_COMPLETED_PROFILE = "is_completed_profile",
  BIRTH_DATE = "birth_date",
  GENDER = "gender",
  STORE_CREDIT = "store_credit",
  REGISTER_DATE = "register_date",
  REGISTER_EMPLOYEE = "register_employee",
  REGISTER_LOCATION = "register_location",
  FULL_NAME = "full_name",
  TOTAL_POINTS = "total_points",
}

export enum MetafieldType {
  SINGLE_LINE_TEXT_FIELD = "single_line_text_field",
  BOOLEAN = "boolean",
  NUMBER_INTEGER = "number_integer",
  NUMBER_DECIMAL = "number_decimal",
  DATE_TIME = "date_time",
  DATE = "date",
}
export enum PointIssueType {
  IMMEDIATE = "IMMEDIATE",
  DELAYED = "DELAYED",
}

export enum DelayedPointsStatus {
  ISSUED = "ISSUED",
  PENDING = "PENDING",
  ERROR = "ERROR",
}

export enum gidPrefix {
  ORDER = "gid://shopify/Order/",
  APP = "gid://shopify/App/",
  CUSTOMER = "gid://shopify/Customer/",
}

export enum DiscountTypeName {
  DiscountAutomaticApp = "DiscountAutomaticApp",
  DiscountAutomaticBasic = "DiscountAutomaticBasic",
  DiscountAutomaticBxgy = "DiscountAutomaticBxgy",
  DiscountCodeBxgy = "DiscountCodeBxgy",
  DiscountAutomaticFreeShipping = "DiscountAutomaticFreeShipping",
  DiscountCodeFreeShipping = "DiscountCodeFreeShipping",
  DiscountCodeApp = "DiscountCodeApp",
  DiscountCodeBasic = "DiscountCodeBasic",
}

export enum CustomerSelection {
  DiscountCustomerAll = "DiscountCustomerAll",
  DiscountCustomers = "DiscountCustomers",
  DiscountCustomerSegments = "DiscountCustomerSegments",
}

export enum DiscountCodePrefix {
  AMOUNT_OFF = "AMOUNT_OFF_",
  FREE_SHIP = "FREE_SHIP_",
  REDEEM = "OMO_POINTS_",
}

// Mã quốc gia cho số điện thoại
export const COUNTRY_PHONE_CODES = {
  TAIWAN: "+886",
  // Có thể thêm các mã quốc gia khác tại đây
} as const;

// Giữ lại cho mục đích tương thích ngược
export const TAIWAN_COUNTRY_PHONE_CODE = COUNTRY_PHONE_CODES.TAIWAN;
