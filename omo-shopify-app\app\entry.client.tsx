import { RemixBrowser } from "@remix-run/react";
import { createInstance } from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { I18nextProvider, initReactI18next } from "react-i18next";
import i18n from "./i18n";

async function hydrate() {
  const instance = createInstance();
  await instance
    .use(initReactI18next)
    .use(LanguageDetector)
    .use(Backend)
    .init({
      ...i18n,
      backend: { loadPath: `/locales/{{lng}}/{{ns}}.json?v=${import.meta.env.VITE_APP_VERSION}` },
      detection: {
        order: ["htmlTag", "cookie", "localStorage", "navigator"],
        caches: ["localStorage", "cookie"],
      },
    });

  startTransition(() => {
    hydrateRoot(
      document,
      <StrictMode>
        <I18nextProvider i18n={instance}>
          <RemixBrowser />
        </I18nextProvider>
      </StrictMode>,
    );
  });
}

if (window.requestIdleCallback) {
  window.requestIdleCallback(hydrate);
} else {
  // Safari doesn't support requestIdleCallback
  // https://caniuse.com/requestidlecallback
  window.setTimeout(hydrate, 1);
}
