import { createReadableStreamFromReadable, type EntryContext } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import { createInstance } from "i18next";
import Backend from "i18next-fs-backend/cjs";
import { isbot } from "isbot";
import { resolve } from "node:path";
import { renderToPipeableStream } from "react-dom/server";
import { I18nextProvider, initReactI18next } from "react-i18next";
import { PassThrough } from "stream";
import i18n from "./i18n";
import i18next from "./i18next.server";
import logger from "./logger.server";
import { addDocumentResponseHeaders } from "./shopify.server";

const ABORT_DELAY = 5000;

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
) {
  addDocumentResponseHeaders(request, responseHeaders);
  const callbackName = isbot(request.headers.get("user-agent")) ? "onAllReady" : "onShellReady";

  const instance = createInstance();
  const lng = await i18next.getLocale(request);
  const ns = i18next.getRouteNamespaces(remixContext);

  await instance
    .use(initReactI18next)
    .use(Backend)
    .init({
      ...i18n,
      lng,
      ns,
      backend: { loadPath: resolve("./public/locales/{{lng}}/{{ns}}.json") },
      detection: {
        order: ["querystring", "htmlTag", "cookie", "localStorage", "navigator"],
        caches: [],
      },
    });

  return new Promise((resolve, reject) => {
    let didError = false;

    const { pipe, abort } = renderToPipeableStream(
      <I18nextProvider i18n={instance}>
        <RemixServer context={remixContext} url={request.url} />
      </I18nextProvider>,
      {
        [callbackName]: () => {
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: didError ? 500 : responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          didError = true;
          console.error(error);
        },
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

// Enhanced cron jobs initialization with better error handling and logging
let cronJobsInitialized = false;
let cronInstance: any = null;

// Only initialize on server side and not in test environment
if (typeof window === "undefined" && process.env.NODE_ENV !== "test" && !cronJobsInitialized) {
  // Delay initialization to ensure all modules are loaded
  setTimeout(() => {
    initializeCronJobs();
  }, 1000);
}

async function initializeCronJobs() {
  if (cronJobsInitialized) {
    logger.info(" Cron jobs already initialized, skipping...");
    return;
  }

  try {
    logger.info(" Starting server initialization...");

    // Dynamic import with better error handling
    let tasksModule;
    try {
      tasksModule = await import("./utils/tasks.server");
      logger.info(" Successfully loaded tasks.server module");
    } catch (importError) {
      logger.error(" Failed to import tasks.server module:", importError);
      logger.warn(" Server will continue without background tasks");
      return;
    }

    if (!tasksModule || !tasksModule.default) {
      logger.warn(" No default export found in tasks.server module");
      logger.warn(" Cron jobs module not available - running without background tasks");
      return;
    }

    const runCronJob = tasksModule.default;
    cronInstance = runCronJob();

    // Test if cron instance is valid
    if (!cronInstance || typeof cronInstance.start !== "function") {
      logger.error(" Invalid cron instance - missing start method");
      return;
    }

    // Start cron jobs
    logger.info(" Starting cron jobs...");
    cronInstance.start();

    // Verify cron jobs are running
    if (typeof cronInstance.status === "function") {
      const status = cronInstance.status();
      logger.info(" Cron jobs status:", status);
    }

    cronJobsInitialized = true;

    // Enhanced graceful shutdown handlers
    const shutdownHandler = (signal: string) => {
      logger.info(`📡 ${signal} received, initiating graceful shutdown...`);

      if (cronInstance && typeof cronInstance.stop === "function") {
        try {
          cronInstance.stop();
          logger.info(" Cron jobs stopped successfully");
        } catch (error) {
          logger.error(" Error stopping cron jobs:", error);
        }
      }

      // Give some time for cleanup
      setTimeout(() => {
        logger.info("🏁 Server shutdown complete");
        process.exit(0);
      }, 2000);
    };

    // Handle various shutdown signals
    process.on("SIGTERM", () => shutdownHandler("SIGTERM"));
    process.on("SIGINT", () => shutdownHandler("SIGINT"));
    process.on("SIGHUP", () => shutdownHandler("SIGHUP"));

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      logger.error(" Uncaught Exception:", error);
      shutdownHandler("UNCAUGHT_EXCEPTION");
    });

    process.on("unhandledRejection", (reason, promise) => {
      logger.error(" Unhandled Rejection at:", promise, "reason:", reason);
      // Don't exit on unhandled rejection, just log it
    });

    logger.info(" Server initialization completed successfully with cron jobs");
  } catch (error) {
    logger.error(" Failed to initialize server:", error);
    logger.error("Stack trace:", error instanceof Error ? error.stack : "No stack trace");
    logger.info(" Server will continue without cron jobs");
  }
}

// Export function to manually check cron status (useful for debugging)
export function getCronStatus() {
  return {
    initialized: cronJobsInitialized,
    hasInstance: !!cronInstance,
    instanceMethods: cronInstance ? Object.keys(cronInstance) : [],
    timestamp: new Date().toISOString(),
  };
}

// Export function to manually restart cron jobs (useful for debugging)
export async function restartCronJobs() {
  logger.info(" Manually restarting cron jobs...");

  if (cronInstance && typeof cronInstance.stop === "function") {
    cronInstance.stop();
  }

  cronJobsInitialized = false;
  cronInstance = null;

  await initializeCronJobs();

  return getCronStatus();
}
