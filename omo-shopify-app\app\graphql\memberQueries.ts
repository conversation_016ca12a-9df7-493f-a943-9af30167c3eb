// File: app/graphql/memberQueries.ts
/**
 * GraphQL queries related to member management
 */

// Main query to fetch customers list with pagination
export const GET_CUSTOMERS_QUERY = /* GraphQL */ `
  query getCustomers($first: Int, $last: Int, $before: String, $after: String, $query: String) {
    customers(
      first: $first
      last: $last
      before: $before
      after: $after
      query: $query
      sortKey: UPDATED_AT
      reverse: true
    ) {
      edges {
        node {
          id
          legacyResourceId
          displayName
          email
          phone
          firstName
          lastName
          numberOfOrders
          amountSpent {
            amount
            currencyCode
          }
          tags
          metafields(first: 20, namespace: "$app") {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

// Query to count total customers (for pagination)
export const GET_TOTAL_CUSTOMERS_QUERY = `
query getTotalCustomers($query: String) {
  customers(first: 250, query: $query) {
    edges {
      node {
        id
      }
    }
  }
}`;

// Mutation to add customer tags
export const ADD_CUSTOMER_TAGS_MUTATION = `
mutation addTags($id: ID!, $tags: [String!]!) {
  tagsAdd(id: $id, tags: $tags) {
    node {
      id
    }
    userErrors {
      field
      message
    }
  }
}`;

// Function to build search query string based on search term and type
export const buildSearchQuery = (searchTerm: string, type: string): string => {
  if (!searchTerm || searchTerm.trim() === "") return "";

  searchTerm = searchTerm.trim();
  let query = "";

  if (type === "email") {
    query = `email:*${searchTerm}*`;
  } else if (type === "name") {
    query = `first_name:${searchTerm}* OR last_name:${searchTerm}*`;
  }

  return query;
};

// Mutation to update customer metafields
export const UPDATE_CUSTOMER_METAFIELD_MUTATION = `
mutation updateCustomerMetafield($input: CustomerInput!) {
  customerUpdate(input: $input) {
    customer {
      id
    }
    userErrors {
      field
      message
    }
  }
}`;

// ──────────────────────────────────────────────────────────
// Fetch a single “points” metafield for one customer
// ──────────────────────────────────────────────────────────
export const GET_CUSTOMER_POINTS = /* GraphQL */ `
  query getCustomerPoints($customerId: ID!) {
    customer(id: $customerId) {
      id
      pointsMf: metafield(namespace: "$app", key: "points") {
        id
        value
      }
    }
  }
`;

// ──────────────────────────────────────────────────────────
// Upsert (create or update) that same metafield in one call
// ──────────────────────────────────────────────────────────
export const UPSERT_CUSTOMER_POINTS = `
  mutation upsertCustomerPoints(
    $ownerId: ID!
    $namespace: String!
    $key: String!
    $value: String!
  ) {
    metafieldsSet(
      metafields: [{ ownerId: $ownerId, namespace: $namespace, key: $key, value: $value }]
    ) {
      metafields {
        id
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;
