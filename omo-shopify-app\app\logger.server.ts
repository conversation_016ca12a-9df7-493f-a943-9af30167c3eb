import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import LokiTransport from "winston-loki";

// Define custom log levels if needed (optional).
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Determine the log level based on NODE_ENV or a specific environment variable.
const level = process.env.LOG_LEVEL || (process.env.NODE_ENV === "production" ? "info" : "debug");

// Loki configuration
const lokiUrl = process.env.LOKI_URL || "http://loki:3100";
const serviceName = process.env.SERVICE_NAME || "omo-shopify-app";

// Create a Winston logger instance.
const logger = winston.createLogger({
  levels: logLevels,
  level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message, stack }) => {
      return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
    }),
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize({ all: true }),
        winston.format.printf(
          ({ timestamp, level, message, stack }) => `${timestamp} [${level}]: ${stack || message}`,
        ),
      ),
    }),
    new DailyRotateFile({
      filename: "app-%DATE%.log",
      dirname: "logs",
      datePattern: "YYYY-MM-DD",
      zippedArchive: true,
      maxSize: "20m",
      maxFiles: "14d",
    }),
    new LokiTransport({
      host: lokiUrl,
      labels: {
        service: serviceName,
        environment: process.env.NODE_ENV || "development",
        app: "omo-shopify-app"
      },
      json: true,
      format: winston.format.json(),
      replaceTimestamp: true,
      onConnectionError: (err: Error) => {
        console.error("Loki connection error:", err.message);
      },
    }),
  ],
  exitOnError: false,
});

export default logger;
