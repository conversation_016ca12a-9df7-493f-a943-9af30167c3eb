import { LoaderFunctionArgs, redirect } from "@remix-run/node";
import { Link } from "@remix-run/react";
import {
  Award,
  BarChart3,
  Building,
  Gift,
  Heart,
  MessageSquare,
  RefreshCw,
  Settings,
  Star,
  Ticket,
  TrendingUp,
  Users,
  X,
  Zap,
} from "lucide-react";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }

  return {};
};

// Custom Component Styles (can be further customized)
const customButtonClasses =
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background";
const primaryButtonClasses = `${customButtonClasses} bg-blue-600 text-white hover:bg-blue-700`;
const secondaryButtonClasses = `${customButtonClasses} bg-slate-100 text-slate-900 hover:bg-slate-200`;
const outlineButtonClasses = `${customButtonClasses} border border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white`;

const customInputClasses =
  "flex h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50";

const customCardClasses = "rounded-lg border bg-white text-slate-900 shadow-sm";

export default function LandingPage() {
  const features = [
    {
      icon: <RefreshCw className="w-10 h-10 text-blue-600" />,
      title: "Customer & Point Sync",
      description:
        "Sync customer data and points seamlessly between Shopify and your physical POS.",
    },
    {
      icon: <Star className="w-10 h-10 text-blue-600" />,
      title: "Automated Point Accrual",
      description: "Customers automatically earn points for every purchase, online or in-store.",
    },
    {
      icon: <Ticket className="w-10 h-10 text-blue-600" />,
      title: "Rewards & Vouchers",
      description: "Let customers redeem points for flexible discount codes and exclusive rewards.",
    },
    {
      icon: <Award className="w-10 h-10 text-blue-600" />,
      title: "Membership Tiers",
      description:
        "Create Silver, Gold, and Platinum tiers with unique benefits to foster loyalty.",
    },
    {
      icon: <BarChart3 className="w-10 h-10 text-blue-600" />,
      title: "Reporting Dashboard",
      description:
        "Gain actionable insights with powerful analytics on loyalty and customer behavior.",
    },
    {
      icon: <Users className="w-10 h-10 text-blue-600" />,
      title: "Referral Programs",
      description: "Turn loyal customers into brand advocates with a built-in referral system.",
    },
  ];

  const benefits = [
    {
      icon: <Heart className="w-8 h-8 text-blue-500" />,
      title: "Increase Customer Retention",
      description: "Reward loyalty and give customers a compelling reason to return.",
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-blue-500" />,
      title: "Enhance Order Value",
      description: "Motivate customers to spend more to unlock the next reward tier.",
    },
    {
      icon: <Zap className="w-8 h-8 text-blue-500" />,
      title: "Reduce Operational Errors",
      description: "Automate point tracking to eliminate manual mistakes and printing costs.",
    },
    {
      icon: <Settings className="w-8 h-8 text-blue-500" />,
      title: "Automate Marketing",
      description: "Use behavioral data to send targeted, automated marketing campaigns.",
    },
  ];

  const testimonials = [
    {
      quote:
        "This app has been a game-changer for our customer retention strategy. The seamless online-to-offline integration is flawless.",
      name: "Sarah Johnson",
      company: "CEO of The Fashion Hub",
    },
    {
      quote:
        "Our average order value has increased by 20% since implementing the tiered loyalty program. The dashboard provides incredible insights.",
      name: "Michael Chen",
      company: "Founder of Gadget Grove",
    },
  ];

  const clientBrandNames = [
    "Urban Style",
    "Coffee Bloom",
    "TechNest",
    "GreenLeaf Organics",
    "Bookworm's Haven",
  ];

  return (
    <div className="flex flex-col min-h-screen bg-white font-sans text-slate-800">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-sm border-b border-slate-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between h-16">
          <Link to="#" className="flex items-center gap-2">
            <Gift className="w-7 h-7 text-blue-600" />
            <span className="text-xl font-bold text-slate-800">LoyaltyFlow</span>
          </Link>
          <nav className="hidden md:flex items-center gap-6">
            <Link
              to="#features"
              className="text-sm font-medium text-slate-600 hover:text-blue-600 transition-colors"
            >
              Features
            </Link>
            <Link
              to="#how-it-works"
              className="text-sm font-medium text-slate-600 hover:text-blue-600 transition-colors"
            >
              How It Works
            </Link>
            <Link
              to="#pricing"
              className="text-sm font-medium text-slate-600 hover:text-blue-600 transition-colors"
            >
              Pricing
            </Link>
            <Link
              to="#support"
              className="text-sm font-medium text-slate-600 hover:text-blue-600 transition-colors"
            >
              Support
            </Link>
          </nav>
          <Link to="#" className={`${primaryButtonClasses} px-4 py-2 hidden md:inline-flex`}>
            Get Started
          </Link>
        </div>
      </header>

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative bg-blue-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28 text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-slate-900 tracking-tight">
              Connect Online & Offline
            </h1>
            <p className="mt-4 text-4xl md:text-5xl lg:text-6xl font-extrabold text-blue-600 tracking-tight">
              Increase Loyalty, Boost Revenue
            </p>
            <p className="mt-6 max-w-2xl mx-auto text-lg text-slate-600">
              Seamless Loyalty Programs Between Shopify and Physical Stores.
            </p>
            <div className="mt-8 flex justify-center gap-4">
              <Link to="#" className={`${primaryButtonClasses} px-6 py-3 text-lg`}>
                Start Your Free 14-Day Trial
              </Link>
            </div>
          </div>
          {/* Visual placeholder for Hero - can be a decorative div or removed entirely */}
          <div className="relative h-20 md:h-32 lg:h-40 -mt-10 md:-mt-16 bg-blue-100/50 w-full max-w-4xl mx-auto rounded-t-lg">
            <div className="absolute inset-0 flex items-center justify-center">
              <p className="text-sm text-blue-500 italic">
                Visualize your dashboard & QR scanning here
              </p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 md:py-28 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold">A Complete Loyalty Toolkit</h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Everything you need to build a powerful, unified loyalty program that customers
                love.
              </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature) => (
                <div key={feature.title} className="text-center p-6">
                  <div className="flex justify-center mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-slate-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section id="benefits" className="py-20 md:py-28 bg-slate-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold">Drive Real Business Growth</h2>
                <p className="mt-4 text-lg text-slate-600">
                  Our platform isn&apos;t just about points; it&apos;s about delivering measurable
                  results that impact your bottom line.
                </p>
                <div className="mt-8 space-y-6">
                  {benefits.map((benefit) => (
                    <div key={benefit.title} className="flex items-start gap-4">
                      <div className="flex-shrink-0 bg-blue-100 text-blue-600 rounded-full p-3">
                        {benefit.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{benefit.title}</h3>
                        <p className="text-slate-600">{benefit.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="hidden lg:flex items-center justify-center p-8 bg-blue-100 rounded-lg min-h-[300px]">
                <p className="text-blue-500 italic text-center">
                  Illustrative charts & data visualizations can appear here.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-20 md:py-28 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold">Launch in 3 Simple Steps</h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Get your unified loyalty program up and running in minutes, not weeks.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8 md:gap-16">
              <div className="absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 hidden md:block" />
              <div className="relative flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mb-4 z-10">
                  1
                </div>
                <h3 className="text-xl font-semibold mb-2">Install & Connect</h3>
                <p className="text-slate-600">
                  Install the app from the Shopify Store and connect your POS system with a few
                  clicks.
                </p>
              </div>
              <div className="relative flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mb-4 z-10">
                  2
                </div>
                <h3 className="text-xl font-semibold mb-2">Configure Rules</h3>
                <p className="text-slate-600">
                  Set your point-to-dollar ratios, define rewards, and create your membership tiers.
                </p>
              </div>
              <div className="relative flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mb-4 z-10">
                  3
                </div>
                <h3 className="text-xl font-semibold mb-2">Launch & Grow</h3>
                <p className="text-slate-600">
                  Go live and watch your customer loyalty and revenue grow automatically.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 md:py-28 bg-blue-600 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold">Custom Solutions for Your Brand</h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-blue-200">
              Every business is unique. We provide custom enterprise-level solutions tailored to
              your specific needs, volume, and goals.
            </p>
            <div className="mt-8">
              <Link to="#" className={`${secondaryButtonClasses} px-6 py-3 text-lg`}>
                Contact Sales for a Custom Quote
              </Link>
            </div>
          </div>
        </section>

        {/* Testimonials & Logos Section */}
        <section className="py-20 md:py-28 bg-slate-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold">Trusted by Leading Brands</h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                See what our happy customers have to say about LoyaltyFlow.
              </p>
            </div>
            <div className="grid lg:grid-cols-2 gap-8">
              {testimonials.map((testimonial) => (
                <div key={testimonial.name} className={`${customCardClasses} bg-white`}>
                  <div className="p-8">
                    <MessageSquare className="w-8 h-8 text-blue-500 mb-4" />
                    <p className="text-slate-600 italic mb-6">&quot;{testimonial.quote}&quot;</p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-slate-200 flex items-center justify-center mr-4">
                        <Users className="w-6 h-6 text-slate-500" /> {/* Placeholder for avatar */}
                      </div>
                      <div>
                        <p className="font-semibold text-slate-800">{testimonial.name}</p>
                        <p className="text-sm text-slate-500">{testimonial.company}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-20 text-center">
              <h3 className="text-xl font-semibold text-slate-700 mb-6">
                Join these and many other successful brands
              </h3>
              <div className="flex flex-wrap justify-center items-center gap-x-8 gap-y-4">
                {clientBrandNames.map((brandName) => (
                  <div
                    key={brandName}
                    className="flex items-center gap-2 p-3 border border-slate-200 rounded-md bg-white"
                  >
                    <Building className="w-5 h-5 text-slate-500" />
                    <span className="text-sm font-medium text-slate-600">{brandName}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer id="support" className="bg-slate-900 text-slate-300">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
            <div className="col-span-2 lg:col-span-1">
              <Link to="#" className="flex items-center gap-2 mb-4">
                <Gift className="w-7 h-7 text-blue-500" />
                <span className="text-xl font-bold text-white">LoyaltyFlow</span>
              </Link>
              <p className="text-sm text-slate-400">The future of customer loyalty.</p>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link to="#features" className="hover:text-blue-400">
                    Features
                  </Link>
                </li>
                <li>
                  <Link to="#pricing" className="hover:text-blue-400">
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-blue-400">
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-blue-400">
                    Support
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link to="#" className="hover:text-blue-400">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-blue-400">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-blue-400">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
            <div className="col-span-2 lg:col-span-2">
              <h4 className="font-semibold text-white mb-4">Subscribe to our newsletter</h4>
              <p className="text-sm text-slate-400 mb-4">
                Get the latest news, articles, and resources, sent to your inbox weekly.
              </p>
              <form className="flex flex-col sm:flex-row gap-2">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className={`${customInputClasses} bg-slate-800 border-slate-700 text-white placeholder-slate-500 flex-grow`}
                />
                <button type="submit" className={`${outlineButtonClasses} px-4 py-2`}>
                  Subscribe
                </button>
              </form>
            </div>
          </div>
          <div className="mt-12 border-t border-slate-800 pt-8 flex flex-col sm:flex-row justify-between items-center text-sm">
            <p className="text-slate-500">
              &copy; {new Date().getFullYear()} LoyaltyFlow, Inc. All rights reserved.
            </p>
            <div className="flex space-x-4 mt-4 sm:mt-0">
              <Link to="#" className="text-slate-500 hover:text-white">
                <X className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
