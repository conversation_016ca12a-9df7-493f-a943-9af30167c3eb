import { CustomerSelection, DiscountTypeName } from "@/constants";
import db from "@/db.server";
import { gql } from "@apollo/client/core";
import { type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { clientByShop } from "../utils/auth.server";

const GET_CUSTOMER_DISCOUNTS = gql`
  query GetCustomerDiscounts($first: Int!, $after: String) {
    discountNodes(first: $first, after: $after, query: "status:active") {
      edges {
        cursor
        node {
          id
          discount {
            __typename
            ... on DiscountCodeBasic {
              title
              codes(first: 100) {
                nodes {
                  code
                }
              }
              combinesWith {
                orderDiscounts
                productDiscounts
                shippingDiscounts
              }
              endsAt
              minimumRequirement {
                ... on DiscountMinimumQuantity {
                  greaterThanOrEqualToQuantity
                }
                ... on DiscountMinimumSubtotal {
                  greaterThanOrEqualToSubtotal {
                    amount
                  }
                }
              }
              customerSelection {
                __typename
                ... on DiscountCustomers {
                  customers {
                    id
                  }
                }
                ... on DiscountCustomerSegments {
                  segments {
                    id
                  }
                }
                ... on DiscountCustomerAll {
                  allCustomers
                }
              }
            }
            ... on DiscountCodeBxgy {
              title
              codes(first: 10) {
                nodes {
                  code
                }
              }
              combinesWith {
                orderDiscounts
                productDiscounts
                shippingDiscounts
              }
              endsAt
              customerSelection {
                __typename
                ... on DiscountCustomers {
                  customers {
                    id
                  }
                }
                ... on DiscountCustomerSegments {
                  segments {
                    id
                  }
                }
                ... on DiscountCustomerAll {
                  allCustomers
                }
              }
            }
            ... on DiscountCodeFreeShipping {
              title
              codes(first: 10) {
                nodes {
                  code
                }
              }
              combinesWith {
                orderDiscounts
                productDiscounts
                shippingDiscounts
              }
              endsAt
              minimumRequirement {
                ... on DiscountMinimumQuantity {
                  greaterThanOrEqualToQuantity
                }
                ... on DiscountMinimumSubtotal {
                  greaterThanOrEqualToSubtotal {
                    amount
                  }
                }
              }
              customerSelection {
                __typename
                ... on DiscountCustomers {
                  customers {
                    id
                  }
                }
                ... on DiscountCustomerSegments {
                  segments {
                    id
                  }
                }
                ... on DiscountCustomerAll {
                  allCustomers
                }
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // 1. Authenticate the request via Shopify Customer Account (returns sessionToken with sub = customerId)
  const { cors, sessionToken } = await authenticate.public.customerAccount(request);
  const { dest: myshopifyDomain, sub: numericCustomerId } = sessionToken;

  // 2. Look up the shop in your own DB so you can create an Apollo client
  const shop = await db.shop.findFirstOrThrow({
    where: { myshopifyDomain },
  });

  const gqlClient = await clientByShop(shop);

  // 3. Fetch all discount codes assigned to this customer
  async function fetchAllDiscountsForCustomer(customerId: string) {
    // Convert to global ID format
    const customerGid = `${customerId}`;
    const perPage = 100;
    let hasNextPage = true;
    let afterCursor: string | null = null;
    const allDiscounts: Array<{
      id: string;
      codes: string[];
      title: string;
      customerSelection:
        | { __typename: CustomerSelection.DiscountCustomers; customers: Array<{ id: string }> }
        | {
            __typename: CustomerSelection.DiscountCustomerSegments;
            segments: Array<{ id: string }>;
          }
        | { __typename: CustomerSelection.DiscountCustomerAll };
      combinesWith: {
        shippingDiscounts: boolean;
        productDiscounts: boolean;
        orderDiscounts: boolean;
      };
      endsAt: string | null;
      minimumRequirement:
        | { greaterThanOrEqualToQuantity: string }
        | { greaterThanOrEqualToSubtotal: { amount: string } };
    }> = [];

    while (hasNextPage) {
      const result: any = await gqlClient.query<{
        discountNodes: {
          edges: Array<{
            cursor: string;
            node: {
              id: string;
              discount: {
                __typename: string;
                title?: string;
                codes?: {
                  nodes: Array<{ code: string }>;
                };
                customerSelection?: {
                  __typename: string;
                  customers?: Array<{ id: string }>;
                  segments?: Array<{ id: string }>;
                  allCustomers?: boolean;
                };
              };
            };
          }>;
          pageInfo: { hasNextPage: boolean; endCursor: string | null };
        };
      }>({
        query: GET_CUSTOMER_DISCOUNTS,
        variables: {
          first: perPage,
          after: afterCursor ?? undefined,
        },
        fetchPolicy: "network-only",
      });

      const { data } = result;

      data.discountNodes.edges.forEach(({ node }: { node: any }) => {
        const discount = node.discount;

        // Only process discount codes (not automatic discounts)
        if (
          discount.__typename === DiscountTypeName.DiscountCodeBasic ||
          discount.__typename === DiscountTypeName.DiscountCodeBxgy ||
          discount.__typename === DiscountTypeName.DiscountCodeFreeShipping
        ) {
          const codes =
            discount.codes?.nodes.map((codeNode: { code: string }) => codeNode.code) ?? [];
          const customerSelection = discount.customerSelection;

          if (customerSelection) {
            // Check if this discount applies to our customer
            let appliesToCustomer = false;

            if (customerSelection.__typename === CustomerSelection.DiscountCustomerAll) {
              appliesToCustomer = true;
            } else if (customerSelection.__typename === CustomerSelection.DiscountCustomers) {
              appliesToCustomer =
                customerSelection.customers?.some(
                  (customer: { id: string }) => customer.id === customerGid,
                ) ?? false;
            }
            // Note: For segments, we'd need additional logic to check if customer is in those segments

            if (appliesToCustomer) {
              allDiscounts.push({
                id: node.id,
                codes,
                title: discount.title ?? "",
                customerSelection: customerSelection,
                combinesWith: {
                  shippingDiscounts: discount.combinesWith?.shippingDiscounts,
                  productDiscounts: discount.combinesWith?.productDiscounts,
                  orderDiscounts: discount.combinesWith?.orderDiscounts,
                },
                endsAt: discount.endsAt,
                minimumRequirement: discount.minimumRequirement,
              });
            }
          }
        }
      });

      hasNextPage = data.discountNodes.pageInfo.hasNextPage;
      afterCursor = data.discountNodes.pageInfo.endCursor;
    }

    return allDiscounts;
  }

  // 4. Actually call the function with the real customer ID
  let discounts = [];
  try {
    discounts = await fetchAllDiscountsForCustomer(numericCustomerId);
  } catch (err) {
    console.error("Error fetching customer discounts:", err);
    // You can choose to return an empty array or bubble up an error
    return cors(
      Response.json({ error: "Could not fetch discounts for this customer." }, { status: 500 }),
    );
  }

  // 5. Return the list of discounts in JSON
  return cors(
    Response.json(
      {
        shop: myshopifyDomain,
        customerId: numericCustomerId,
        discounts,
      },
      { status: 200 },
    ),
  );
};

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { cors, sessionToken } = await authenticate.public.customerAccount(request);
  console.log("sessionToken", sessionToken);
  return cors(Response.json({ message: "Action endpoint reached" }));
};
