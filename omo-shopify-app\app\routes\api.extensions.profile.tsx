import { CustomerMetafield, MetafieldType } from "@/constants";
import db from "@/db.server";
import { MemberMetafield } from "@/types/memberTypes";
import { clientByShop } from "@/utils/auth.server";
import { gql } from "@apollo/client/core";
import { type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // 1. Authenticate the request via Shopify Customer Account (returns sessionToken with sub = customerId)
  const { cors, sessionToken } = await authenticate.public.customerAccount(request);
  const { dest: myshopifyDomain, sub: numericCustomerId } = sessionToken;

  const shop = await db.shop.findFirstOrThrow({
    where: { myshopifyDomain },
  });

  const completeProfileSettings = await db.completeProfileSettings.findFirst({
    where: { shopId: shop.id },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });
  const gqlClient = await clientByShop(shop);
  const metafields = await gqlClient.mutate({
    mutation: gql`
      query CustomerMetafields($ownerId: ID!) {
        customer(id: $ownerId) {
          metafields(first: 20, namespace: "$app") {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
      }
    `,
    variables: {
      ownerId: numericCustomerId,
    },
  });
  const customerMetafields = metafields.data.customer?.metafields?.edges.map((m: any) => ({
    namespace: m.node.namespace,
    key: m.node.key,
    value: m.node.value,
  })) as MemberMetafield[];
  const gender = customerMetafields?.find((m) => m.key === CustomerMetafield.GENDER)?.value;
  const birthday = customerMetafields?.find((m) => m.key === CustomerMetafield.BIRTH_DATE)?.value;
  const currentPoint = customerMetafields?.find((m) => m.key === CustomerMetafield.POINTS)?.value;
  const tier = customerMetafields?.find((m) => m.key === CustomerMetafield.VIP_TIER)?.value;
  return cors(
    Response.json(
      {
        shop: myshopifyDomain,
        customerId: numericCustomerId,
        genderOptions: completeProfileSettings?.customProfile?.gender,
        gender,
        birthday,
        currentPoint,
        tier,
      },
      { status: 200 },
    ),
  );
  // return cors(Response.json({ message: "Loader endpoint reached" }));
};

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { cors, sessionToken } = await authenticate.public.customerAccount(request);
  console.log("sessionToken", sessionToken);
  const body = await request.json();
  const { gender, birthday } = body;
  const { dest: myshopifyDomain, sub: numericCustomerId } = sessionToken;

  const shop = await db.shop.findFirstOrThrow({
    where: { myshopifyDomain },
  });

  const gqlClient = await clientByShop(shop);
  await gqlClient.mutate({
    mutation: gql`
      mutation updateCustomerMetafields($input: CustomerInput!) {
        customerUpdate(input: $input) {
          userErrors {
            message
            field
          }
        }
      }
    `,
    variables: {
      input: {
        id: numericCustomerId,
        metafields: [
          {
            key: CustomerMetafield.GENDER,
            namespace: "$app",
            value: gender,
            type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
          },
          {
            key: CustomerMetafield.BIRTH_DATE,
            namespace: "$app",
            value: birthday,
            type: MetafieldType.DATE,
          },
        ],
      },
    },
  });
  return cors(
    Response.json(
      {
        shop: myshopifyDomain,
        customerId: numericCustomerId,
        gender,
        birthday,
      },
      { status: 200 },
    ),
  );
};
