import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import responseSuccess from "@/utils/response.success";
import type { ActionFunctionArgs } from "@remix-run/node";
import logger from "@/logger.server";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  transformCustomerData,
} from "../api.members/member.schema";
import { MEMBER_QUERY } from "../api.members/query";
import { UPDATE_CUSTOMER_MUTATION, UPDATE_MARKETING_NOTIFY_MUTATION } from "./mutation";
import { updateMemberSchema } from "./update-member.shema";

export async function action({ request, params }: ActionFunctionArgs) {
  if (request.method !== "PATCH") {
    logger.warn(`Method not allowed - ${request.method}`);
    throw new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    logger.error(`Invalid JSON payload - ${e.message}`);
    throw responseBadRequest([], e.message);
  });

  try {
    await updateMemberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    const errorDetails = mapJoiErrorToMemberError(error.details);
    logger.error(`Validation failed - ${JSON.stringify(errorDetails)}`);
    return responseBadRequest(errorDetails);
  }

  if (!params.id) {
    const errorDetails = [
      {
        field: "memberId",
        code: "invalid_value",
        message: "The 'memberId' must be a valid Shopify customer ID",
      },
    ];
    logger.error(`Missing member ID - ${JSON.stringify(errorDetails)}`);
    return responseBadRequest(errorDetails);
  }

  const customerId = params.id.includes("gid://shopify/Customer/")
    ? params.id
    : `gid://shopify/Customer/${params.id}`;

  const { data: memberQueryResult } = await client.query({
    query: MEMBER_QUERY,
    variables: {
      identifier: { id: customerId },
    },
  });

  if (!memberQueryResult.customerByIdentifier) {
    const errorDetails = [
      {
        field: "memberId",
        code: "invalid_value",
        message: "Member not found",
      },
    ];
    logger.error(`Member not found - ID: ${customerId}`);
    return responseBadRequest(errorDetails);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const bodyData: Record<string, any> = {
    ...mapToShopifyInput(body),
    id: memberQueryResult.customerByIdentifier.id,
  };

  Object.keys(bodyData).forEach((key) => {
    if (
      bodyData[key] === "" ||
      bodyData[key] === undefined ||
      bodyData[key] === null ||
      (Array.isArray(bodyData[key]) && bodyData[key].length === 0)
    ) {
      delete bodyData[key];
    }
  });

  delete bodyData.emailMarketingConsent;

  const { data, errors } = await client.mutate({
    mutation: UPDATE_CUSTOMER_MUTATION,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    logger.error(`Customer update failed - ${JSON.stringify(errors)}`);
    throw Response.json(errors, { status: 500 });
  }

  const { userErrors, customer } = data.customerUpdate;

  if (userErrors && userErrors.length > 0) {
    const errorDetails = mapErrors(userErrors);
    logger.error(`Customer update validation failed - ${JSON.stringify(errorDetails)}`);
    throw responseBadRequest(errorDetails);
  }

  if (body.marketingNotify !== undefined) {
    const bodyData: {
      customerId: string;
      emailMarketingConsent: Record<string, string>;
    } = {
      customerId: memberQueryResult.customerByIdentifier.id,
      emailMarketingConsent: {},
    };

    if (body.marketingNotify) {
      bodyData.emailMarketingConsent = {
        marketingState: "SUBSCRIBED",
        marketingOptInLevel: "UNKNOWN",
      };
    } else {
      bodyData.emailMarketingConsent = {
        marketingState: "UNSUBSCRIBED",
      };
    }

    const { data, errors } = await client.mutate({
      mutation: UPDATE_MARKETING_NOTIFY_MUTATION,
      variables: { input: bodyData },
      fetchPolicy: "no-cache",
    });

    if (errors) {
      logger.error(`Marketing consent update failed - ${JSON.stringify(errors)}`);
      throw Response.json(errors, { status: 500 });
    }
    const { userErrors } = data.customerEmailMarketingConsentUpdate;

    if (userErrors && userErrors.length > 0) {
      const errorDetails = mapErrors(userErrors);
      logger.error(`Marketing consent validation failed - ${JSON.stringify(errorDetails)}`);
      throw responseBadRequest(errorDetails);
    }
  }

  const transformData = transformCustomerData(customer);
  const responseData = {
    ...transformData,
    marketingNotify:
      body.marketingNotify !== undefined ? body.marketingNotify : transformData.marketingNotify,
  };

  logger.info(`Member updated successfully - ID: ${customerId}`);
  return responseSuccess(responseData);
}
