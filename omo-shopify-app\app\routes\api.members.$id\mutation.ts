import { gql } from "@apollo/client/core";

export const UPDATE_CUSTOMER_MUTATION = gql`
  mutation updateCustomerMetafields($input: CustomerInput!) {
    customerUpdate(input: $input) {
      customer {
        id
        email
        phone
        taxExempt
        emailMarketingConsent {
          marketingState
          marketingOptInLevel
          consentUpdatedAt
        }
        firstName
        lastName
        amountSpent {
          amount
          currencyCode
        }
        smsMarketingConsent {
          marketingState
          marketingOptInLevel
        }
        addresses {
          address1
          city
          country
          phone
          zip
        }
        metafields(first: 20, namespace: "$app") {
          nodes {
            id
            key
            namespace
            value
          }
        }
      }
      userErrors {
        message
        field
      }
    }
  }
`;

export const UPDATE_MARKETING_NOTIFY_MUTATION = gql`
  mutation customerEmailMarketingConsentUpdate($input: CustomerEmailMarketingConsentUpdateInput!) {
    customerEmailMarketingConsentUpdate(input: $input) {
      userErrors {
        field
        message
      }
    }
  }
`;
