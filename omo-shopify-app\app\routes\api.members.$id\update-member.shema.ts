import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import <PERSON><PERSON> from "joi";
import { IMember } from "../api.members/member.schema";

dayjs.extend(customParseFormat);

// Joi validation schema
export const updateMemberSchema = Joi.object<IMember>({
  // Required fields
  cellphone: Joi.string()
    .trim()
    .regex(/^09\d{8}$/)
    .messages({
      "string.empty": "The 'cellphone' cannot be empty",
      "string.pattern.base":
        "The 'cellphone' field has an invalid cellphone format. The total numbers must be 10 digits and start with '09'.",
    }),
  actionDate: Joi.date().iso().required().messages({
    "any.required": "The 'actionDate' is required",
    "date.format":
      "The 'actionDate' field has an invalid date/time format. Please use YYYY-MM-DDTHH:mm:ss format",
  }),
  // Optional fields
  email: Joi.string().max(255).email().allow("").optional().messages({
    "string.email": "The 'email' format is invalid",
    "string.max": "The 'email' cannot exceed 255 characters",
  }),
  lastName: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'lastName' cannot exceed 255 characters",
  }),
  firstName: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'firstName' cannot exceed 255 characters",
  }),
  birthday: Joi.string()
    .custom((value, helpers) => {
      const isValid = dayjs(value, "YYYY-MM-DD", true).isValid();
      if (!isValid) {
        return helpers.error("date.format");
      }
    })
    .allow("")
    .optional()
    .messages({
      "date.format": "The 'birthday' has an invalid date format. Please use YYYY-MM-DD format",
    }),
  gender: Joi.string().valid("Male", "Female", "Other").allow("").optional().messages({
    "any.only": "The 'gender' must be one of: Male, Female, Other",
  }),
  address: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'address' cannot exceed 255 characters",
  }),
  marketingNotify: Joi.boolean().optional().messages({
    "boolean.base": "The 'marketingNotify' must be true or false",
  }),
  registerEmployee: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'registerEmployee' cannot exceed 255 characters",
  }),
  registerLocation: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'registerLocation' cannot exceed 255 characters",
  }),
});
