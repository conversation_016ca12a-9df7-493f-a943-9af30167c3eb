import { CustomerStatus, TAIWAN_COUNTRY_PHONE_CODE } from "@/constants";
import db from "@/db.server";
import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import responseSuccess from "@/utils/response.success";
import { ActionFunctionArgs } from "@remix-run/node";
import logger from "@/logger.server";
import dayjs from "dayjs";
import { UPDATE_CUSTOMER_MUTATION } from "../api.members.$id/mutation";
import { mapJoiErrorToMemberError } from "../api.members/member.schema";
import { MEMBER_QUERY } from "../api.members/query";
import { cancelMemberSchema } from "./cancel-member.shema";
import { DELETE_CUSTOMER_MUTATION } from "./mutation";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    logger.warn(`Method not allowed - ${request.method}`);
    throw new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    logger.error(`Invalid JSON - ${e.message}`);
    throw responseBadRequest([], e.message);
  });

  try {
    await cancelMemberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    const errors = mapJoiErrorToMemberError(error.details);
    logger.error(`Validation failed - ${JSON.stringify(errors)}`);
    return responseBadRequest(errors);
  }

  const { data: memberQueryResult } = await client.query({
    query: MEMBER_QUERY,
    variables: {
      identifier: {
        phoneNumber: `${TAIWAN_COUNTRY_PHONE_CODE}${body.cellphone}`,
      },
    },
  });

  if (!memberQueryResult.customerByIdentifier) {
    const error = [
      {
        field: "cellphone",
        code: "invalid_value",
        message: "Member not found. Please check your input and try again.",
      },
    ];
    logger.error(`Member not found - Phone: ${body.cellphone}`);
    return responseBadRequest(error);
  }

  const { data, errors } = await client.mutate({
    mutation: DELETE_CUSTOMER_MUTATION,
    variables: {
      id: memberQueryResult.customerByIdentifier.id,
    },
  });

  if (data?.customerDelete?.userErrors && data?.customerDelete?.userErrors?.length > 0) {
    if (
      data.customerDelete.userErrors[0].message !=
      "Customer can’t be deleted because they have associated orders"
    ) {
      const error = {
        field: "cellphone",
        code: "error",
        message: data.customerDelete.userErrors[0].message,
      };
      logger.error(`Customer deletion failed - ${JSON.stringify(error)}`);
      return responseBadRequest(error);
    } else {
      const { errors } = await client.mutate({
        mutation: UPDATE_CUSTOMER_MUTATION,
        variables: {
          input: {
            id: memberQueryResult.customerByIdentifier.id,
            metafields: [
              {
                key: "status",
                type: "single_line_text_field",
                value: CustomerStatus.DELETED,
              },
            ],
          },
        },
      });

      if (errors) {
        logger.error(`Customer update failed - ${JSON.stringify(errors)}`);
        throw Response.json(errors, { status: 500 });
      }

      logger.info(`Member status updated to deleted - ID: ${memberQueryResult.customerByIdentifier.id}`);
      return responseSuccess({}, "The member has been successfully canceled.");
    }
  }

  if (errors) {
    logger.error(`Customer deletion failed - ${JSON.stringify(errors)}`);
    throw Response.json(errors, { status: 500 });
  }

  await db.membershipCancel.create({
    data: {
      cancelDate: dayjs(body.cancelDate).toDate(),
      customerId: memberQueryResult.customerByIdentifier.id,
      cellphone: body.cellphone,
    },
  });

  logger.info(`Member canceled successfully - ID: ${memberQueryResult.customerByIdentifier.id} - Cellphone: ${body.cellphone}`);
  return responseSuccess({}, "The member has been successfully canceled.");
}
