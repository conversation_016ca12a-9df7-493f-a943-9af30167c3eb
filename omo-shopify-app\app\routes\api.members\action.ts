import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ActionFunctionArgs } from "@remix-run/node";
import logger from "@/logger.server";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  memberSchema,
} from "./member.schema";
import { CREATE_CUSTOMER_MUTATION } from "./mutation";

export async function action({ request }: ActionFunctionArgs) {
  const { method } = request;

  if (method !== "POST") {
    const result = new Response(null, { status: 405 });
    logger.warn(`Method not allowed - ${JSON.stringify({ method, status: 405 })}`);
    return result;
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await memberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    logger.error(`Validation failed - ${JSON.stringify(error.details)}`);
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  const bodyData = mapToShopifyInput(body);

  const { data, errors } = await client.mutate({
    mutation: CREATE_CUSTOMER_MUTATION,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    logger.error(`GraphQL error - ${JSON.stringify(errors)}`);
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const { userErrors } = data.customerCreate;

  if (userErrors && userErrors.length > 0) {
    logger.error(`Shopify user errors - ${JSON.stringify(userErrors)}`);
    return responseBadRequest(mapErrors(userErrors));
  }

  const result = Response.json({}, { status: 201 });
  logger.info(`Customer created - Status: 201, ID: ${data.customerCreate?.customer?.id ?? "unknown"}`);
  return result;
}
