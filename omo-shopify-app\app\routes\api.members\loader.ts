import { TAIWAN_COUNTRY_PHONE_CODE } from "@/constants";
import db from "@/db.server";
import logger from "@/logger.server";
import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import responseSuccess from "@/utils/response.success";
import { LoaderFunctionArgs } from "@remix-run/node";
import Joi from "joi";
import { mapJoiErrorToMemberError, phoneSchema, transformCustomerData } from "./member.schema";
import { MEMBER_QUERY } from "./query";

export async function loader({ request }: LoaderFunctionArgs) {
  const { client, shopByToken: shop } = await verifyToken(request);

  const url = new URL(request.url);

  const cellphone = url.searchParams.get("cellphone");

  const { error } = Joi.object({
    cellphone: phoneSchema,
  }).validate({ cellphone });

  if (error) {
    const errorDetails = mapJoiErrorToMemberError(error.details);
    logger.error(`Validation failed - ${JSON.stringify(errorDetails, null, 2)}`);
    return responseBadRequest(errorDetails);
  }

  const result = await client.query({
    query: MEMBER_QUERY,
    variables: {
      identifier: { phoneNumber: `${TAIWAN_COUNTRY_PHONE_CODE}${cellphone}` },
    },
  });

  if (!result.data.customerByIdentifier) {
    const notFoundError = [
      {
        field: "cellphone",
        code: "invalid_value",
        message: "Member not found. Please check your input and try again.",
      },
    ];
    logger.warn(`Member not found - Phone: ${cellphone}`);
    return responseBadRequest(notFoundError);
  }

  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      isActive: true,
    },
    include: {
      points: true,
    },
  });

  const memberData = {
    ...transformCustomerData(result.data.customerByIdentifier),
    memberTierStartDate: "",
    memberTierEndDate: "",
    pointsNeededForRedemption: loyaltyProgram?.points?.pointsRedemptionAmount,
    redemptionValue: loyaltyProgram?.points?.pointsRedemptionValue,
    maxRedeemPercentage: loyaltyProgram?.points?.maxRedeemPercentage,
    minPurchaseAmount: loyaltyProgram?.points?.minPurchaseAmount ?? 0,
  };
  logger.info(`Member found - Phone: ${cellphone}`);
  return responseSuccess(memberData);
}
