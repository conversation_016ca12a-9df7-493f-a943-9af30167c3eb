import { gql } from "@apollo/client/core";

export const MEMBER_QUERY = gql`
  query ($identifier: CustomerIdentifierInput!) {
    customerByIdentifier(identifier: $identifier) {
      id
      email
      phone
      taxExempt
      emailMarketingConsent {
        marketingState
        marketingOptInLevel
        consentUpdatedAt
      }
      firstName
      lastName
      amountSpent {
        amount
        currencyCode
      }
      smsMarketingConsent {
        marketingState
        marketingOptInLevel
      }
      addresses {
        address1
        city
        country
        phone
        zip
      }
      metafields(first: 20, namespace: "$app") {
        nodes {
          id
          key
          namespace
          value
        }
      }
    }
  }
`;
