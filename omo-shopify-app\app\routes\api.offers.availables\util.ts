import { DiscountTypeName } from "@/constants";

export enum DiscountType {
  None = "None",
  ProductDiscount = "ProductDiscount",
  OrderDiscount = "OrderDiscount",
  BuyXgetY = "BuyXgetY",
  ShippingFree = "ShippingFree",
  Giftcard = "Giftcard",
}

export enum DiscountClass {
  Product = "PRODUCT",
  Order = "ORDER",
  Shipping = "SHIPPING",
}

export interface CollectionInterface {
  id: string;
  title: string;
  products: {
    nodes: {
      id: string;
      title: string;
      variants: {
        nodes: {
          title: string;
          sku: string;
          product: {
            title: string;
          };
        }[];
      };
    }[];
  };
}

export interface ProductInterface {
  id: string;
  title: string;
  variants: {
    nodes: {
      id: string;
      sku: string | null;
      title: string;
    }[];
  };
}

export interface DiscountInterface {
  id: string;
  title: string;
  endsAt: string;
  asyncUsageCount: number;
  discountClass: DiscountClass;
  __typename: DiscountTypeName;
  codes: { nodes: { code: string }[] };
  customerGets: {
    items: {
      products: {
        nodes: {
          id: string;
          title: string;
        }[];
      };
      productVariants: {
        nodes: {
          title: string;
          sku: string;
          product: {
            title: string;
          };
        }[];
      };
      collections: {
        nodes: {
          id: string;
        }[];
      };
    };
    value: {
      __typename: "DiscountOnQuantity";
      quantity: {
        quantity: number;
      };
      effect: {
        percentage: number;
        amount: {
          amount: number;
        };
      };
      amount: {
        amount: number;
      };
      percentage: number;
    };
  };
  customerBuys: {
    value: {
      quantity: number;
      amount: number;
    };
    items: {
      products: {
        edges: {
          node: {
            id: string;
            title: string;
          };
        }[];
      };
      productVariants: {
        edges: {
          node: {
            title: string;
            sku: string;
            product: {
              title: string;
            };
          };
        }[];
      };
      collections: {
        edges: {
          node: {
            title: string;
            id: string;
          };
        }[];
      };
    };
  };
  minimumRequirement: {
    greaterThanOrEqualToSubtotal: {
      amount: number;
    };
    greaterThanOrEqualToQuantity: number;
  };
  customerSelection: {
    segments: {
      name: string;
      id: string;
    }[];
    customers: {
      id: string;
    }[];
  };
}

export const getDiscountType = ({
  discountClass,
  __typename: typename,
}: DiscountInterface): DiscountType => {
  if (
    typename === DiscountTypeName.DiscountAutomaticBxgy ||
    typename === DiscountTypeName.DiscountCodeBxgy
  ) {
    return DiscountType.BuyXgetY;
  }

  if (discountClass === DiscountClass.Product) {
    return DiscountType.ProductDiscount;
  } else if (discountClass === DiscountClass.Order) {
    return DiscountType.OrderDiscount;
  } else {
    return DiscountType.ShippingFree;
  }
};

export const getDiscountPrice = (discount: DiscountInterface): number | null => {
  return discount?.customerGets?.value?.amount?.amount ?? null;
};

export const getDiscountPercentage = (discount: DiscountInterface): number | null => {
  return discount?.customerGets?.value?.percentage ?? null;
};

export const getIsGift = (discount: DiscountInterface) => {
  const { __typename, customerGets } = discount;
  if (
    __typename !== DiscountTypeName.DiscountCodeBxgy &&
    __typename !== DiscountTypeName.DiscountAutomaticBxgy
  ) {
    return false;
  }

  if (customerGets.value.effect.percentage) {
    return true;
  }
  return false;
};

export const getUsingMinQty = (discount: DiscountInterface) => {
  const { __typename, customerBuys, minimumRequirement } = discount;
  if (
    __typename === DiscountTypeName.DiscountAutomaticBxgy ||
    __typename === DiscountTypeName.DiscountCodeBxgy
  ) {
    return customerBuys?.value?.quantity ?? null;
  }
  return minimumRequirement?.greaterThanOrEqualToQuantity ?? null;
};

export const getUsingMinPrice = (discount: DiscountInterface) => {
  const { minimumRequirement } = discount;
  return minimumRequirement?.greaterThanOrEqualToSubtotal?.amount ?? null;
};

export const getGiftQty = (discount: DiscountInterface) => {
  const { customerGets } = discount;
  return customerGets?.value?.quantity?.quantity ?? null;
};

export const getGiftDiscountPercentage = (discount: DiscountInterface) => {
  const { customerGets } = discount;
  return customerGets?.value?.effect?.percentage ?? null;
};

export const getGiftDiscountAmount = (discount: DiscountInterface) => {
  const { customerGets } = discount;
  return customerGets?.value?.effect?.amount?.amount ?? null;
};

export const getProducts = (
  discount: DiscountInterface,
  collections?: CollectionInterface[],
  products?: ProductInterface[],
) => {
  const { customerBuys } = discount;

  return [
    ...(customerBuys?.items?.products?.edges?.flatMap((edge) => {
      const product = products?.find((product) => product.id === edge.node.id);
      return product?.variants?.nodes?.map((variant) => ({
        productName: product?.title + " - " + variant?.title,
        productSKU: variant?.sku ?? null,
      }));
    }) ?? []),

    ...(customerBuys?.items?.productVariants?.edges?.map((edge) => ({
      productName: edge.node?.product?.title + " - " + edge.node?.title,
      productSKU: edge.node.sku,
    })) ?? []),

    ...(customerBuys?.items?.collections?.edges
      ?.flatMap((node) => {
        return collections
          ?.find((collection) => collection.id === node.node.id)
          ?.products?.nodes?.map((productInCollection) => {
            const { variants } = productInCollection;
            if (variants) {
              return variants.nodes.map((variant) => ({
                productName: productInCollection.title + " - " + variant.title,
                productSKU: variant.sku ?? null,
              }));
            } else {
              const product = products?.find((product) => product.id == productInCollection.id);

              if (product) {
                return product.variants.nodes.map((variant) => ({
                  productName: product.title + " - " + variant.title,
                  productSKU: variant.sku ?? null,
                }));
              }

              return [
                {
                  productName: productInCollection.title,
                  productSKU: null,
                },
              ];
            }
          });
      })
      .flat() ?? []),
  ];
};

export const getProductQty = (discount: DiscountInterface) => {
  const { customerBuys } = discount;

  return customerBuys?.value?.quantity ?? null;
};

export const getGifts = (
  discount: DiscountInterface,
  collections?: CollectionInterface[],
  products?: ProductInterface[],
) => {
  const { customerGets } = discount;

  return [
    ...(customerGets?.items?.products?.nodes.map((node) => {
      const product = products?.find((product) => product.id === node.id);
      return product?.variants?.nodes?.map((variant) => ({
        giftName: product?.title + " - " + variant?.title,
        giftSKU: variant?.sku ?? null,
      }));
    }) ?? []),

    ...(customerGets?.items?.productVariants?.nodes.map((node) => ({
      giftName: node.product?.title + " - " + node.title,
      giftSKU: node.sku ?? null,
    })) ?? []),

    ...(customerGets?.items?.collections?.nodes
      ?.flatMap((node) => {
        return collections
          ?.find((collection) => collection.id === node.id)
          ?.products?.nodes?.map((productInCollection) => {
            const { variants } = productInCollection;
            if (variants) {
              return variants.nodes.map((variant) => ({
                giftName: productInCollection?.title + " - " + variant.title,
                giftSKU: variant.sku ?? null,
              }));
            } else {
              const product = products?.find((product) => product.id == productInCollection.id);

              if (product) {
                return product.variants.nodes.map((variant) => ({
                  productName: product.title + " - " + variant.title,
                  productSKU: variant.sku ?? null,
                }));
              }

              return [
                {
                  giftName: productInCollection.title,
                  giftSKU: null,
                },
              ];
            }
          });
      })
      .flat() ?? []),
  ];
};

export const parseIfNumber = (value: string): number | string => {
  const parsed = Number(value);
  return isNaN(parsed) ? value : parsed;
};

export const getMinPurchaseAmount = (discount: DiscountInterface) => {
  const { customerBuys } = discount;

  return customerBuys?.value?.amount ?? null;
};
