import logger from "@/logger.server";
import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ActionFunctionArgs } from "@remix-run/node";
import { orderSchema } from "./order.schema";
import { handleReturnOrder, processSalesOrder } from "./service";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    logger.warn(`[Orders/Action] Method not allowed - ${request.method}`);
    throw new Response(null, { status: 405 });
  }

  const { client, shopByToken: shop } = await verifyToken(request);

  const createOrderInputDto = await request.json().catch((e) => {
    const error = e as Error;
    logger.error(`[Orders/Action] Failed to parse request body - ${error.message}`);
    throw responseBadRequest([], error.message);
  });

  // Validate the request body
  const errorCodeMap = {
    "any.required": "missing_field",
    "string.empty": "invalid_value",
    "string.pattern.base": "invalid_value",
    "number.base": "invalid_value",
    "number.positive": "invalid_value",
    "date.format": "invalid_format",
    "any.only": "invalid_value",
    "any.unknown": "invalid_value",
  };
  try {
    await orderSchema.validateAsync(createOrderInputDto, { abortEarly: false });
  } catch (error) {
    const validationError = error as {
      details: {
        path: string[];
        type: keyof typeof errorCodeMap;
        message: string;
      }[];
    };

    const validationErrors = validationError.details.map((errorDetail) => ({
      field: errorDetail.path[0],
      code: errorCodeMap[errorDetail.type],
      message: errorDetail.message,
    }));

    // Validate that product SKUs are unique
    const uniqueProductSkus = new Set<string>();
    for (const product of createOrderInputDto.products) {
      if (uniqueProductSkus.has(product.productSku)) {
        validationErrors.push({
          field: "products",
          code: "invalid_value",
          message: "Product SKUs must be unique.",
        });
      }
      uniqueProductSkus.add(product.productSku);
    }

    logger.error(`[Orders/Action] Request validation failed - ${JSON.stringify(validationErrors)}`);
    return responseBadRequest(validationErrors);
  }

  if (createOrderInputDto.purchaseType === "Sales") {
    logger.info(
      `[Orders/Action] Processing sales order - orderId: ${createOrderInputDto.orderId || "N/A"}`,
    );
    return processSalesOrder(client, shop, createOrderInputDto);
  } else {
    logger.info(
      `[Orders/Action] Processing return order - orderId: ${createOrderInputDto.orderId || "N/A"}`,
    );
    return handleReturnOrder(client, createOrderInputDto);
  }
}
