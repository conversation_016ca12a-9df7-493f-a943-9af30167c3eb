import { gql } from "@apollo/client/core";

export const DRAFT_ORDER_DELETE_MUTATION = gql`
  mutation draftOrderDelete($input: DraftOrderDeleteInput!) {
    draftOrderDelete(input: $input) {
      deletedId
      userErrors {
        message
        field
      }
    }
  }
`;

export const DRAFT_ORDER_CREATE_MUTATION = gql`
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      userErrors {
        field
        message
      }
      draftOrder {
        id
        customer {
          id
          email
          phone
        }
        discountCodes
        lineItems(first: 100) {
          nodes {
            id
            variant {
              id
            }
            sku
            title
            quantity
            originalUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalDiscountSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            discountedTotalSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
        totalLineItemsPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalDiscountsSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        metafields(first: 20, namespace: "$app") {
          nodes {
            namespace
            key
            type
            value
          }
        }
      }
    }
  }
`;

export const DRAFT_ORDER_COMPLETE_MUTATION = gql`
  mutation draftOrderComplete($id: ID!) {
    draftOrderComplete(id: $id) {
      draftOrder {
        id
        metafield(key: "order_id") {
          value
        }
        order {
          id
          lineItems(first: 100) {
            nodes {
              id
              sku
            }
          }
        }
      }
    }
  }
`;

export const METAFIELDS_SET_MUTATION = gql`
  mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const ORDER_MARK_AS_PAID_MUTATION = gql`
  mutation OrderMarkAsPaid($input: OrderMarkAsPaidInput!) {
    orderMarkAsPaid(input: $input) {
      order {
        id
        fullyPaid
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const UPDATE_CUSTOMER_MUTATION = gql`
  mutation UpdateCustomer($input: CustomerInput!) {
    customerUpdate(input: $input) {
      customer {
        id
        metafields(first: 20, namespace: "$app") {
          nodes {
            key
            value
            type
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const REFUND_CREATE_MUTATION = gql`
  mutation M($input: RefundInput!) {
    refundCreate(input: $input) {
      userErrors {
        field
        message
      }
      order {
        id
        metafields(first: 20, namespace: "$app") {
          nodes {
            namespace
            key
            type
            value
          }
        }
      }
      refund {
        id
        refundLineItems(first: 100) {
          nodes {
            id
            lineItem {
              id
              sku
              name
            }
            quantity
            priceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
        totalRefundedSet {
          shopMoney {
            amount
            currencyCode
          }
        }
      }
    }
  }
`;

export const UPDATE_DRAFT_ORDER_MUTATION = gql`
  mutation updateDraftOrder($id: ID!, $input: DraftOrderInput!) {
    draftOrderUpdate(id: $id, input: $input) {
      userErrors {
        field
        message
      }
      draftOrder {
        id
      }
    }
  }
`;
