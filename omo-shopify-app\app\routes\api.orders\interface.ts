interface MoneyInterface {
  amount: string | number;
  currencyCode: string;
}

interface MetafieldNodeInterface {
  key: string;
  type: string;
  value: string;
}

export interface CreateDraftOrderMutationResponseInterface {
  draftOrderCreate: {
    userErrors: {
      field: string[];
      message: string;
    }[];
    draftOrder: {
      id: string;
      customer?: {
        id: string;
        email: string;
        phone: string;
      };
      discountCodes: string[];
      lineItems: {
        nodes: {
          id: string;
          variant: {
            id: string;
          };
          sku: string;
          title: string;
          quantity: number;
          originalUnitPriceSet: {
            shopMoney: MoneyInterface;
          };
          totalDiscountSet: {
            shopMoney: MoneyInterface;
          };
          discountedTotalSet: {
            shopMoney: MoneyInterface;
          };
          taxLines: {
            title: string;
            rate: number;
            priceSet: {
              shopMoney: MoneyInterface;
            };
          }[];
        }[];
      };
      totalLineItemsPriceSet: {
        shopMoney: MoneyInterface;
      };
      totalDiscountsSet: {
        shopMoney: MoneyInterface;
      };
      totalPriceSet: {
        shopMoney: MoneyInterface;
      };
      metafields: {
        nodes: MetafieldNodeInterface[];
      };
    };
  };
}

export interface CreateDraftOrderMutationInputInterface {
  input: {
    purchasingEntity?: {
      customerId: string;
    };
    taxExempt: boolean;
    lineItems: {
      title?: string;
      sku: string;
      variantId?: string;
      originalUnitPriceWithCurrency?: MoneyInterface;
      priceOverride?: MoneyInterface;
      quantity: number;
    }[];
    discountCodes?: string[];
    metafields: MetafieldNodeInterface[];
    appliedDiscount?: {
      value: number;
      valueType: "FIXED_AMOUNT" | "PERCENTAGE";
      title: string;
    };
  };
}

export interface DraftOrderCompleteMutationInputInterface {
  id: string;
}

export interface DraftOrderCompleteMutationResponseInterface {
  draftOrderComplete: {
    userErrors: {
      field: string[];
      message: string;
    }[];
    draftOrder: {
      id: string;
      metafield: {
        value: string;
      };
      order: {
        id: string;
        lineItems: {
          nodes: {
            id: string;
            sku: string;
          }[];
        };
        // metafields: {
        //   nodes: MetafieldNode[]
        // }
      };
    };
  };
}

export interface RefundCreateMutationInputInterface {
  input: {
    orderId: string;
    refundLineItems: {
      lineItemId: string;
      quantity: number;
    }[];
    transactions: {
      amount: number;
      gateway: string;
      kind: "REFUND";
      orderId: string;
      parentId?: string;
    }[];
  };
}

export interface RefundCreateMutationResponseInterface {
  refundCreate: {
    userErrors: {
      field: string[];
      message: string;
    }[];
    order: {
      id: string;
      metafields: {
        nodes: MetafieldNodeInterface[];
      };
    };
    refund: {
      id: string;
      refundLineItems: {
        nodes: {
          id: string;
          lineItem: {
            id: string;
            name: string;
            sku: string;
          };
          quantity: number;
          priceSet: {
            shopMoney: MoneyInterface;
          };
        }[];
      };
      totalRefundedSet: {
        shopMoney: MoneyInterface;
      };
    };
  };
}

export interface SetMetafieldsMutationInputInterface {
  metafields: {
    key: string;
    namespace: string;
    ownerId: string;
    type: string;
    value: string;
  }[];
}

interface MoneyV2Interface {
  amount: string;
  currencyCode: string;
}

interface MetafieldNodeInterface {
  namespace?: string;
  key: string;
  type: string;
  value: string;
}

export interface QueryOrderWithLineItemsInputInterface {
  id: string;
}

export interface QueryOrderWithLineItemsResponseInterface {
  order: {
    id: string;
    lineItems: {
      nodes: {
        id: string;
        variant: {
          id: string;
        };
        sku: string;
      }[];
    };
  };
}

export interface QueryOrderWithSuggestedRefundInputInterface {
  id: string;
  refundLineItems: {
    lineItemId: string;
    quantity: number;
  }[];
}

export interface QueryOrderWithSuggestedRefundResponseInterface {
  order: {
    id: string;
    customer?: {
      id: string;
    };
    discountCodes: string[];
    suggestedRefund: {
      refundLineItems: {
        lineItem: {
          id: string;
          variant: {
            id: string;
          };
          sku: string;
          title: string;
          currentQuantity: number;
          originalUnitPriceSet: {
            shopMoney: MoneyV2Interface;
          };
          totalDiscountSet: {
            shopMoney: MoneyV2Interface;
          };
          discountedTotalSet: {
            shopMoney: MoneyV2Interface;
          };
        };
        quantity: number;
      }[];
      subtotalSet: {
        shopMoney: MoneyV2Interface;
      };
      discountedSubtotalSet: {
        shopMoney: MoneyV2Interface;
      };
      amountSet: {
        shopMoney: MoneyV2Interface;
      };
    };
    metafields: {
      nodes: MetafieldNodeInterface[];
    };
  };
}

export interface CreateOrderInputDtoInterface {
  cellphone?: string;
  orderId: string;
  purchaseType: "Sales" | "Return";
  orderDate: string;
  products: Array<{
    qty: number;
    productSku: string;
    productName?: string;
    unitPrice?: number;
  }>;
  totalOrderAmount: number;
  discountCodes?: string[];
  loyaltyPoint?: number;
  loyaltyPointDiscountAmount?: number;
  discountAmount?: number;
  actualOrderAmount: number;
  locationID: string;
  staffID: string;
  invoice?: string;
  relativeOrderId?: string;
}

export interface CustomerInterface {
  id: string;
  email: string;
  metafields: { nodes: { namespace: string; key: string; value: string }[] };
}
