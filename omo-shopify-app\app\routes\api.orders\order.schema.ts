import { MetafieldType, OrderMetafield } from "@/constants";
import { LoyaltyPoints } from "@prisma/client";
import Jo<PERSON> from "joi";
import {
  CreateDraftOrderMutationInputInterface,
  CreateDraftOrderMutationResponseInterface,
  CreateOrderInputDtoInterface,
  QueryOrderWithSuggestedRefundResponseInterface,
  RefundCreateMutationInputInterface,
  RefundCreateMutationResponseInterface,
} from "./interface";

export const orderSchema = Joi.object<CreateOrderInputDtoInterface>({
  cellphone: Joi.string()
    .pattern(/^09\d{8}$/)
    .optional()
    // .required()
    .allow("")
    .messages({
      "string.pattern.base": "The 'cellphone' field must be a valid phone number",
    }),
  orderId: Joi.string().required().messages({
    "any.required": "The 'orderId' field is required",
    "string.empty": "The 'orderId' field cannot be empty",
  }),
  purchaseType: Joi.string().valid("Sales", "Return").required().messages({
    "any.required": "The 'purchaseType' field is required",
    "any.only": "The 'purchaseType' must be either 'Sales' or 'Return'",
  }),
  orderDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\+\d{2}:\d{2})?$/)
    .required()
    .messages({
      "any.required": "The 'orderDate' field is required",
      "date.format":
        "The 'orderDate' field must be in 'YYYY-MM-DDThh:mm:ss' format, optionally followed by a timezone offset like '+08:00'",
    }),
  products: Joi.array()
    .items(
      Joi.object({
        qty: Joi.number().integer().positive().required().messages({
          "any.required": "The 'qty' field is required",
          "number.base": "The 'qty' field must be a number",
          "number.positive": "The 'qty' field must be a positive number",
        }),
        productSku: Joi.string().required().messages({
          "any.required": "The 'productSku' field is required",
          "string.empty": "The 'productSku' field cannot be empty",
        }),
        productName: Joi.string().optional(),
        unitPrice: Joi.number().positive().optional(),
      }),
    )
    .required()
    .messages({
      "any.required": "The 'products' field is required",
    }),
  totalOrderAmount: Joi.when("purchaseType", {
    is: "Sales",
    then: Joi.number().required().messages({
      "any.required": "The 'totalOrderAmount' field is required for Sales orders",
      "number.base": "The 'totalOrderAmount' field must be a number",
      "number.positive": "The 'totalOrderAmount' field must be a positive number",
    }),
    otherwise: Joi.forbidden().messages({
      "any.unknown": "The 'actualOrderAmount' field must not be defined for Sales orders",
    }),
  }),
  discountCodes: Joi.when("purchaseType", {
    is: "Sales",
    then: Joi.array().items(Joi.string()).optional().messages({
      "array.base": "The 'discountCodes' field must be an array of strings",
      "string.base": "Each 'discountCode' must be a string",
      "string.empty": "Each 'discountCode' cannot be empty",
    }),
    otherwise: Joi.forbidden().messages({
      "any.unknown": "The 'actualOrderAmount' field must not be defined for Sales orders",
    }),
  }),
  loyaltyPoint: Joi.number().integer().min(1).messages({
    "number.base": "The 'loyaltyPoint' field must be a number",
    "number.integer": "The 'loyaltyPoint' field must be an integer",
    "number.min": "The 'loyaltyPoint' field must be at least 1",
  }),
  loyaltyPointDiscountAmount: Joi.number().min(1).messages({
    "number.base": "The 'loyaltyPointDiscountAmount' field must be a number",
    "number.min": "The 'loyaltyPointDiscountAmount' field must be at least 1",
  }),
  discountAmount: Joi.when("purchaseType", {
    is: "Sales",
    then: Joi.number().required().messages({
      "any.required": "The 'discountAmount' field is required for Sales orders",
      "number.base": "The 'discountAmount' field must be a number",
      "number.positive": "The 'discountAmount' field must be a positive number",
    }),
    otherwise: Joi.forbidden().messages({
      "any.unknown": "The 'discountAmount' field must not be defined for Sales orders",
    }),
  }),
  actualOrderAmount: Joi.number().required().messages({
    "any.required": "The 'actualOrderAmount' field is required",
    "number.base": "The 'actualOrderAmount' field must be a number",
    "number.positive": "The 'actualOrderAmount' field must be a positive number",
  }),
  locationID: Joi.string().required().messages({
    "any.required": "The 'locationID' field is required",
    "string.empty": "The 'locationID' field cannot be empty",
  }),
  staffID: Joi.string().required().messages({
    "any.required": "The 'staffID' field is required",
    "string.empty": "The 'staffID' field cannot be empty",
  }),
  invoice: Joi.string().optional(),
  relativeOrderId: Joi.when("purchaseType", {
    is: "Sales",
    then: Joi.forbidden().messages({
      "any.unknown": "The 'relativeOrderId' field must not be defined for Sales orders",
    }),
    otherwise: Joi.string().required().messages({
      "any.required": "The 'relativeOrderId' field is required for Return orders",
      "string.empty": "The 'relativeOrderId' field cannot be empty",
    }),
  }),
})
  .and("loyaltyPoint", "loyaltyPointDiscountAmount")
  .messages({
    "object.and":
      "Both 'loyaltyPoint' and 'loyaltyPointDiscountAmount' must be provided together and greater than 0",
  });

export const orderMetafields = (
  createOrderInputDto: CreateOrderInputDtoInterface,
): {
  key: string;
  type: string;
  value: string;
}[] => [
  {
    key: OrderMetafield.ORDER_ID,
    type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
    value: createOrderInputDto.orderId,
  },
  {
    key: OrderMetafield.ORDER_DATE,
    type: MetafieldType.DATE_TIME,
    value: createOrderInputDto.orderDate,
  },
  ...(createOrderInputDto.invoice
    ? [
        {
          key: OrderMetafield.INVOICE,
          type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
          value: createOrderInputDto.invoice,
        },
      ]
    : []),
  ...(createOrderInputDto.totalOrderAmount
    ? [
        {
          key: OrderMetafield.TOTAL_ORDER_AMOUNT,
          type: MetafieldType.NUMBER_DECIMAL,
          value: String(createOrderInputDto.totalOrderAmount),
        },
      ]
    : []),
  ...(createOrderInputDto.discountAmount
    ? [
        {
          key: OrderMetafield.DISCOUNT_AMOUNT,
          type: MetafieldType.NUMBER_DECIMAL,
          value: String(createOrderInputDto.discountAmount),
        },
      ]
    : []),
  ...(createOrderInputDto.actualOrderAmount
    ? [
        {
          key: OrderMetafield.ACTUAL_ORDER_AMOUNT,
          type: MetafieldType.NUMBER_DECIMAL,
          value: String(createOrderInputDto.actualOrderAmount),
        },
      ]
    : []),
  {
    key: OrderMetafield.LOCATION_ID,
    type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
    value: createOrderInputDto.locationID,
  },
  {
    key: OrderMetafield.STAFF_ID,
    type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
    value: createOrderInputDto.staffID,
  },
  {
    key: OrderMetafield.REDEEM_POINTS,
    type: MetafieldType.NUMBER_INTEGER,
    value: createOrderInputDto.loyaltyPoint?.toString() ?? "0",
  },
];

export const transformOrderToCreateDraftInput = (
  createOrderInputDto: CreateOrderInputDtoInterface,
  memberId: string | undefined,
  productVariants: Array<{
    variantId?: string;
    productId?: string;
    productName?: string;
    unitPrice?: number;
    qty: number;
    productSku: string;
  }>,
  loyaltyPoint?: LoyaltyPoints | null,
): CreateDraftOrderMutationInputInterface => ({
  input: {
    purchasingEntity: memberId
      ? {
          customerId: memberId,
        }
      : undefined,
    taxExempt: true,
    lineItems: productVariants.map((productVariant) => ({
      title: productVariant.productName,
      sku: productVariant.productSku,
      variantId: productVariant.variantId,
      originalUnitPriceWithCurrency: productVariant.unitPrice
        ? {
            amount: productVariant.unitPrice,
            currencyCode: "TWD",
          }
        : undefined,
      priceOverride: productVariant.unitPrice
        ? {
            amount: productVariant.unitPrice,
            currencyCode: "TWD",
          }
        : undefined,
      quantity: productVariant.qty,
    })),
    discountCodes: createOrderInputDto.discountCodes,
    metafields: orderMetafields(createOrderInputDto),
    appliedDiscount: createOrderInputDto.loyaltyPointDiscountAmount
      ? {
          value: createOrderInputDto.loyaltyPointDiscountAmount,
          valueType: "FIXED_AMOUNT",
          title: loyaltyPoint?.programName ?? "",
        }
      : undefined,
  },
});

export const transformOrderFromCreateDraftResponse = (
  response: CreateDraftOrderMutationResponseInterface,
) => {
  const draftOrder = response.draftOrderCreate.draftOrder;
  return {
    cellphone: draftOrder.customer?.phone,
    purchaseType: "Sales",
    orderId: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.ORDER_ID,
    )?.value,
    // shopifyOrderId: order.id,
    orderDate: draftOrder.metafields.nodes.find(
      (metafield) => metafield.key === OrderMetafield.ORDER_DATE,
    )?.value,
    products: draftOrder.lineItems.nodes.map((lineItem) => ({
      qty: lineItem.quantity,
      productSku: lineItem.sku,
      productName: lineItem.title,
      unitPrice: Number(lineItem.originalUnitPriceSet.shopMoney.amount),
    })),
    totalOrderAmount: Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount),
    discountCodes: draftOrder.discountCodes,
    loyaltyPoint: undefined, //TODO: get from CRM
    loyaltyPointDiscountAmount: undefined, //TODO: get from CRM
    discountAmount: Number(draftOrder.totalDiscountsSet.shopMoney.amount),
    actualOrderAmount: Number(draftOrder.totalPriceSet.shopMoney.amount),
    locationID: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.LOCATION_ID,
    )?.value,
    staffID: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.STAFF_ID,
    )?.value,
    invoice: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.INVOICE,
    )?.value,
    relativeOrderId: undefined,
  };
};

export const transformRelativeOrderToRefundInput = (
  relativeOrder: QueryOrderWithSuggestedRefundResponseInterface["order"],
): RefundCreateMutationInputInterface => ({
  input: {
    orderId: relativeOrder.id,
    refundLineItems: relativeOrder.suggestedRefund.refundLineItems.map((refundLineItem) => ({
      lineItemId: refundLineItem.lineItem.id,
      quantity: refundLineItem.quantity,
    })),
    transactions: [
      {
        amount: Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount),
        gateway: "cash",
        kind: "REFUND",
        orderId: relativeOrder.id,
      },
    ],
  },
});

export const transformRefundCreateOutputToResponse = (
  refundCreate: RefundCreateMutationResponseInterface["refundCreate"],
  orderId: string,
) => ({
  cellphone: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "cellphone",
  )?.value,
  purchaseType: "Return",
  orderId: orderId,
  orderDate: refundCreate.order.metafields.nodes.find(
    (metafield) => metafield.key === OrderMetafield.ORDER_DATE,
  )?.value,
  products: refundCreate.refund.refundLineItems.nodes.map((lineItem) => ({
    qty: lineItem.quantity,
    productSku: lineItem.lineItem.sku,
    productName: lineItem.lineItem.name,
    unitPrice: Number(lineItem.priceSet.shopMoney.amount),
  })),
  totalOrderAmount: undefined,
  discountCodes: undefined, // Assuming discount codes are not available in refund response
  loyaltyPoint: undefined, //TODO: get from CRM
  loyaltyPointDiscountAmount: undefined, //TODO: get from CRM
  discountAmount: undefined, // Assuming discount amount is not available in refund response
  actualOrderAmount: -Number(refundCreate.refund.totalRefundedSet.shopMoney.amount),
  locationID: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.LOCATION_ID,
  )?.value,
  staffID: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.STAFF_ID,
  )?.value,
  invoice: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.INVOICE,
  )?.value,
  relativeOrderId: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === OrderMetafield.ORDER_ID,
  )?.value,
});
