import { gql } from "@apollo/client/core";

export const CUSTOMER_BY_CELLPHONE_QUERY = gql`
  query ($identifier: CustomerIdentifierInput!) {
    customerByIdentifier(identifier: $identifier) {
      id
      phone
      email
      metafields(first: 20, namespace: "$app") {
        nodes {
          namespace
          key
          value
        }
      }
    }
  }
`;

export const DISCOUNT_BY_CODE_QUERY = gql`
  query codeDiscountNodeByCode($code: String!) {
    codeDiscountNodeByCode(code: $code) {
      id
      codeDiscount {
        __typename
        ... on DiscountCodeBasic {
          customerGets {
            value {
              __typename
              ... on DiscountOnQuantity {
                effect {
                  __typename
                }
              }
            }
          }
        }
        ... on DiscountCodeBxgy {
          customerGets {
            value {
              __typename
              ... on DiscountOnQuantity {
                effect {
                  __typename
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const ORDER_WITH_LINE_ITEMS_QUERY = gql`
  query OrderWithLineItems($id: ID!) {
    order(id: $id) {
      id
      lineItems(first: 100) {
        nodes {
          id
          variant {
            id
          }
          sku
        }
      }
    }
  }
`;

export const ORDER_WITH_SUGGESTED_REFUND_QUERY = gql`
  query SuggestedRefund($id: ID!, $refundLineItems: [RefundLineItemInput!]) {
    order(id: $id) {
      id
      customer {
        id
      }
      discountCodes
      suggestedRefund(refundLineItems: $refundLineItems) {
        refundLineItems {
          lineItem {
            id
            variant {
              id
            }
            sku
            title
            currentQuantity
            originalUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalDiscountSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            discountedTotalSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
          quantity
        }
        subtotalSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        discountedSubtotalSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        amountSet {
          shopMoney {
            amount
            currencyCode
          }
        }
      }
      metafields(first: 20, namespace: "$app") {
        nodes {
          namespace
          key
          type
          value
        }
      }
    }
  }
`;
