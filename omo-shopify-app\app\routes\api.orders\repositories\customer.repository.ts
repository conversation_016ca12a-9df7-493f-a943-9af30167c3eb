import responseBadRequest from "@/utils/response.badRequest";
import { ApolloClient } from "@apollo/client/core";
import { CustomerInterface } from "../interface";
import { CUSTOMER_BY_CELLPHONE_QUERY } from "../graphql/queries";
import { UPDATE_CUSTOMER_MUTATION } from "../graphql/mutations";
import logger from "@/logger.server";

export const findCustomerByPhone = async (
  client: ApolloClient<object>,
  phoneNumber: string,
): Promise<CustomerInterface> => {
  const {
    data: { customerByIdentifier },
  } = await client.query({
    query: CUSTOMER_BY_CELLPHONE_QUERY,
    variables: {
      identifier: { phoneNumber: phoneNumber },
    },
  });

  if (!customerByIdentifier) {
    throw responseBadRequest([
      {
        field: "cellphone",
        code: "not_found",
        message: "Member with the specified 'cellphone' is not found.",
      },
    ]);
  }
  return customerByIdentifier;
};

export const updateCustomerPoints = async (
  client: ApolloClient<object>,
  customerId: string,
  points: number,
) => {
  logger.debug(`Updating customer metafield - customerId: ${customerId}, points: ${points}`);
  
  await client
    .mutate({
      mutation: UPDATE_CUSTOMER_MUTATION,
      variables: {
        input: {
          id: customerId,
          metafields: [
            {
              key: "points",
              value: points.toString(),
              type: "number_integer",
            },
          ],
        },
      },
    })
    .catch((error) => {
      logger.error(
        `Failed to update customer metafield - customerId: ${customerId}, error: ${error}`,
      );
      throw Response.json(
        {
          field: "points",
          code: "update_failed",
          message: `Failed to update customer metafield - customerId: ${customerId}, error: ${error}`,
        },
        {
          status: 500,
        },
      );
    });
};
