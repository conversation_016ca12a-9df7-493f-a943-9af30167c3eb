import db from "@/db.server";
import logger from "@/logger.server";
import responseBadRequest from "@/utils/response.badRequest";
import { CreateOrderInputDtoInterface, DraftOrderCompleteMutationResponseInterface } from "../interface";

export const storeSalesOrderToDatabase = async (
  draftOrder: DraftOrderCompleteMutationResponseInterface["draftOrderComplete"]["draftOrder"],
) => {
  const orderPosId = draftOrder.metafield?.value;
  const orderId = draftOrder.order?.id;

  if (!orderPosId || !orderId) {
    const error = new Error(
      `Missing required fields: orderPosId=${orderPosId}, orderId=${orderId}`,
    );
    logger.error(error.message);
    throw error;
  }

  logger.debug(
    `Storing sales order to database - orderPosId: ${orderPosId}, shopifyOrderId: ${orderId}`,
  );

  try {
    await db.salesOrders.create({
      data: {
        orderPosId,
        ordShopiId: orderId,
      },
    });
    logger.info(
      `Successfully stored sales order - orderPosId: ${orderPosId}, shopifyOrderId: ${orderId}`,
    );
  } catch (error) {
    logger.error(`Failed to store sales order - orderPosId: ${orderPosId}, error: ${error}`);
    throw error;
  }
};

export const findOrderByRequestData = async (createOrderInputDto: CreateOrderInputDtoInterface) => {
  const orderId = createOrderInputDto.orderId;
  logger.debug(`Looking up order by request data - orderId: ${orderId}`);

  try {
    const result = await db.salesOrders.findFirstOrThrow({
      where: {
        orderPosId: orderId,
      },
      select: {
        id: true,
        ordShopiId: true,
      },
    });

    logger.debug(`Found order - orderId: ${orderId}, shopifyOrderId: ${result.ordShopiId}`);
    return result;
  } catch (error) {
    const message = `Sales order with ID '${orderId}' is not found`;
    logger.warn(message);
    throw responseBadRequest([
      {
        field: "orderId",
        code: "not_found",
        message,
      },
    ]);
  }
};

export const updateReturnOrderToDatabase = async (
  refundOrderId: string,
  refundOrderShopId: string,
  salesOrderLocalId: number,
) => {
  logger.debug(
    `Updating return order in database - refundOrderId: ${refundOrderId}, shopifyOrderId: ${refundOrderShopId}`,
  );

  try {
    const result = await db.returnOrders.create({
      data: {
        orderId: refundOrderId,
        retOrdShopId: refundOrderShopId,
        salesOrdId: salesOrderLocalId,
      },
    });

    logger.info(
      `Successfully updated return order - refundOrderId: ${refundOrderId}, shopifyOrderId: ${refundOrderShopId}`,
    );
    return result;
  } catch (error) {
    logger.error(
      `Failed to update return order - refundOrderId: ${refundOrderId}, error: ${error}`,
    );
    throw error;
  }
};
