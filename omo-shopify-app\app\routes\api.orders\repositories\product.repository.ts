import { ApolloClient, gql } from "@apollo/client/core";
import responseBadRequest from "@/utils/response.badRequest";
import { CreateOrderInputDtoInterface } from "../interface";

export const getProductVariantsFromSku = async (
  client: ApolloClient<object>,
  createOrderInputDto: CreateOrderInputDtoInterface,
) => {
  const productVariantsQueries = createOrderInputDto.products.map(
    (productVariant, index) => `
      productVariants${index}: productVariants(query: "${productVariant.productSku}", first: 1) {
        nodes {
          id
          sku
          title
          price
          product {
            id
            title
          }
        }
      }
    `,
  );

  const { data: productVariantsQueryResults } = await client.query({
    query: gql`
      query {
        ${productVariantsQueries.join("\n")}
      }
    `,
  });

  return Object.values(productVariantsQueryResults).map((productVariant, index) => {
    const variant = productVariant as {
      nodes: {
        id: string;
        sku: string;
        title: string;
        price: number;
        product: { id: string; title: string };
      }[];
    };
    
    if (variant.nodes.length === 0) {
      // Handle exception before a dummy item fails to be created
      const { unitPrice, productName } = createOrderInputDto.products[index];
      if (!unitPrice || !productName) {
        const errors = [];
        if (!unitPrice) {
          errors.push({
            field: "unitPrice",
            code: "missing_field",
            message:
              "'unitPrice' must be provided if product with the specified SKU is not yet declared in Shopify.",
          });
        }
        if (!productName) {
          errors.push({
            field: "productName",
            code: "missing_field",
            message:
              "'productName' must be provided if product with the specified SKU is not yet declared in Shopify.",
          });
        }
        throw responseBadRequest(errors);
      }
      return createOrderInputDto.products[index];
    } else {
      const {
        id: variantId,
        price: unitPrice,
        product: { id: productId, title: productName },
      } = variant.nodes[0];

      // Construct the product variant object to return
      return {
        variantId,
        productId,
        productName,
        unitPrice,
        ...createOrderInputDto.products[index],
      };
    }
  });
};
