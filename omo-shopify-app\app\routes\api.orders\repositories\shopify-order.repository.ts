import { ApolloClient } from "@apollo/client/core";
import logger from "@/logger.server";
import responseBadRequest from "@/utils/response.badRequest";
import { 
  QueryOrderWithLineItemsResponseInterface,
  QueryOrderWithSuggestedRefundInputInterface,
  QueryOrderWithSuggestedRefundResponseInterface,
  CreateOrderInputDtoInterface
} from "../interface";
import { 
  ORDER_WITH_LINE_ITEMS_QUERY, 
  ORDER_WITH_SUGGESTED_REFUND_QUERY 
} from "../graphql/queries";

export const findLineItemsFromShopify = async (
  client: ApolloClient<object>,
  salesOrdShopId: string,
): Promise<QueryOrderWithLineItemsResponseInterface["order"]["lineItems"]["nodes"]> => {
  logger.debug(`Fetching line items for shopify order ${salesOrdShopId}`);

  try {
    const result = await client.query({
      query: ORDER_WITH_LINE_ITEMS_QUERY,
      variables: { id: salesOrdShopId },
      fetchPolicy: "no-cache",
    });

    const lineItems = result.data?.order?.lineItems?.nodes || [];
    logger.debug(`Found ${lineItems.length} line items for shopify order ${salesOrdShopId}`);
    return lineItems;
  } catch (error) {
    logger.error(`Failed to fetch line items for shopify order ${salesOrdShopId}: ${error}`);
    throw error;
  }
};

export const findOrderWithSuggestedRefund = async (
  client: ApolloClient<object>,
  queryOrderInput: QueryOrderWithSuggestedRefundInputInterface,
): Promise<QueryOrderWithSuggestedRefundResponseInterface> => {
  return (
    await client
      .query({
        query: ORDER_WITH_SUGGESTED_REFUND_QUERY,
        variables: queryOrderInput,
      })
      .catch((error) => {
        if (error.graphQLErrors?.[0]?.path?.includes("order", "suggestedRefund")) {
          throw responseBadRequest([
            {
              field: "products",
              code: "exceeds_purchase_quantity",
              message: "Cannot refund more items than were purchased.",
            },
          ]);
        } else {
          throw error;
        }
      })
  ).data;
};

export const mapLineItemsToQueryOrderInput = (
  createOrderInputDto: CreateOrderInputDtoInterface,
  salesOrderLineItemNodes: QueryOrderWithLineItemsResponseInterface["order"]["lineItems"]["nodes"],
) => {
  return createOrderInputDto.products.map((product) => {
    const lineItem = salesOrderLineItemNodes.find(
      (lineItem) => lineItem.sku === product.productSku,
    );
    if (!lineItem) {
      throw responseBadRequest([
        {
          field: "products",
          code: "not_found",
          message: `Product with SKU '${product.productSku}' is not found in the order.`,
        },
      ]);
    }
    return {
      lineItemId: lineItem.id,
      quantity: product.qty,
    };
  });
};
