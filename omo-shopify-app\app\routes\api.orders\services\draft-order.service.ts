import logger from "@/logger.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ApolloClient } from "@apollo/client/core";
import { LoyaltyPoints } from "@prisma/client";
import assert from "assert";
import {
  DRAFT_ORDER_COMPLETE_MUTATION,
  DRAFT_ORDER_CREATE_MUTATION,
  DRAFT_ORDER_DELETE_MUTATION,
} from "../graphql/mutations";
import {
  CreateDraftOrderMutationResponseInterface,
  CreateOrderInputDtoInterface,
  DraftOrderCompleteMutationResponseInterface,
} from "../interface";
import { storeSalesOrderToDatabase } from "../repositories/order.repository";
import { transformOrderToCreateDraftInput } from "../transformers/order.transformer";

export const createDraftOrder = async (
  client: ApolloClient<object>,
  createOrderInputDto: CreateOrderInputDtoInterface,
  customerId: string | undefined,
  productVariants: any[],
  loyaltyPoints?: LoyaltyPoints | null,
) => {
  const { data: draftOrderCreateMutationResult } = await client.mutate({
    mutation: DRAFT_ORDER_CREATE_MUTATION,
    variables: transformOrderToCreateDraftInput(
      createOrderInputDto,
      customerId,
      productVariants,
      loyaltyPoints,
    ),
  });

  assert(draftOrderCreateMutationResult as CreateDraftOrderMutationResponseInterface);

  if (draftOrderCreateMutationResult.draftOrderCreate.userErrors.length > 0) {
    logger.error(
      `Draft order creation failed - ${JSON.stringify(draftOrderCreateMutationResult.draftOrderCreate.userErrors)}`,
    );
    throw responseBadRequest(
      draftOrderCreateMutationResult.draftOrderCreate.userErrors.map(
        (error: { field: string[] | null; message: string }) => [
          {
            field: error.field ? error.field.join(".") : "unknown",
            code: "invalid_value",
            message: error.message,
          },
        ],
      ),
    );
  }

  return draftOrderCreateMutationResult.draftOrderCreate;
};

export const completeDraftOrder = async (client: ApolloClient<object>, draftOrderId: string) => {
  logger.info(`Completing draft order ${draftOrderId}`);
  try {
    const { data: draftOrderCompleteMutationResult } = await client.mutate({
      mutation: DRAFT_ORDER_COMPLETE_MUTATION,
      variables: {
        id: draftOrderId,
      },
    });
    assert(draftOrderCompleteMutationResult as DraftOrderCompleteMutationResponseInterface);

    logger.debug(`Draft order ${draftOrderId} completed, storing to database`);
    await storeSalesOrderToDatabase(draftOrderCompleteMutationResult.draftOrderComplete.draftOrder);

    const orderId = draftOrderCompleteMutationResult.draftOrderComplete.draftOrder.order?.id;
    if (!orderId) {
      throw new Error(`Failed to get order ID from draft order ${draftOrderId}`);
    }

    logger.info(`Successfully completed draft order ${draftOrderId} as order ${orderId}`);
    return orderId;
  } catch (error) {
    logger.error(`Failed to complete draft order ${draftOrderId}: ${error}`);
    throw error;
  }
};

export const deleteDraftOrderOnError = async (
  client: ApolloClient<object>,
  draftOrderId: string,
  validationErrors: any[],
) => {
  logger.warn(
    `Deleting draft order ${draftOrderId} due to validation error: ${JSON.stringify(validationErrors)}`,
  );
  await client
    .mutate({
      mutation: DRAFT_ORDER_DELETE_MUTATION,
      variables: {
        input: { id: draftOrderId },
      },
    })
    .catch((deleteError) =>
      logger.error(`Failed to delete draft order: ${JSON.stringify(deleteError)}`),
    );
};
