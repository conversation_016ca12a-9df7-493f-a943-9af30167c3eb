import logger from "@/logger.server";
import { ApolloClient } from "@apollo/client/core";
import { METAFIELDS_SET_MUTATION, ORDER_MARK_AS_PAID_MUTATION } from "../graphql/mutations";
import { CreateOrderInputDtoInterface } from "../interface";
import { orderMetafields } from "../transformers/order.transformer";

export const addOrderMetafields = async (
  client: ApolloClient<object>,
  shopifyOrderId: string,
  createOrderInputDto: CreateOrderInputDtoInterface,
) => {
  try {
    const metafields = orderMetafields(createOrderInputDto);

    await client.mutate({
      mutation: METAFIELDS_SET_MUTATION,
      variables: {
        metafields: metafields.map((metafield) => ({
          ...metafield,
          ownerId: shopifyOrderId,
        })),
      },
    });
    logger.info(`Successfully added metafields to order ${shopifyOrderId}`);
  } catch (error) {
    logger.error(`Failed to add metafields to order ${shopifyOrderId}: ${error}`);
  }
};

export const markOrderAsPaid = async (client: ApolloClient<object>, shopifyOrderId: string) => {
  try {
    await client.mutate({
      mutation: ORDER_MARK_AS_PAID_MUTATION,
      variables: {
        input: { id: shopifyOrderId },
      },
    });
    logger.info(`Successfully marked order ${shopifyOrderId} as paid`);
  } catch (error) {
    logger.error(`Failed to mark order ${shopifyOrderId} as paid: ${error}`);
  }
};
