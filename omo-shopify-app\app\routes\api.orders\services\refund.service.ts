import { ApolloClient } from "@apollo/client/core";
import assert from "assert";
import { 
  RefundCreateMutationResponseInterface,
  QueryOrderWithSuggestedRefundResponseInterface 
} from "../interface";
import { REFUND_CREATE_MUTATION } from "../graphql/mutations";
import { transformRelativeOrderToRefundInput } from "../transformers/refund.transformer";

export const createRefund = async (
  client: ApolloClient<object>,
  relativeOrder: QueryOrderWithSuggestedRefundResponseInterface["order"],
) => {
  const { data: refundCreateMutationResult } = await client.mutate({
    mutation: REFUND_CREATE_MUTATION,
    variables: transformRelativeOrderToRefundInput(relativeOrder),
  });
  
  assert(refundCreateMutationResult as RefundCreateMutationResponseInterface);
  
  return refundCreateMutationResult.refundCreate;
};
