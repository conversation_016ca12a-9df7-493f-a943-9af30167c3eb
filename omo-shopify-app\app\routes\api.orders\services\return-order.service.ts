import { COUNTRY_PHONE_CODES } from "@/constants";
import responseSuccess from "@/utils/response.success";
import { ApolloClient } from "@apollo/client/core";
import { CreateOrderInputDtoInterface } from "../interface";
import { findCustomerByPhone } from "../repositories/customer.repository";
import { findOrderByRequestData, updateReturnOrderToDatabase } from "../repositories/order.repository";
import { 
  findLineItemsFromShopify, 
  findOrderWithSuggestedRefund,
  mapLineItemsToQueryOrderInput 
} from "../repositories/shopify-order.repository";
import { validateSuggestedRefund, validateReturnOrderOwnership } from "../validators/refund.validator";
import { createRefund } from "./refund.service";
import { transformRefundCreateOutputToResponse } from "../transformers/refund.transformer";

export const handleReturnOrder = async (
  client: ApolloClient<object>,
  createOrderInputDto: CreateOrderInputDtoInterface,
) => {
  // Query `memberId` from `cellphone`
  const memberId = createOrderInputDto.cellphone
    ? (
        await findCustomerByPhone(
          client,
          `${COUNTRY_PHONE_CODES.TAIWAN}${createOrderInputDto.cellphone.slice(1)}`,
        )
      ).id
    : undefined;

  const salesOrderPrisma = await findOrderByRequestData(createOrderInputDto);

  // Find the line items from the database
  const salesOrderLineItems = await findLineItemsFromShopify(client, salesOrderPrisma.ordShopiId);

  // Map line items to query order input
  const lineItemsAsInput = mapLineItemsToQueryOrderInput(createOrderInputDto, salesOrderLineItems);

  // Prepare query order input
  const queryOrderInput = {
    id: salesOrderPrisma.ordShopiId,
    refundLineItems: lineItemsAsInput,
  };

  // Query order with suggested refund
  const relativeOrder = await findOrderWithSuggestedRefund(client, queryOrderInput);

  // Validate suggested refund
  validateSuggestedRefund(createOrderInputDto, relativeOrder.order);

  // Validate that the member is the owner of the order
  validateReturnOrderOwnership(memberId, relativeOrder.order.customer?.id);

  // Create refund order
  const refundCreateResult = await createRefund(client, relativeOrder.order);

  // Update return order to database
  const returnOrderPrisma = await updateReturnOrderToDatabase(
    createOrderInputDto.orderId,
    refundCreateResult.refund.id,
    salesOrderPrisma.id,
  );

  // Return the refund order
  return responseSuccess(
    transformRefundCreateOutputToResponse(
      refundCreateResult,
      returnOrderPrisma.orderId,
    ),
  );
};
