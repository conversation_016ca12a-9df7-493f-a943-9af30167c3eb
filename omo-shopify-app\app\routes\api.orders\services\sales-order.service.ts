import { COUNTRY_PHONE_CODES } from "@/constants";
import db from "@/db.server";
import logger from "@/logger.server";
import responseBadRequest from "@/utils/response.badRequest";
import responseSuccess from "@/utils/response.success";
import { ApolloClient } from "@apollo/client/core";
import { LoyaltyProgramType } from "@prisma/client";
import { CreateOrderInputDtoInterface } from "../interface";
import { findCustomerByPhone } from "../repositories/customer.repository";
import { getProductVariantsFromSku } from "../repositories/product.repository";
import { transformOrderFromCreateDraftResponse } from "../transformers/order.transformer";
import { validateDraftOrder } from "../validators/draft-order.validator";
import { validateSalesOrder } from "../validators/sales-order.validator";
import {
  completeDraftOrder,
  createDraftOrder,
  deleteDraftOrderOnError,
} from "./draft-order.service";
import { addOrderMetafields, markOrderAsPaid } from "./metafield.service";

export const processSalesOrder = async (
  client: ApolloClient<object>,
  shop: { id: number },
  createOrderInputDto: CreateOrderInputDtoInterface,
) => {
  await validateSalesOrder(createOrderInputDto);

  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.POINTS,
      isActive: true,
    },
    include: {
      points: true,
    },
  });

  const customer = createOrderInputDto.cellphone
    ? await findCustomerByPhone(
        client,
        `${COUNTRY_PHONE_CODES.TAIWAN}${createOrderInputDto.cellphone.slice(1)}`,
      )
    : undefined;

  const customerId = customer?.id;

  const productVariants = await getProductVariantsFromSku(client, createOrderInputDto);

  const draftOrderResult = await createDraftOrder(
    client,
    createOrderInputDto,
    customerId,
    productVariants,
    loyaltyProgram?.points,
  );

  const validationErrors = await validateDraftOrder(
    createOrderInputDto,
    draftOrderResult.draftOrder,
    client,
    customer,
    loyaltyProgram,
    shop.id,
  );

  if (validationErrors.length > 0) {
    logger.warn(
      `Deleting draft order ${draftOrderResult.draftOrder.id} due to validation error: ${JSON.stringify(validationErrors)}`,
    );
    await deleteDraftOrderOnError(client, draftOrderResult.draftOrder.id, validationErrors);
    return responseBadRequest(validationErrors);
  }

  const shopifyOrderId = await completeDraftOrder(client, draftOrderResult.draftOrder.id);

  await addOrderMetafields(client, shopifyOrderId, createOrderInputDto);
  await markOrderAsPaid(client, shopifyOrderId);

  return responseSuccess({
    memberId: customerId,
    ...transformOrderFromCreateDraftResponse({ draftOrderCreate: draftOrderResult }),
  });
};
