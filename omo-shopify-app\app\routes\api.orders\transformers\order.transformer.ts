import { LoyaltyPoints } from "@prisma/client";
import {
  CreateDraftOrderMutationInputInterface,
  CreateDraftOrderMutationResponseInterface,
  CreateOrderInputDtoInterface,
} from "../interface";

export const orderMetafields = (
  createOrderInputDto: CreateOrderInputDtoInterface,
): {
  key: string;
  type: string;
  value: string;
}[] => [
  {
    key: "order_id",
    type: "single_line_text_field",
    value: createOrderInputDto.orderId,
  },
  {
    key: "order_date",
    type: "date_time",
    value: createOrderInputDto.orderDate,
  },
  ...(createOrderInputDto.invoice
    ? [
        {
          key: "invoice",
          type: "single_line_text_field",
          value: createOrderInputDto.invoice,
        },
      ]
    : []),
  ...(createOrderInputDto.totalOrderAmount
    ? [
        {
          key: "total_order_amount",
          type: "number_decimal",
          value: String(createOrderInputDto.totalOrderAmount),
        },
      ]
    : []),
  ...(createOrderInputDto.discountAmount
    ? [
        {
          key: "discount_amount",
          type: "number_decimal",
          value: String(createOrderInputDto.discountAmount),
        },
      ]
    : []),
  ...(createOrderInputDto.actualOrderAmount
    ? [
        {
          key: "actual_order_amount",
          type: "number_decimal",
          value: String(createOrderInputDto.actualOrderAmount),
        },
      ]
    : []),
  {
    key: "location_id",
    type: "single_line_text_field",
    value: createOrderInputDto.locationID,
  },
  {
    key: "staff_id",
    type: "single_line_text_field",
    value: createOrderInputDto.staffID,
  },
  {
    key: "redeem_points",
    type: "number_integer",
    value: createOrderInputDto.loyaltyPoint?.toString() ?? "0",
  },
];

export const transformOrderToCreateDraftInput = (
  createOrderInputDto: CreateOrderInputDtoInterface,
  memberId: string | undefined,
  productVariants: Array<{
    variantId?: string;
    productId?: string;
    productName?: string;
    unitPrice?: number;
    qty: number;
    productSku: string;
  }>,
  loyaltyPoint?: LoyaltyPoints | null,
): CreateDraftOrderMutationInputInterface => ({
  input: {
    purchasingEntity: memberId
      ? {
          customerId: memberId,
        }
      : undefined,
    taxExempt: true,
    lineItems: productVariants.map((productVariant) => ({
      title: productVariant.productName,
      sku: productVariant.productSku,
      variantId: productVariant.variantId,
      originalUnitPriceWithCurrency: productVariant.unitPrice
        ? {
            amount: productVariant.unitPrice,
            currencyCode: "TWD",
          }
        : undefined,
      priceOverride: productVariant.unitPrice
        ? {
            amount: productVariant.unitPrice,
            currencyCode: "TWD",
          }
        : undefined,
      quantity: productVariant.qty,
    })),
    discountCodes: createOrderInputDto.discountCodes,
    metafields: orderMetafields(createOrderInputDto),
    appliedDiscount: createOrderInputDto.loyaltyPointDiscountAmount
      ? {
          value: createOrderInputDto.loyaltyPointDiscountAmount,
          valueType: "FIXED_AMOUNT",
          title: loyaltyPoint?.programName ?? "",
        }
      : undefined,
  },
});

export const transformOrderFromCreateDraftResponse = (
  response: CreateDraftOrderMutationResponseInterface,
) => {
  const draftOrder = response.draftOrderCreate.draftOrder;
  return {
    cellphone: draftOrder.customer?.phone,
    purchaseType: "Sales",
    orderId: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "order_id",
    )?.value,
    // shopifyOrderId: order.id,
    orderDate: draftOrder.metafields.nodes.find((metafield) => metafield.key === "order_date")
      ?.value,
    products: draftOrder.lineItems.nodes.map((lineItem) => ({
      qty: lineItem.quantity,
      productSku: lineItem.sku,
      productName: lineItem.title,
      unitPrice: Number(lineItem.originalUnitPriceSet.shopMoney.amount),
    })),
    totalOrderAmount: Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount),
    discountCodes: draftOrder.discountCodes,
    loyaltyPoint: undefined, //TODO: get from CRM
    loyaltyPointDiscountAmount: undefined, //TODO: get from CRM
    discountAmount: Number(draftOrder.totalDiscountsSet.shopMoney.amount),
    actualOrderAmount: Number(draftOrder.totalPriceSet.shopMoney.amount),
    locationID: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "location_id",
    )?.value,
    staffID: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "staff_id",
    )?.value,
    invoice: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "invoice",
    )?.value,
    relativeOrderId: undefined,
  };
};
