import {
  QueryOrderWithSuggestedRefundResponseInterface,
  RefundCreateMutationInputInterface,
  RefundCreateMutationResponseInterface,
} from "../interface";

export const transformRelativeOrderToRefundInput = (
  relativeOrder: QueryOrderWithSuggestedRefundResponseInterface["order"],
): RefundCreateMutationInputInterface => ({
  input: {
    orderId: relativeOrder.id,
    refundLineItems: relativeOrder.suggestedRefund.refundLineItems.map((refundLineItem) => ({
      lineItemId: refundLineItem.lineItem.id,
      quantity: refundLineItem.quantity,
    })),
    transactions: [
      {
        amount: Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount),
        gateway: "cash",
        kind: "REFUND",
        orderId: relativeOrder.id,
      },
    ],
  },
});

export const transformRefundCreateOutputToResponse = (
  refundCreate: RefundCreateMutationResponseInterface["refundCreate"],
  orderId: string,
) => ({
  cellphone: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "cellphone",
  )?.value,
  purchaseType: "Return",
  orderId: orderId,
  orderDate: refundCreate.order.metafields.nodes.find((metafield) => metafield.key === "order_date")
    ?.value,
  products: refundCreate.refund.refundLineItems.nodes.map((lineItem) => ({
    qty: lineItem.quantity,
    productSku: lineItem.lineItem.sku,
    productName: lineItem.lineItem.name,
    unitPrice: Number(lineItem.priceSet.shopMoney.amount),
  })),
  totalOrderAmount: undefined,
  discountCodes: undefined, // Assuming discount codes are not available in refund response
  loyaltyPoint: undefined, //TODO: get from CRM
  loyaltyPointDiscountAmount: undefined, //TODO: get from CRM
  discountAmount: undefined, // Assuming discount amount is not available in refund response
  actualOrderAmount: -Number(refundCreate.refund.totalRefundedSet.shopMoney.amount),
  locationID: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "location_id",
  )?.value,
  staffID: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "staff_id",
  )?.value,
  invoice: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "invoice",
  )?.value,
  relativeOrderId: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "order_id",
  )?.value,
});
