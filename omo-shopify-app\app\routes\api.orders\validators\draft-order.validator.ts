import { addCustomerTimeline } from "@/services";
import { ApolloClient } from "@apollo/client/core";
import { CustomerTimelineType, LoyaltyPoints, LoyaltyProgram } from "@prisma/client";
import {
  CreateDraftOrderMutationResponseInterface,
  CreateOrderInputDtoInterface,
  CustomerInterface,
} from "../interface";
import { updateCustomerPoints } from "../repositories/customer.repository";

export const validateDraftOrder = async (
  createOrderInputDto: CreateOrderInputDtoInterface,
  draftOrder: CreateDraftOrderMutationResponseInterface["draftOrderCreate"]["draftOrder"],
  client: ApolloClient<object>,
  customer: CustomerInterface | undefined,
  loyaltyProgram: (LoyaltyProgram & { points: LoyaltyPoints | null }) | null,
  shopId: number,
) => {
  const validationErrors = [];

  // Validate total order amount
  if (
    Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount) !==
    createOrderInputDto.totalOrderAmount
  ) {
    validationErrors.push({
      field: "totalOrderAmount",
      code: "calculation_mismatch",
      message: `Total order amount does not match the calculated value`,
      detail: {
        expected: Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount),
        actual: createOrderInputDto.totalOrderAmount,
      },
    });
  }

  // Validate discount amount
  if (
    Number(draftOrder.totalDiscountsSet.shopMoney.amount) !== createOrderInputDto.discountAmount
  ) {
    validationErrors.push({
      field: "discountAmount",
      code: "calculation_mismatch",
      message: `Discount amount does not match the calculated value. Expected ${draftOrder.totalDiscountsSet.shopMoney.amount}, given ${createOrderInputDto.discountAmount}`,
    });
  }

  // Validate actual order amount
  if (Number(draftOrder.totalPriceSet.shopMoney.amount) !== createOrderInputDto.actualOrderAmount) {
    validationErrors.push({
      field: "actualOrderAmount",
      code: "calculation_mismatch",
      message: `Actual order amount does not match the calculated value. Expected ${draftOrder.totalPriceSet.shopMoney.amount}, given ${createOrderInputDto.actualOrderAmount}`,
    });
  }

  // Validate discount codes
  if (
    createOrderInputDto.discountCodes &&
    draftOrder.discountCodes.length !== createOrderInputDto.discountCodes.length
  ) {
    const invalidDiscountCodes = [];
    for (const discountCode of createOrderInputDto.discountCodes) {
      if (!draftOrder.discountCodes.includes(discountCode)) {
        invalidDiscountCodes.push(discountCode);
      }
    }
    validationErrors.push({
      field: "discountCodes",
      code: "invalid_discount_codes",
      message: `Some or all discount codes are not applicable`,
      detail: {
        valid_codes: draftOrder.discountCodes,
        invalid_codes: invalidDiscountCodes,
      },
    });
  }

  // Validate loyalty points
  const { loyaltyPoint, loyaltyPointDiscountAmount, totalOrderAmount } = createOrderInputDto;

  if (
    !customer ||
    !loyaltyPoint ||
    !loyaltyPointDiscountAmount ||
    !loyaltyProgram ||
    !loyaltyProgram.points
  ) {
    if (loyaltyPoint || loyaltyPointDiscountAmount) {
      validationErrors.push({
        field: "loyaltyPoint",
        code: "invalid_value",
        message: "Loyalty point is not valid.",
      });
    }
    return validationErrors;
  }

  const customerPoints = Number(
    customer.metafields.nodes.find((node) => node.key === "points")?.value ?? 0,
  );

  if (customerPoints < loyaltyPoint) {
    validationErrors.push({
      field: "loyaltyPoint",
      code: "invalid_value",
      message: `Customer does not have enough points. Required: ${loyaltyPoint}, Available: ${customerPoints}`,
    });
  }

  const { pointsRedemptionAmount, pointsRedemptionValue, maxRedeemPercentage, minPurchaseAmount } =
    loyaltyProgram.points;

  const expectedDiscount = (loyaltyPoint / pointsRedemptionAmount) * pointsRedemptionValue;
  const epsilon = 0.00001;

  if (Math.abs(expectedDiscount - loyaltyPointDiscountAmount) > epsilon) {
    validationErrors.push({
      field: "loyaltyPointDiscountAmount",
      code: "calculation_mismatch",
      message: `Loyalty point discount amount is incorrect. Expected ${expectedDiscount.toFixed(
        2,
      )}, given ${loyaltyPointDiscountAmount}`,
    });
  }

  const maxAllowedDiscount = (totalOrderAmount * maxRedeemPercentage) / 100;
  if (loyaltyPointDiscountAmount - maxAllowedDiscount > epsilon) {
    validationErrors.push({
      field: "loyaltyPointDiscountAmount",
      code: "calculation_mismatch",
      message: `Loyalty point discount amount exceeds the maximum allowed (${maxRedeemPercentage}% of totalOrderAmount = ${maxAllowedDiscount.toFixed(
        2,
      )}).`,
    });
  }

  if (typeof minPurchaseAmount === "number" && totalOrderAmount < minPurchaseAmount) {
    validationErrors.push({
      field: "loyaltyPointDiscountAmount",
      code: "calculation_mismatch",
      message: `Total order amount (${totalOrderAmount.toFixed(
        2,
      )}) is below the minimum purchase amount (${minPurchaseAmount.toFixed(
        2,
      )}) required to redeem points.`,
    });
  }

  if (validationErrors.length === 0 && loyaltyPoint) {
    await updateCustomerPoints(client, customer.id, customerPoints - loyaltyPoint);

    await addCustomerTimeline({
      shopId: shopId,
      customerId: customer.id,
      message: `<p>This customer redeemed <strong>${loyaltyPoint}</strong> points for order <a href="shopify://orders/${draftOrder.id}">${draftOrder.id}</a></p>`,
      type: CustomerTimelineType.POINTS,
    });
  }

  return validationErrors;
};
