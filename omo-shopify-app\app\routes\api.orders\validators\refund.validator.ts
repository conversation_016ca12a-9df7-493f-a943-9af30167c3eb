import responseBadRequest from "@/utils/response.badRequest";
import { 
  CreateOrderInputDtoInterface,
  QueryOrderWithSuggestedRefundResponseInterface 
} from "../interface";

export const validateSuggestedRefund = (
  createOrderInputDto: CreateOrderInputDtoInterface,
  relativeOrder: QueryOrderWithSuggestedRefundResponseInterface["order"],
) => {
  const validationErrors = [];
  
  if (
    -Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount) !==
    createOrderInputDto.actualOrderAmount
  ) {
    validationErrors.push({
      field: "actualOrderAmount",
      code: "calculation_mismatch",
      message: `Total refunded amount does not match the calculated value`,
      detail: {
        expected: -Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount),
        actual: createOrderInputDto.totalOrderAmount,
      },
    });
  }

  if (validationErrors.length > 0) {
    throw responseBadRequest(validationErrors);
  }
};

export const validateReturnOrderOwnership = (
  memberId: string | undefined,
  orderCustomerId: string | undefined,
) => {
  if (memberId !== orderCustomerId) {
    throw responseBadRequest([
      {
        field: "cellphone",
        code: "invalid_value",
        message: "Member with the specified 'cellphone' is not the owner of the order.",
      },
    ]);
  }
};
