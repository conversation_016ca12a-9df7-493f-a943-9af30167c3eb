import db from "@/db.server";
import responseBadRequest from "@/utils/response.badRequest";
import { CreateOrderInputDtoInterface } from "../interface";

export const validateSalesOrder = async (createOrderInputDto: CreateOrderInputDtoInterface) => {
  // Verify that `orderId` not yet exists in the database
  const existingOrder = await db.salesOrders.findFirst({
    where: {
      orderPosId: createOrderInputDto.orderId,
    },
    select: {
      id: true,
      ordShopiId: true,
    },
  });

  if (existingOrder) {
    throw responseBadRequest([
      {
        field: "orderId",
        code: "invalid_value",
        message: `Order with ID ${createOrderInputDto.orderId} already exists`,
      },
    ]);
  }
};
