import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { LoaderFunctionArgs } from "@remix-run/node";
import { initMetafields } from "../app.settings/service";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  await initMetafields(admin);
  await findShop(admin, session);

  return {};
};
