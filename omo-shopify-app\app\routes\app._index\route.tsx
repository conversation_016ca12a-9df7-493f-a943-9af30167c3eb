import { Layout, Page } from "@shopify/polaris";
import FeaturesSection from "./components/FeaturesSection";
import HelpSection from "./components/HelpSection";
import LanguageSelector from "./components/LanguageSelector";
import WelcomeSection from "./components/WelcomeSection";

export { loader } from "./loader";

export default function WelcomePage() {
  return (
    <Page>
      <Layout>
        <Layout.Section>
          <LanguageSelector />
        </Layout.Section>

        <Layout.Section>
          <WelcomeSection />
        </Layout.Section>

        <Layout.Section>
          <FeaturesSection />
        </Layout.Section>

        <Layout.Section>
          <div className="border-t border-gray-200"></div>
        </Layout.Section>

        <Layout.Section>
          <HelpSection />
        </Layout.Section>

        <Layout.Section></Layout.Section>
      </Layout>
    </Page>
  );
}
