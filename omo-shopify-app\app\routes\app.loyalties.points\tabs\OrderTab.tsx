import { PointIssueType } from "@/constants";
import { useFetcher } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Button,
  Card,
  Checkbox,
  ChoiceList,
  Divider,
  InlineGrid,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface LoyaltyPointsData {
  id: number;
  includeProductTotal: boolean;
  includeShipping: boolean;
  includeTaxes: boolean;
  pointsIssueType: string;
  issueDays: number;
  orderStatus: string;
  orderRefundType: string;
  redeemedRefundType: string;
}

interface OrderTabProps {
  loyaltyPoints?: LoyaltyPointsData | null;
}

export default function OrderTab({ loyaltyPoints }: Readonly<OrderTabProps>) {
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const { t } = useTranslation();

  // State for form values
  const [productTotal, setProductTotal] = useState(loyaltyPoints?.includeProductTotal ?? true);
  const [includeShipping, setIncludeShipping] = useState(loyaltyPoints?.includeShipping ?? false);
  const [includeTaxes, setIncludeTaxes] = useState(loyaltyPoints?.includeTaxes ?? false);
  const [pointsIssueType, setPointsIssueType] = useState(
    loyaltyPoints?.pointsIssueType?.toUpperCase() ?? PointIssueType.DELAYED,
  );
  const [issueDays, setIssueDays] = useState(loyaltyPoints?.issueDays?.toString() ?? "14");
  const [orderStatus, setOrderStatus] = useState(loyaltyPoints?.orderStatus ?? "PAID_FULFILLED");
  const [orderRefundType, setOrderRefundType] = useState(
    loyaltyPoints?.orderRefundType?.toUpperCase() ?? "PROPORTIONAL",
  );
  const [redeemedRefundType, setRedeemedRefundType] = useState(
    loyaltyPoints?.redeemedRefundType?.toUpperCase() ?? "PROPORTIONAL",
  );

  // Handle checkbox changes
  const handleProductTotalChange = useCallback(
    (newChecked: boolean | ((prevState: boolean) => boolean)) => setProductTotal(newChecked),
    [],
  );
  const handleIncludeShippingChange = useCallback(
    (newChecked: boolean | ((prevState: boolean) => boolean)) => setIncludeShipping(newChecked),
    [],
  );
  const handleIncludeTaxesChange = useCallback(
    (newChecked: boolean | ((prevState: boolean) => boolean)) => setIncludeTaxes(newChecked),
    [],
  );

  // Handle choice list changes
  const handlePointsIssueTypeChange = useCallback(
    (selected: string[]) => setPointsIssueType(selected[0]),
    [],
  );
  const handleOrderRefundTypeChange = useCallback(
    (selected: string[]) => setOrderRefundType(selected[0]),
    [],
  );
  const handleRedeemedRefundTypeChange = useCallback(
    (selected: string[]) => setRedeemedRefundType(selected[0]),
    [],
  );

  // Handle text field changes
  const handleIssueDaysChange = useCallback((value: string) => setIssueDays(value), []);

  // Handle select changes
  const handleOrderStatusChange = useCallback((value: string) => setOrderStatus(value), []);

  const orderStatusOptions = [
    { label: t("loyalties.points.orderTab.orderStatus.paidAndFulfilled"), value: "PAID_FULFILLED" },
    { label: t("loyalties.points.orderTab.orderStatus.paid"), value: "PAID" },
  ];

  // Show toast notification when save is successful
  useEffect(() => {
    if (fetcher.data) {
      shopify?.toast.show(t("loyalties.points.settingsSaved"));
    }
  }, [fetcher.data, shopify, t]);

  const onSave = useCallback(() => {
    const formData = new FormData();

    // Order total settings
    formData.append("includeProductTotal", productTotal.toString());
    formData.append("includeShipping", includeShipping.toString());
    formData.append("includeTaxes", includeTaxes.toString());

    // Points issue settings
    formData.append("pointsIssueType", pointsIssueType);
    formData.append("issueDays", issueDays);
    formData.append("orderStatus", orderStatus);

    // Refund settings
    formData.append("orderRefundType", orderRefundType);
    formData.append("redeemedRefundType", redeemedRefundType);

    // Add a flag to indicate this is from the order tab
    formData.append("tabType", "order");

    fetcher.submit(formData, { method: "post" });
  }, [
    productTotal,
    includeShipping,
    includeTaxes,
    pointsIssueType,
    issueDays,
    orderStatus,
    orderRefundType,
    redeemedRefundType,
    fetcher,
  ]);

  return (
    <BlockStack gap="500">
      <Text variant="headingLg" as="h5">
        {t("loyalties.points.orderTab.pointsReward")}
      </Text>
      <Card>
        <BlockStack gap="500">
          {/* Order Total Section */}
          <InlineGrid gap="400" columns={2}>
            <BlockStack gap="200">
              <Text as="h2" variant="headingMd">
                {t("loyalties.points.orderTab.orderTotal.title")}
              </Text>
              <Text as="p" variant="bodyMd" tone="subdued">
                {t("loyalties.points.orderTab.orderTotal.description")}
              </Text>
            </BlockStack>

            <BlockStack>
              <Checkbox
                label={t("loyalties.points.orderTab.orderTotal.productTotal")}
                checked={productTotal}
                onChange={handleProductTotalChange}
              />
              <Checkbox
                label={t("loyalties.points.orderTab.orderTotal.includeShipping")}
                checked={includeShipping}
                onChange={handleIncludeShippingChange}
              />
              <Checkbox
                label={t("loyalties.points.orderTab.orderTotal.includeTaxes")}
                checked={includeTaxes}
                onChange={handleIncludeTaxesChange}
              />
            </BlockStack>
          </InlineGrid>

          <Divider />

          {/* Points Issue Date Section */}
          <InlineGrid gap="400" columns={2}>
            <BlockStack gap="200">
              <Text as="h2" variant="headingMd">
                {t("loyalties.points.orderTab.pointsIssueDate.title")}
              </Text>
              <Text as="p" variant="bodyMd" tone="subdued">
                {t("loyalties.points.orderTab.pointsIssueDate.description")}
              </Text>
            </BlockStack>

            <BlockStack gap="200">
              <Text as="h2" variant="headingMd">
                {t("loyalties.points.orderTab.pointsIssueDate.issuePointsReward")}
              </Text>
              <ChoiceList
                title=""
                titleHidden
                choices={[
                  {
                    label: t("loyalties.points.orderTab.pointsIssueDate.immediately"),
                    value: PointIssueType.IMMEDIATE,
                  },
                  {
                    label: (
                      <InlineGrid gap="200" columns={["oneThird", "twoThirds"]}>
                        <TextField
                          label=""
                          type="number"
                          value={issueDays}
                          onChange={handleIssueDaysChange}
                          autoComplete="off"
                          disabled={pointsIssueType !== PointIssueType.DELAYED}
                        />
                        <Text as="span" variant="bodyMd">
                          {t("loyalties.points.orderTab.pointsIssueDate.days")}
                        </Text>
                      </InlineGrid>
                    ),
                    value: PointIssueType.DELAYED,
                  },
                ]}
                selected={[pointsIssueType]}
                onChange={handlePointsIssueTypeChange}
              />
              <BlockStack gap="200">
                <Text as="p" variant="bodyMd">
                  {t("loyalties.points.orderTab.pointsIssueDate.afterOrderIs")}
                </Text>
                <Select
                  label=""
                  options={orderStatusOptions}
                  onChange={handleOrderStatusChange}
                  value={orderStatus}
                />
                <Text as="p" variant="bodyMd" tone="subdued">
                  {t("loyalties.points.orderTab.pointsIssueDate.rewardPointsIssued")}
                </Text>
              </BlockStack>
            </BlockStack>
          </InlineGrid>

          <Divider />

          {/* Order Rewarded Points Refund Section */}
          <InlineGrid gap="400" columns={2}>
            <BlockStack gap="200">
              <Text as="h2" variant="headingMd">
                {t("loyalties.points.orderTab.orderRewardedPointsRefund.title")}
              </Text>
              <Text as="p" variant="bodyMd" tone="subdued">
                {t("loyalties.points.orderTab.orderRewardedPointsRefund.description")}
              </Text>
            </BlockStack>

            <BlockStack gap="200">
              <ChoiceList
                title=""
                titleHidden
                choices={[
                  {
                    label: t("loyalties.points.orderTab.orderRewardedPointsRefund.proportionally"),
                    value: "PROPORTIONAL",
                    helpText: t(
                      "loyalties.points.orderTab.orderRewardedPointsRefund.proportionallyHelpText",
                    ),
                  },
                  {
                    label: t("loyalties.points.orderTab.orderRewardedPointsRefund.full"),
                    value: "FULL",
                    helpText: t("loyalties.points.orderTab.orderRewardedPointsRefund.fullHelpText"),
                  },
                  {
                    label: t("loyalties.points.orderTab.orderRewardedPointsRefund.none"),
                    value: "NONE",
                    helpText: t("loyalties.points.orderTab.orderRewardedPointsRefund.noneHelpText"),
                  },
                ]}
                selected={[orderRefundType]}
                onChange={handleOrderRefundTypeChange}
              />
            </BlockStack>
          </InlineGrid>

          <Divider />

          {/* Redeemed Points Refund Section */}
          <InlineGrid gap="400" columns={2}>
            <BlockStack gap="200">
              <Text as="h2" variant="headingMd">
                {t("loyalties.points.orderTab.redeemedPointsRefund.title")}
              </Text>
              <Text as="p" variant="bodyMd" tone="subdued">
                {t("loyalties.points.orderTab.redeemedPointsRefund.description")}
              </Text>
            </BlockStack>

            <BlockStack gap="200">
              <ChoiceList
                title=""
                titleHidden
                choices={[
                  {
                    label: t("loyalties.points.orderTab.redeemedPointsRefund.proportionally"),
                    value: "PROPORTIONAL",
                    helpText: t(
                      "loyalties.points.orderTab.redeemedPointsRefund.proportionallyHelpText",
                    ),
                  },
                  {
                    label: t("loyalties.points.orderTab.redeemedPointsRefund.none"),
                    value: "NONE",
                    helpText: t("loyalties.points.orderTab.redeemedPointsRefund.noneHelpText"),
                  },
                ]}
                selected={[redeemedRefundType]}
                onChange={handleRedeemedRefundTypeChange}
              />
            </BlockStack>
          </InlineGrid>
        </BlockStack>
      </Card>

      <div className="flex justify-center w-full items-center mb-5">
        <Button
          size="large"
          variant="primary"
          onClick={onSave}
          loading={fetcher.state === "submitting"}
        >
          {t("loyalties.points.save")}
        </Button>
      </div>
    </BlockStack>
  );
}
