import { useFetcher } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Button,
  Card,
  Checkbox,
  Divider,
  Form,
  InlineGrid,
  InlineStack,
  Layout,
  RadioButton,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface LoyaltyPointsData {
  id: number;
  programName: string;
  pointSingular: string;
  pointPlural: string;
  pointsPerCurrency: number;
  currencyAmount: number;
  pointsRedemptionValue: number;
  pointsRedemptionAmount: number;
  maxRedeemPercentage: number;
  minPurchaseAmount: number | null;
  roundingMethod: string;
  isConvertPointsByPercentage: boolean;
}

interface PointsTabProps {
  loyaltyPoints: LoyaltyPointsData | null;
}

export default function PointsTab({ loyaltyPoints }: Readonly<PointsTabProps>) {
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const { t } = useTranslation();

  // Program branding
  const [programName, setProgramName] = useState(loyaltyPoints?.programName ?? "");
  const [pointSingular, setPointSingular] = useState(loyaltyPoints?.pointSingular ?? "");
  const [pointPlural, setPointPlural] = useState(loyaltyPoints?.pointPlural ?? "");

  // Form validation errors
  const [programNameError, setProgramNameError] = useState("");
  const [pointSingularError, setPointSingularError] = useState("");

  // Points reward value
  const [currencyAmount, setCurrencyAmount] = useState(
    loyaltyPoints?.currencyAmount?.toString() ?? "1",
  );
  const [pointsPerCurrency, setPointsPerCurrency] = useState(
    loyaltyPoints?.pointsPerCurrency?.toString() ?? "1",
  );
  const [isConvertPointsByPercentage, setIsConvertPointsByPercentage] = useState(
    loyaltyPoints?.isConvertPointsByPercentage ?? false,
  );
  const [roundingMethod, setRoundingMethod] = useState(loyaltyPoints?.roundingMethod ?? "ROUND");

  // Points reward value errors
  const [currencyAmountError, setCurrencyAmountError] = useState("");
  const [pointsPerCurrencyError, setPointsPerCurrencyError] = useState("");

  // Points redemption value
  const [pointsRedemptionAmount, setPointsRedemptionAmount] = useState(
    loyaltyPoints?.pointsRedemptionAmount?.toString() ?? "100",
  );
  const [pointsRedemptionValue, setPointsRedemptionValue] = useState(
    loyaltyPoints?.pointsRedemptionValue ? loyaltyPoints.pointsRedemptionValue.toString() : "1",
  );
  const [pointsRedemptionValueError, setPointsRedemptionValueError] = useState("");

  // Maximum redeem amount
  const [maxRedeemPercentage, setMaxRedeemPercentage] = useState(
    loyaltyPoints?.maxRedeemPercentage?.toString() ?? "30",
  );
  const [maxRedeemPercentageError, setMaxRedeemPercentageError] = useState("");

  // Minimum purchase amount
  const [minPurchaseType, setMinPurchaseType] = useState(
    loyaltyPoints?.minPurchaseAmount ? "fixed" : "none",
  );
  const [minPurchaseAmount, setMinPurchaseAmount] = useState(
    loyaltyPoints?.minPurchaseAmount?.toString() ?? "1000",
  );
  const [minPurchaseAmountError, setMinPurchaseAmountError] = useState("");

  // Handle checkbox for percentage conversion
  const handleChange = useCallback((newChecked: boolean) => {
    setIsConvertPointsByPercentage(newChecked);
  }, []);

  // Validate form fields
  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset all errors
    setProgramNameError("");
    setPointSingularError("");
    setCurrencyAmountError("");
    setPointsPerCurrencyError("");
    setPointsRedemptionValueError("");
    setMaxRedeemPercentageError("");
    setMinPurchaseAmountError("");

    // Validate program name
    if (!programName.trim()) {
      setProgramNameError(t("loyalties.points.programBranding.programName.error"));
      isValid = false;
    }

    // Validate point singular
    if (!pointSingular.trim()) {
      setPointSingularError(t("loyalties.points.programBranding.pointCurrency.error"));
      isValid = false;
    }

    // Validate currency amount
    if (!currencyAmount.trim()) {
      setCurrencyAmountError(t("loyalties.points.pointsRule.rewardValue.currencyError"));
      isValid = false;
    } else if (isNaN(Number(currencyAmount)) || Number(currencyAmount) <= 0) {
      setCurrencyAmountError(t("loyalties.points.pointsRule.rewardValue.currencyInvalidError"));
      isValid = false;
    }

    // Validate points per currency
    if (!pointsPerCurrency.trim()) {
      setPointsPerCurrencyError(t("loyalties.points.pointsRule.rewardValue.pointsError"));
      isValid = false;
    } else if (isNaN(Number(pointsPerCurrency)) || Number(pointsPerCurrency) <= 0) {
      setPointsPerCurrencyError(t("loyalties.points.pointsRule.rewardValue.pointsInvalidError"));
      isValid = false;
    }

    // Validate points redemption value
    if (!pointsRedemptionValue.trim()) {
      setPointsRedemptionValueError(t("loyalties.points.pointsRule.redemptionValue.error"));
      isValid = false;
    } else if (isNaN(Number(pointsRedemptionValue)) || Number(pointsRedemptionValue) <= 0) {
      setPointsRedemptionValueError(t("loyalties.points.pointsRule.redemptionValue.invalidError"));
      isValid = false;
    }

    // Validate max redeem percentage
    if (!maxRedeemPercentage.trim()) {
      setMaxRedeemPercentageError(t("loyalties.points.pointsRule.maximumRedeem.error"));
      isValid = false;
    } else if (
      isNaN(Number(maxRedeemPercentage)) ||
      Number(maxRedeemPercentage) <= 0 ||
      Number(maxRedeemPercentage) > 100
    ) {
      setMaxRedeemPercentageError(t("loyalties.points.pointsRule.maximumRedeem.invalidError"));
      isValid = false;
    }

    // Validate min purchase amount if fixed type is selected
    if (minPurchaseType === "fixed") {
      if (!minPurchaseAmount.trim()) {
        setMinPurchaseAmountError(t("loyalties.points.pointsRule.minimumPurchase.error"));
        isValid = false;
      } else if (isNaN(Number(minPurchaseAmount)) || Number(minPurchaseAmount) < 0) {
        setMinPurchaseAmountError(t("loyalties.points.pointsRule.minimumPurchase.invalidError"));
        isValid = false;
      }
    }

    return isValid;
  }, [
    programName,
    pointSingular,
    currencyAmount,
    pointsPerCurrency,
    pointsRedemptionValue,
    maxRedeemPercentage,
    minPurchaseType,
    minPurchaseAmount,
    t,
  ]);

  const onSave = useCallback(() => {
    // Validate form before submitting
    if (!validateForm()) {
      // Show error toast if validation fails
      shopify.toast.show(t("loyalties.points.formError"), { isError: true });
      return;
    }

    const formData = new FormData();

    // Program branding
    formData.append("programName", programName);
    formData.append("pointSingular", pointSingular);
    formData.append("pointPlural", pointPlural);

    // Points reward value
    formData.append("currencyAmount", currencyAmount);
    formData.append("pointsPerCurrency", pointsPerCurrency);
    formData.append("roundingMethod", roundingMethod);
    formData.append("isConvertPointsByPercentage", isConvertPointsByPercentage.toString());

    // Points redemption value
    formData.append("pointsRedemptionValue", parseFloat(pointsRedemptionValue).toString());
    formData.append("pointsRedemptionAmount", pointsRedemptionAmount.toString());

    // Maximum redeem amount
    formData.append("maxRedeemPercentage", maxRedeemPercentage);

    // Minimum purchase amount
    if (minPurchaseType === "fixed") {
      formData.append("minPurchaseAmount", minPurchaseAmount);
    }

    fetcher.submit(formData, { method: "post" });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    validateForm,
    programName,
    pointSingular,
    pointPlural,
    currencyAmount,
    pointsPerCurrency,
    roundingMethod,
    isConvertPointsByPercentage,
    pointsRedemptionValue,
    pointsRedemptionAmount,
    maxRedeemPercentage,
    minPurchaseType,
    fetcher,
    t,
    minPurchaseAmount,
  ]);

  useEffect(() => {
    if (fetcher.data) {
      console.log("fetcher.data: ", fetcher.data);
      shopify.toast.show(t("loyalties.points.success"));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, t]);

  const handleSubmit = useCallback(() => {
    onSave();
  }, [onSave]);

  return (
    <Form onSubmit={handleSubmit}>
      <Layout>
        <Layout.Section>
          <BlockStack gap="200">
            <Text variant="headingLg" as="h5">
              {t("loyalties.points.programBranding.title")}
            </Text>

            <Card roundedAbove="xs">
              <BlockStack gap="500">
                <InlineGrid gap="400" columns={2}>
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      {t("loyalties.points.programBranding.programName.title")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.programBranding.programName.description")}
                    </Text>
                  </BlockStack>

                  <BlockStack gap="200">
                    <TextField
                      label=""
                      value={programName}
                      onChange={setProgramName}
                      autoComplete="off"
                      helpText={t("loyalties.points.programBranding.programName.helpText")}
                      error={programNameError}
                    />
                  </BlockStack>
                </InlineGrid>

                <InlineGrid gap="400" columns={2}>
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      {t("loyalties.points.programBranding.pointCurrency.title")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.programBranding.pointCurrency.description")}
                    </Text>
                  </BlockStack>
                  <BlockStack gap="200">
                    <TextField
                      label=""
                      value={pointSingular}
                      onChange={setPointSingular}
                      autoComplete="off"
                      helpText={t(
                        "loyalties.points.programBranding.pointCurrency.singularHelpText",
                      )}
                      error={pointSingularError}
                    />
                    <TextField
                      label=""
                      value={pointPlural}
                      onChange={setPointPlural}
                      autoComplete="off"
                      helpText={t("loyalties.points.programBranding.pointCurrency.pluralHelpText")}
                    />
                  </BlockStack>
                </InlineGrid>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section>
          <BlockStack gap="200">
            <Text variant="headingLg" as="h5">
              {t("loyalties.points.pointsRule.title")}
            </Text>

            <Card roundedAbove="xs">
              <BlockStack gap="500">
                <InlineGrid gap="400" columns={2}>
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      {t("loyalties.points.pointsRule.rewardValue.title")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.pointsRule.rewardValue.description")}
                    </Text>
                  </BlockStack>

                  <BlockStack gap="200">
                    <InlineStack gap="200" wrap={false} blockAlign="center">
                      <TextField
                        label=""
                        type="number"
                        autoComplete="off"
                        value={currencyAmount}
                        onChange={setCurrencyAmount}
                        prefix="NT$"
                        error={currencyAmountError}
                      />
                      <Text as={"h5"} variant="bodyLg" alignment="center">
                        =
                      </Text>
                      <TextField
                        label=""
                        type="number"
                        autoComplete="off"
                        value={pointsPerCurrency}
                        onChange={setPointsPerCurrency}
                        error={pointsPerCurrencyError}
                      />
                      <Text as={"h5"} variant="bodyLg" alignment="center">
                        Points
                      </Text>
                    </InlineStack>

                    <Text as={"h5"} variant="bodyMd" alignment="start">
                      {t("loyalties.points.pointsRule.rewardValue.earnText", {
                        points: pointsPerCurrency,
                        amount: currencyAmount,
                      })}
                    </Text>
                    <Checkbox
                      label={t("loyalties.points.pointsRule.rewardValue.convertByPercentage")}
                      checked={isConvertPointsByPercentage}
                      onChange={handleChange}
                    />
                    {isConvertPointsByPercentage && (
                      <Select
                        label=""
                        value={roundingMethod}
                        onChange={setRoundingMethod}
                        options={[
                          {
                            label: t(
                              "loyalties.points.pointsRule.rewardValue.roundingOptions.round",
                            ),
                            value: "ROUND",
                          },
                          {
                            label: t(
                              "loyalties.points.pointsRule.rewardValue.roundingOptions.floor",
                            ),
                            value: "FLOOR",
                          },
                          {
                            label: t(
                              "loyalties.points.pointsRule.rewardValue.roundingOptions.ceiling",
                            ),
                            value: "CEILING",
                          },
                        ]}
                      />
                    )}
                  </BlockStack>
                </InlineGrid>

                <InlineGrid gap="400" columns={2}>
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      {t("loyalties.points.pointsRule.redemptionValue.title")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.pointsRule.redemptionValue.description")}
                    </Text>
                  </BlockStack>
                  <BlockStack gap="200">
                    <InlineStack gap="200" wrap={false} blockAlign="center">
                      <TextField
                        label=""
                        type="number"
                        autoComplete="off"
                        value={pointsRedemptionAmount}
                        onChange={setPointsRedemptionAmount}
                      />
                      <span>Points</span>
                      <span>=</span>
                      <TextField
                        label=""
                        type="number"
                        autoComplete="off"
                        value={pointsRedemptionValue}
                        onChange={setPointsRedemptionValue}
                        error={pointsRedemptionValueError}
                      />
                    </InlineStack>
                    <Text as={"h5"} variant="bodyMd" alignment="start">
                      {t("loyalties.points.pointsRule.redemptionValue.equalsText", {
                        points: pointsRedemptionAmount,
                        value: pointsRedemptionValue,
                      })}
                    </Text>
                  </BlockStack>
                </InlineGrid>

                <Divider borderColor="border-inverse" />

                <InlineGrid gap="400" columns={2}>
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      {t("loyalties.points.pointsRule.maximumRedeem.title")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.pointsRule.maximumRedeem.description")}
                    </Text>
                  </BlockStack>
                  <BlockStack gap="200">
                    <InlineStack gap="200" blockAlign="center" wrap={false}>
                      <TextField
                        label=""
                        type="number"
                        autoComplete="off"
                        value={maxRedeemPercentage}
                        onChange={setMaxRedeemPercentage}
                        error={maxRedeemPercentageError}
                      />
                      <Text as="h2" variant="headingMd">
                        {t("loyalties.points.pointsRule.maximumRedeem.ofTotal")}
                      </Text>
                    </InlineStack>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.pointsRule.maximumRedeem.helpText", {
                        percentage: maxRedeemPercentage,
                      })}
                    </Text>
                  </BlockStack>
                </InlineGrid>

                <InlineGrid gap="400" columns={2}>
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      {t("loyalties.points.pointsRule.minimumPurchase.title")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("loyalties.points.pointsRule.minimumPurchase.description")}
                    </Text>
                  </BlockStack>

                  <BlockStack gap="200">
                    <RadioButton
                      label={t("loyalties.points.pointsRule.minimumPurchase.none")}
                      id="none"
                      name="minPurchaseType"
                      checked={minPurchaseType === "none"}
                      onChange={() => setMinPurchaseType("none")}
                    />
                    <RadioButton
                      label={t("loyalties.points.pointsRule.minimumPurchase.fixed")}
                      id="fixed"
                      name="minPurchaseType"
                      checked={minPurchaseType === "fixed"}
                      onChange={() => setMinPurchaseType("fixed")}
                    />
                    {minPurchaseType === "fixed" && (
                      <InlineStack gap={"200"} blockAlign="center" wrap={false}>
                        <TextField
                          label=""
                          type="number"
                          autoComplete="off"
                          value={minPurchaseAmount}
                          onChange={setMinPurchaseAmount}
                          error={minPurchaseAmountError}
                        />
                        <Text as="h2" variant="headingMd" alignment="start">
                          {t("loyalties.points.pointsRule.minimumPurchase.currency")}
                        </Text>
                      </InlineStack>
                    )}
                    {minPurchaseType === "fixed" && (
                      <Text as="p" variant="bodyMd" tone="subdued">
                        {t("loyalties.points.pointsRule.minimumPurchase.helpText", {
                          amount: minPurchaseAmount,
                        })}
                      </Text>
                    )}
                  </BlockStack>
                </InlineGrid>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section>
          <div className="flex justify-center w-full items-center mb-5">
            <Button
              size="large"
              variant="primary"
              submit
              loading={fetcher.state === "submitting"}
              disabled={fetcher.state === "submitting"}
            >
              {fetcher.state === "submitting"
                ? t("loyalties.points.saving")
                : t("loyalties.points.save")}
            </Button>
          </div>
        </Layout.Section>
      </Layout>
    </Form>
  );
}
