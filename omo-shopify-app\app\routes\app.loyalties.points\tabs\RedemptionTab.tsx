import { BlockStack, Box, Card, Divider, InlineStack, Layout, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

export default function RedemptionTab() {
  const { t } = useTranslation();

  return (
    <Layout>
      <Layout.Section>
        <BlockStack gap="600">
          {/* Header Section */}
          <BlockStack gap="200">
            <Text variant="headingXl" as="h1">
              {t("loyalties.redemptionTab.title")}
            </Text>
            <Text as="p" tone="subdued">
              {t("loyalties.redemptionTab.description")}
            </Text>
          </BlockStack>

          {/* Main Content Card */}
          <Card>
            <BlockStack gap="600">
              {/* Success Message */}
              <InlineStack gap="300" align="start">
                <BlockStack gap="200">
                  <Text variant="headingMd" as="h2">
                    {t("loyalties.redemptionTab.installHeading")}
                  </Text>
                  <Text as="p" tone="subdued">
                    {t("loyalties.redemptionTab.description")}
                  </Text>
                </BlockStack>
              </InlineStack>

              {/* Shopify Admin Mockup */}
              <Box padding="400" background="bg-surface-secondary" borderRadius="200">
                <Card>
                  <img
                    src="/assets/images/redemption.png"
                    alt={t("loyalties.redemptionTab.imageAlt")}
                    style={{
                      width: "auto",
                      height: "100%",
                      display: "block",
                      borderRadius: "var(--p-border-radius-200)",
                    }}
                  />
                </Card>
              </Box>

              <Divider />

              {/* Instructions */}
              <BlockStack gap="200">
                <InlineStack gap="200" align="start">
                  <Text as="span" tone="subdued">
                    1.
                  </Text>
                  <Text as="p">{t("loyalties.redemptionTab.steps.step1")}</Text>
                </InlineStack>

                <InlineStack gap="200" align="start">
                  <Text as="span" tone="subdued">
                    2.
                  </Text>
                  <Text as="p">{t("loyalties.redemptionTab.steps.step2")}</Text>
                </InlineStack>

                <InlineStack gap="200" align="start">
                  <Text as="span" tone="subdued">
                    3.
                  </Text>
                  <Text as="p">{t("loyalties.redemptionTab.steps.step3")}</Text>
                </InlineStack>
              </BlockStack>
            </BlockStack>
          </Card>
          <BlockStack gap="600"></BlockStack>
        </BlockStack>
      </Layout.Section>
    </Layout>
  );
}
