/**
 * Type definition for loyalty points data
 */
export type LoyaltyPointsData = {
  id: number;
  includeProductTotal: boolean;
  includeShipping: boolean;
  includeTaxes: boolean;
  pointsIssueType: string;
  issueDays: number;
  orderStatus: string;
  orderRefundType: string;
  redeemedRefundType: string;
  programName: string;
  pointSingular: string;
  pointPlural: string;
  pointsPerCurrency: number;
  currencyAmount: number;
  pointsRedemptionValue: number;
  pointsRedemptionAmount: number;
  maxRedeemPercentage: number;
  minPurchaseAmount: number | null;
  roundingMethod: string;
  isConvertPointsByPercentage: boolean;
};

/**
 * Type for action response
 */
export interface ActionResponse {
  success: boolean;
  error?: string;
}
