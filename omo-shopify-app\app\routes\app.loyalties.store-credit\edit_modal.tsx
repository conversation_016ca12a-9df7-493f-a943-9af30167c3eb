import { Mo<PERSON>, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Box, Button, Divider, Icon, InlineStack, Text } from "@shopify/polaris";
import { CreditCardIcon, StoreIcon } from "@shopify/polaris-icons";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

interface StoreCreditEditModalProps {
  id: string;
  navigate: (path: string) => void;
  isOpen?: boolean;
  onHide: () => void;
}

export default function StoreCreditEditModal({
  id,
  navigate,
  isOpen,
  onHide,
}: Readonly<StoreCreditEditModalProps>) {
  const { t } = useTranslation();

  useEffect(() => {
    console.log("StoreCreditEditModal mounted");
  });

  return (
    <Modal id={id} open={isOpen} onHide={onHide}>
      <BlockStack gap="400">
        <div
          style={{
            padding: "16px 20px",
            borderBottom: "1px solid #e1e3e5",
          }}
        >
          <Text variant="bodyMd" as="h3" tone="subdued">
            {t("loyalties.storeCredit.editModal.description")}
          </Text>
          <Box paddingBlockEnd="300" />

          <BlockStack gap="500">
            <InlineStack align="space-between" gap="400">
              <InlineStack gap="400" align="start">
                <Box paddingBlockStart="100">
                  <Icon source={StoreIcon} />
                </Box>
                <BlockStack gap="100">
                  <Text variant="headingMd" as="h2" fontWeight="semibold">
                    {t("loyalties.storeCredit.editModal.placeOrder.title")}
                  </Text>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {t("loyalties.storeCredit.editModal.placeOrder.description")}
                  </Text>
                </BlockStack>
              </InlineStack>
              <Button onClick={() => navigate("/app/loyalties/place-an-order")}>
                {t("common.edit")}
              </Button>
            </InlineStack>

            <Divider />

            <InlineStack align="space-between" gap="400">
              <InlineStack gap="400" align="start">
                <Box paddingBlockStart="100">
                  <Icon source={CreditCardIcon} />
                </Box>
                <BlockStack gap="100">
                  <Text variant="headingMd" as="h2" fontWeight="semibold">
                    {t("loyalties.storeCredit.editModal.subscribeMembership.title")}
                  </Text>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {t("loyalties.storeCredit.editModal.subscribeMembership.description")}
                  </Text>
                </BlockStack>
              </InlineStack>
              <Button>
                {" "}
                {/* TODO: navigate to a real page */}
                {t("common.edit")}
              </Button>
            </InlineStack>
          </BlockStack>
        </div>
      </BlockStack>

      <TitleBar title={t("loyalties.storeCredit.editModal.title")} />
    </Modal>
  );
}
