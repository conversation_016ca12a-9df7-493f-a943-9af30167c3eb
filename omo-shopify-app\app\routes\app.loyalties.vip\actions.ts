import { LoyaltyProgramType } from "@prisma/client";
import { ActionFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import {
  DELETE_VIP_TIER,
  POINT_ENTRY_METHOD,
  UPDATE_ENTRY_METHOD,
  UPDATE_EXPIRATION,
  UPDATE_VALIDATION,
} from "./constants";
import {
  DeleteVIPTierFormData,
  EntryMethodFormData,
  ExpirationFormData,
  ValidationFormData,
} from "./types";

/**
 * Update entry method action
 */
async function updateEntryMethod(loyaltyProgramId: number, formData: EntryMethodFormData) {
  const { entryMethod, ordersCount } = formData;

  const vipSettings = await db.loyaltyVIPSettings.update({
    where: { loyaltyProgramId },
    data: {
      entryMethod,
      ordersCount,
    },
    include: {
      loyaltyProgram: {
        include: {
          vipTiers: true,
        },
      },
    },
  });

  const vipTiers = vipSettings?.loyaltyProgram?.vipTiers;
  if (vipTiers && vipTiers.length > 0) {
    await db.loyaltyVIPTier.updateMany({
      where: { loyaltyProgramId },
      data: entryMethod === POINT_ENTRY_METHOD ? { spendRequirement: 0 } : { pointsRequirement: 0 },
    });
  }

  return Response.json({ success: true, message: "Entry method updated successfully" });
}

/**
 * Update validation settings action
 */
async function updateValidation(loyaltyProgramId: number, formData: ValidationFormData) {
  const { validationType, validationDays } = formData;

  await db.loyaltyVIPSettings.update({
    where: { loyaltyProgramId },
    data: {
      validationType,
      validationDays,
    },
  });

  return Response.json({
    success: true,
    message: "Validation settings updated successfully",
  });
}

/**
 * Update expiration settings action
 */
async function updateExpiration(loyaltyProgramId: number, formData: ExpirationFormData) {
  const { expirationType, expirationDays } = formData;

  await db.loyaltyVIPSettings.update({
    where: { loyaltyProgramId },
    data: {
      expirationType,
      expirationDays,
    },
  });

  return Response.json({
    success: true,
    message: "Expiration settings updated successfully",
  });
}

/**
 * Delete VIP tier action
 */
async function deleteVipTier(formData: DeleteVIPTierFormData) {
  const { tierId } = formData;

  if (!tierId || isNaN(Number(tierId))) {
    return Response.json({ success: false, error: "Invalid tier ID" }, { status: 400 });
  }

  try {
    // First delete all rewards associated with the tier
    await db.loyaltyVIPReward.deleteMany({
      where: {
        vipTierId: Number(tierId),
      },
    });

    // Then delete the tier itself
    await db.loyaltyVIPTier.delete({
      where: {
        id: Number(tierId),
      },
    });

    return Response.json({
      success: true,
      message: "VIP tier deleted successfully",
    });
  } catch (error: unknown) {
    console.error("Error deleting VIP tier:", error);
    return Response.json({ success: false, error: "Failed to delete VIP tier" }, { status: 500 });
  }
}

/**
 * Main action function for VIP loyalty program
 */
export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    return Response.json(
      { success: false, error: `Shop not found for domain ${session.shop}` },
      { status: 400 },
    );
  }

  // Parse the JSON data
  const rawFormData = await request.json();
  const actionType = rawFormData.actionType as string;

  // Define a type for the form data based on the action type
  type FormDataType =
    | EntryMethodFormData
    | ValidationFormData
    | ExpirationFormData
    | DeleteVIPTierFormData;

  // Cast the form data to the appropriate type
  const formData = rawFormData as FormDataType;

  try {
    // Handle delete VIP tier action separately as it doesn't require loyalty program
    if (actionType === DELETE_VIP_TIER) {
      return deleteVipTier(formData as DeleteVIPTierFormData);
    }

    // For other actions, we need to find the VIP loyalty program with its settings
    const loyaltyProgram = await db.loyaltyProgram.findFirst({
      where: {
        shopId: shop.id,
        programType: LoyaltyProgramType.VIP_TIER,
      },
      include: {
        vipSettings: true,
      },
    });

    if (!loyaltyProgram) {
      return Response.json(
        { success: false, error: "VIP loyalty program not found" },
        { status: 400 },
      );
    }

    // Check if VIP settings exist
    if (!loyaltyProgram.vipSettings) {
      return Response.json({ success: false, error: "VIP settings not found" }, { status: 400 });
    }

    switch (actionType) {
      case UPDATE_ENTRY_METHOD:
        return updateEntryMethod(loyaltyProgram.id, formData as EntryMethodFormData);

      case UPDATE_VALIDATION:
        return updateValidation(loyaltyProgram.id, formData as ValidationFormData);

      case UPDATE_EXPIRATION:
        return updateExpiration(loyaltyProgram.id, formData as ExpirationFormData);

      default:
        return Response.json({ success: false, error: "Invalid action type" }, { status: 400 });
    }
  } catch (error: unknown) {
    console.error("Error processing VIP action:", error);
    return Response.json({ success: false, error: "Failed to process action" }, { status: 500 });
  }
}
