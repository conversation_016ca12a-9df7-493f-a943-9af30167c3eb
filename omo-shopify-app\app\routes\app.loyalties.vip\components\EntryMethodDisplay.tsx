import { useFetcher } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import { BlockStack, Button, Card, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import { CircleDollarSign, DollarSign } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActionResponse } from "../types";
import IconCard from "./IconCard";
import UnlockMethodSelector from "./UnlockMethodSelector";

interface EntryMethodDisplayProps {
  /**
   * Initial method value from the server
   * @default "points"
   */
  initialMethod?: string;

  /**
   * Initial orders count value from the server
   * @default false
   */
  initialOrdersCount?: boolean;

  /**
   * Action type for form submission
   */
  actionType: string;
}

/**
 * A component that displays the entry method for VIP tiers
 */
export default function EntryMethodDisplay({
  initialMethod = "points",
  initialOrdersCount = false,
  actionType,
}: Readonly<EntryMethodDisplayProps>) {
  // Get App Bridge for toast notifications
  const shopify = useAppBridge();

  // Get the fetcher for form submissions
  const fetcher = useFetcher<ActionResponse>();

  // Get translation function
  const { t } = useTranslation();

  // Internal state
  const [isEditEntryMethod, setIsEditEntryMethod] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState(initialMethod);
  const [ordersCountChecked, setOrdersCountChecked] = useState(initialOrdersCount);

  // Update internal state when props change
  useEffect(() => {
    setSelectedMethod(initialMethod);
    setOrdersCountChecked(initialOrdersCount);
  }, [initialMethod, initialOrdersCount]);

  // Effect to handle form submission responses
  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      if (fetcher.data.success) {
        // Show success toast using App Bridge Toast API
        shopify?.toast.show(
          fetcher.data.message ?? t("loyalties.vip.toastMessages.entryMethodUpdated"),
          {
            isError: false,
            duration: 4000,
          },
        );
      } else if (fetcher.data.error) {
        // Show error toast using App Bridge Toast API
        shopify?.toast.show(fetcher.data.error, {
          isError: true,
          duration: 4000,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.state]);

  const handleEditEntryMethod = useCallback(() => {
    setIsEditEntryMethod(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsEditEntryMethod(false);
    // Reset to initial values
    setSelectedMethod(initialMethod);
    setOrdersCountChecked(initialOrdersCount);
  }, [initialMethod, initialOrdersCount]);

  const handleUpdateMethod = useCallback(
    (method: string, ordersCount: boolean) => {
      // Update local state
      setSelectedMethod(method);
      setOrdersCountChecked(ordersCount);
      setIsEditEntryMethod(false);

      // Submit to server
      const formData = {
        actionType: actionType,
        entryMethod: method,
        ordersCount: ordersCount,
      };

      fetcher.submit(formData, { method: "post", encType: "application/json" });
    },
    [actionType, fetcher],
  );

  return (
    <BlockStack gap="200">
      <Text variant="headingLg" as="h5">
        {t("loyalties.vip.entryMethod.title")}
      </Text>

      <Card roundedAbove="xs">
        <InlineGrid columns={2}>
          <BlockStack gap="200">
            <Text variant="headingMd" as="h2">
              {t("loyalties.vip.entryMethod.title")}
            </Text>

            <Text as="p" variant="bodyMd" tone="subdued">
              {t("loyalties.vip.entryMethod.description")}
            </Text>
          </BlockStack>
          {!isEditEntryMethod && (
            <InlineStack gap={"200"} align="space-between" blockAlign="center">
              <BlockStack gap={"200"}>
                {selectedMethod === "points" ? (
                  <IconCard
                    icon={<CircleDollarSign />}
                    text={t("loyalties.vip.entryMethod.pointsEarned")}
                  />
                ) : (
                  <IconCard
                    icon={<DollarSign />}
                    text={t("loyalties.vip.entryMethod.amountSpent")}
                  />
                )}
                {ordersCountChecked && (
                  <IconCard
                    icon={<DollarSign />}
                    text={t("loyalties.vip.entryMethod.ordersCount")}
                  />
                )}
              </BlockStack>

              <Button onClick={handleEditEntryMethod} variant="tertiary">
                {t("loyalties.vip.entryMethod.edit")}
              </Button>
            </InlineStack>
          )}
          {isEditEntryMethod && (
            <UnlockMethodSelector
              initialMethod={selectedMethod}
              initialOrdersCount={ordersCountChecked}
              onCancel={handleCancelEdit}
              onUpdate={handleUpdateMethod}
            />
          )}
        </InlineGrid>
      </Card>
    </BlockStack>
  );
}
