import { useFetcher } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Card,
  InlineGrid,
  InlineStack,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface ExpirationSettingsDisplayProps {
  /**
   * Initial expiration type from the server
   */
  initialExpirationType: string;

  /**
   * Initial expiration days from the server
   */
  initialExpirationDays: number;

  /**
   * Default expiration type constant
   */
  defaultExpirationType: string;

  /**
   * Action type for form submission
   */
  actionType: string;
}

/**
 * A component that displays and allows editing of the expiration settings
 */
export default function ExpirationSettingsDisplay({
  initialExpirationType,
  initialExpirationDays,
  defaultExpirationType,
  actionType,
}: Readonly<ExpirationSettingsDisplayProps>) {
  // Get App Bridge for toast notifications
  const shopify = useAppBridge();

  // Fetcher for form submissions
  const fetcher = useFetcher();

  // Get translation function
  const { t } = useTranslation();

  // Main state for expiration settings
  const [expirationType, setExpirationType] = useState(initialExpirationType);
  const [expirationDays, setExpirationDays] = useState(initialExpirationDays.toString());

  // State for editing expiration
  const [isEditingExpiration, setIsEditingExpiration] = useState(false);
  const [tempExpiration, setTempExpiration] = useState(expirationType);
  const [tempExpirationDays, setTempExpirationDays] = useState(expirationDays);

  // Update internal state when props change
  useEffect(() => {
    setExpirationType(initialExpirationType);
    setExpirationDays(initialExpirationDays.toString());
  }, [initialExpirationType, initialExpirationDays]);

  // Effect to handle form submission responses
  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      const response = fetcher.data as { success: boolean; message?: string; error?: string };
      if (response.success) {
        // Show success toast using App Bridge Toast API
        shopify?.toast.show(
          response.message ?? t("loyalties.vip.toastMessages.expirationUpdated"),
          {
            isError: false,
            duration: 4000,
          },
        );
      } else if (response.error) {
        // Show error toast using App Bridge Toast API
        shopify?.toast.show(response.error, {
          isError: true,
          duration: 4000,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.state]);

  const handleEditExpiration = useCallback(() => {
    // Initialize temp values with current values
    setTempExpiration(expirationType);
    setTempExpirationDays(expirationDays);
    setIsEditingExpiration(true);
  }, [expirationType, expirationDays]);

  const handleCancelExpirationEdit = useCallback(() => {
    setIsEditingExpiration(false);
    // Reset temp values to current values
    setTempExpiration(expirationType);
    setTempExpirationDays(expirationDays);
  }, [expirationType, expirationDays]);

  const handleSaveExpiration = useCallback(() => {
    setIsEditingExpiration(false);

    // Calculate expiration days
    const days =
      tempExpiration !== defaultExpirationType ? parseInt(tempExpirationDays ?? "0", 10) : 0;

    // Update local state
    setExpirationType(tempExpiration);
    setExpirationDays(days.toString());

    // Submit to server
    const formData = {
      actionType: actionType,
      expirationType: tempExpiration,
      expirationDays: days,
    };

    fetcher.submit(formData, { method: "post", encType: "application/json" });
  }, [tempExpiration, tempExpirationDays, defaultExpirationType, actionType, fetcher]);

  return (
    <BlockStack gap="200">
      <Text variant="headingLg" as="h5">
        {t("loyalties.vip.settings.title")}
      </Text>
      <Card roundedAbove="xs">
        <InlineGrid columns={2}>
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">
              {t("loyalties.vip.settings.expiration.title")}
            </Text>
            <Text as="p" variant="bodyMd" tone="subdued">
              {t("loyalties.vip.settings.expiration.description")}
            </Text>
          </BlockStack>

          <BlockStack gap="200" align="center">
            {isEditingExpiration ? (
              <>
                <Select
                  label=""
                  labelHidden
                  options={[
                    {
                      label: t("loyalties.vip.settings.expiration.lifetime"),
                      value: defaultExpirationType,
                    },
                    {
                      label: t("loyalties.vip.settings.expiration.periodOfTime"),
                      value: "period_of_time",
                    },
                  ]}
                  value={tempExpiration}
                  onChange={setTempExpiration}
                />
                {tempExpiration !== defaultExpirationType && (
                  <TextField
                    label={t("loyalties.vip.settings.expiration.periodOfTime")}
                    autoComplete={"off"}
                    labelHidden
                    suffix={t("loyalties.vip.settings.expiration.days")}
                    type="number"
                    min={1}
                    value={tempExpirationDays}
                    onChange={setTempExpirationDays}
                  />
                )}

                <Box paddingBlockStart="300">
                  <InlineStack align="end" gap="200">
                    <Button variant="tertiary" onClick={handleCancelExpirationEdit}>
                      {t("loyalties.vip.settings.expiration.cancel")}
                    </Button>
                    <Button variant="primary" onClick={handleSaveExpiration}>
                      {t("loyalties.vip.settings.expiration.save")}
                    </Button>
                  </InlineStack>
                </Box>
              </>
            ) : (
              <InlineStack gap="200" align="space-between" blockAlign="center">
                <Text as="p" variant="bodyMd">
                  {expirationType === defaultExpirationType
                    ? t("loyalties.vip.settings.expiration.lifetime")
                    : `${expirationDays} ${t("loyalties.vip.settings.expiration.days")}`}
                </Text>

                <Button variant="tertiary" onClick={handleEditExpiration}>
                  {t("loyalties.vip.entryMethod.edit")}
                </Button>
              </InlineStack>
            )}
          </BlockStack>
        </InlineGrid>
      </Card>
    </BlockStack>
  );
}
