import {
  Box,
  BoxProps,
  Card,
  CardProps,
  InlineStack,
  InlineStackProps,
  Text,
} from "@shopify/polaris";
import React, { ReactElement } from "react";

interface IconCardProps {
  /**
   * The icon to display in the card
   */
  icon: ReactElement;

  /**
   * The text to display next to the icon
   */
  text: string;

  /**
   * Optional padding for the card
   * @default "0"
   */
  cardPadding?: CardProps["padding"];

  /**
   * Optional padding for the box inside the card
   * @default "300"
   */
  boxPadding?: BoxProps["padding"];

  /**
   * Optional gap between icon and text
   * @default "300"
   */
  gap?: InlineStackProps["gap"];

  /**
   * Optional icon size
   * @default 16
   */
  iconSize?: number;

  /**
   * Optional icon color
   * @default "#718096"
   */
  iconColor?: string;
}

/**
 * A card component that displays an icon with text
 */
export default function IconCard({
  icon,
  text,
  cardPadding = "0",
  boxPadding = "300",
  gap = "300",
  iconSize = 16,
  iconColor = "#718096",
}: Readonly<IconCardProps>) {
  return (
    <Card padding={cardPadding}>
      <Box padding={boxPadding}>
        <InlineStack gap={gap} align="center">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "24px",
              height: "24px",
              borderRadius: "50%",
              border: `1px solid ${iconColor}`,
            }}
          >
            {/* Clone the icon element with the specified size and color */}
            {React.isValidElement(icon)
              ? React.cloneElement(icon as ReactElement<any>, {
                  size: iconSize,
                  color: iconColor,
                })
              : icon}
          </div>
          <Text as="span" variant="bodyMd">
            {text}
          </Text>
        </InlineStack>
      </Box>
    </Card>
  );
}
