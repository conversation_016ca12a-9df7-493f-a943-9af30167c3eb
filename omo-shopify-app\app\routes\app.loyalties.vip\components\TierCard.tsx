import { BlockS<PERSON>ck, Box, Button, ButtonGroup, Icon, InlineStack, Text } from "@shopify/polaris";
import { StarIcon } from "@shopify/polaris-icons";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { AMOUNT_ENTRY_METHOD, POINT_ENTRY_METHOD } from "../constants";

interface TierCardProps {
  /**
   * The name of the tier
   */
  tierName: string;

  /**
   * The tone of the icon
   * @default "info"
   */
  iconTone?: "info" | "base" | "warning" | "success" | "critical";

  /**
   * The entry method of Vip tier
   */
  entryMethod?: string;

  /**
   * The amount spent (in currency)
   * @default "0"
   */
  spentAmount?: string;

  /**
   * The amount spent (in currency)
   * @default "0"
   */
  pointEarn?: string;

  /**
   * The currency code
   * @default "TWD"
   */
  currency?: string;

  /**
   * The number of orders
   * @default "0"
   */
  ordersCount?: string;

  /**
   * Callback function when the Edit button is clicked
   */
  onEdit?: () => void;

  /**
   * Callback function when the Delete button is clicked
   */
  onDelete?: () => void;

  /**
   * Whether to show the spent amount and orders count
   * @default true
   */
  showDetails?: boolean;
}

/**
 * A component that displays a VIP tier card
 */
export default function TierCard({
  tierName,
  iconTone = "info",
  spentAmount = "0",
  pointEarn = "0",
  currency = "TWD",
  ordersCount = "0",
  entryMethod = POINT_ENTRY_METHOD,
  onEdit,
  onDelete,
  showDetails = true,
}: Readonly<TierCardProps>) {
  const { t } = useTranslation();

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit();
    }
  }, [onEdit]);

  const handleDelete = useCallback(() => {
    if (onDelete) {
      onDelete();
    }
  }, [onDelete]);

  return (
    <Box padding="300">
      <InlineStack align="space-between">
        <InlineStack gap="300">
          <Box background="bg-surface-secondary" padding="200" borderRadius="100">
            <Icon source={StarIcon} tone={iconTone} />
          </Box>
          <BlockStack gap="100">
            <Text as="h4" variant="headingMd">
              {tierName}
            </Text>
            {showDetails && (
              <Text as="p" variant="bodyMd" tone="subdued">
                {entryMethod === AMOUNT_ENTRY_METHOD &&
                  t("common.spent", { amount: spentAmount, currency: currency })}{" "}
                {entryMethod === POINT_ENTRY_METHOD && t("common.points", { points: pointEarn })}{" "}
                {ordersCount !== "0" && `+ ${ordersCount} ${t("common.orders")}`}
              </Text>
            )}
          </BlockStack>
        </InlineStack>
        <ButtonGroup>
          <Button variant="tertiary" onClick={handleEdit}>
            {t("common.edit")}
          </Button>
          <Button variant="tertiary" tone="critical" onClick={handleDelete}>
            {t("common.delete")}
          </Button>
        </ButtonGroup>
      </InlineStack>
    </Box>
  );
}
