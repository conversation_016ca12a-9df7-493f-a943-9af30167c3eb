import { BlockStack, Box, Button, Checkbox, InlineStack, Select, Text } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";

interface UnlockMethodSelectorProps {
  /**
   * Callback function when the user clicks the Cancel button
   */
  onCancel: () => void;

  /**
   * Callback function when the user clicks the Update button
   * @param method The selected method
   * @param ordersCount Whether orders count is checked
   */
  onUpdate: (method: string, ordersCount: boolean) => void;

  /**
   * Initial selected method
   * @default "points"
   */
  initialMethod?: string;

  /**
   * Initial orders count checked state
   * @default false
   */
  initialOrdersCount?: boolean;
}

/**
 * A component for selecting the method for customers to unlock VIP tiers
 */
export default function UnlockMethodSelector({
  onCancel,
  onUpdate,
  initialMethod = "points",
  initialOrdersCount = false,
}: Readonly<UnlockMethodSelectorProps>) {
  const { t } = useTranslation();
  const [selectedMethod, setSelectedMethod] = useState(initialMethod);
  const [ordersCountChecked, setOrdersCountChecked] = useState(initialOrdersCount);

  const options = [
    { label: t("loyalties.vip.entryMethod.pointsEarned"), value: "points" },
    { label: t("loyalties.vip.entryMethod.amountSpent"), value: "amount" },
  ];

  const handleMethodChange = useCallback((value: string) => {
    setSelectedMethod(value);
  }, []);

  const handleOrdersCountChange = useCallback((checked: boolean) => {
    setOrdersCountChecked(checked);
  }, []);

  const handleUpdate = useCallback(() => {
    onUpdate(selectedMethod, ordersCountChecked);
  }, [onUpdate, selectedMethod, ordersCountChecked]);

  return (
    <BlockStack gap="400">
      <Text variant="headingMd" as="h2">
        {t("loyalties.vip.entryMethod.unlockMethod")}
      </Text>

      <Select
        label=""
        labelHidden
        options={options}
        value={selectedMethod}
        onChange={handleMethodChange}
      />

      <Checkbox
        label={t("loyalties.vip.entryMethod.ordersCount")}
        checked={ordersCountChecked}
        onChange={handleOrdersCountChange}
        disabled
      />

      <Box paddingBlockStart="400">
        <InlineStack align="end" gap="300">
          <Button variant="tertiary" onClick={onCancel}>
            {t("common.cancel")}
          </Button>
          <Button variant="primary" onClick={handleUpdate}>
            {t("common.update")}
          </Button>
        </InlineStack>
      </Box>
    </BlockStack>
  );
}
