import { LoyaltyProgramType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import {
  DEFAULT_ENTRY_METHOD,
  DEFAULT_EXPIRATION_DAYS,
  DEFAULT_EXPIRATION_TYPE,
  DEFAULT_ORDERS_COUNT,
  DEFAULT_VALIDATION_DAYS,
  DEFAULT_VALIDATION_TYPE,
  POINT_ENTRY_METHOD,
} from "./constants";
import { LoaderData, LoyaltyProgramWithVIPSettings } from "./types";

/**
 * Loader function for VIP loyalty program
 */
export async function loader({ request }: LoaderFunctionArgs): Promise<LoaderData> {
  const { session } = await authenticate.admin(request);

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    throw new Error(`Shop not found for domain ${session.shop}`);
  }

  // Find the VIP loyalty program
  let loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipSettings: true,
    },
  });

  // Create the VIP loyalty program if it doesn't exist
  if (!loyaltyProgram) {
    loyaltyProgram = await db.loyaltyProgram.create({
      data: {
        shopId: shop.id,
        programType: LoyaltyProgramType.VIP_TIER,
        isActive: true,
        vipSettings: {
          create: {
            // Default settings
            entryMethod: DEFAULT_ENTRY_METHOD,
            ordersCount: DEFAULT_ORDERS_COUNT,
            validationType: DEFAULT_VALIDATION_TYPE,
            validationDays: DEFAULT_VALIDATION_DAYS,
            expirationType: DEFAULT_EXPIRATION_TYPE,
            expirationDays: DEFAULT_EXPIRATION_DAYS,
          },
        },
      },
      include: {
        vipSettings: true,
      },
    });
  } else if (!loyaltyProgram.vipSettings) {
    // Create VIP settings if they don't exist
    await db.loyaltyVIPSettings.create({
      data: {
        loyaltyProgramId: loyaltyProgram.id,
        // Default settings
        entryMethod: DEFAULT_ENTRY_METHOD,
        ordersCount: DEFAULT_ORDERS_COUNT,
        validationType: DEFAULT_VALIDATION_TYPE,
        validationDays: DEFAULT_VALIDATION_DAYS,
        expirationType: DEFAULT_EXPIRATION_TYPE,
        expirationDays: DEFAULT_EXPIRATION_DAYS,
      },
    });

    // Reload the loyalty program with the new settings
    loyaltyProgram = await db.loyaltyProgram.findFirst({
      where: {
        id: loyaltyProgram.id,
      },
      include: {
        vipSettings: true,
      },
    });
  }

  // Fetch VIP tiers if the program exists
  const vipTiers = await db.loyaltyVIPTier.findMany({
    where: {
      loyaltyProgramId: loyaltyProgram?.id,
    },
    orderBy:
      loyaltyProgram?.vipSettings?.entryMethod === POINT_ENTRY_METHOD
        ? { pointsRequirement: "asc" }
        : { spendRequirement: "asc" },
    include: {
      rewards: true,
    },
  });

  // Cast the loyalty program to include vipSettings
  return {
    vipTiers,
    loyaltyProgram: loyaltyProgram as unknown as LoyaltyProgramWithVIPSettings,
    shop,
  };
}
