// File: app/routes/app.members/services.ts
import { LoyaltyProgramType } from "@prisma/client";
import db from "../../db.server";
import { DEFAULT_ENTRY_METHOD, POINT_ENTRY_METHOD } from "./constants";

/**
 * Fetches customers list with pagination
 */
export async function getVipSettings(shopDomain: string) {
  if (!shopDomain) return;
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: shopDomain,
    },
  });
  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop?.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipSettings: true,
    },
  });
  const entryMethod = loyaltyProgram?.vipSettings?.entryMethod ?? DEFAULT_ENTRY_METHOD;

  // Fetch VIP tiers if the program exists
  const vipTiers = await db.loyaltyVIPTier.findMany({
    where: {
      loyaltyProgramId: loyaltyProgram?.id,
    },
    orderBy:
      entryMethod === POINT_ENTRY_METHOD
        ? { pointsRequirement: "asc" }
        : { spendRequirement: "asc" },
    include: {
      rewards: true,
    },
  });

  return { vipTiers, entryMethod };
}
