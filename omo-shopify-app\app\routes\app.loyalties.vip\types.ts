import { LoyaltyProgramType } from "@prisma/client";
import {
  DELETE_VIP_TIER,
  UPDATE_ENTRY_METHOD,
  UPDATE_EXPIRATION,
  UPDATE_VALIDATION,
} from "./constants";

/**
 * Response type for action functions
 */
export interface ActionResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Type for VIP tier reward
 */
export interface VIPTierReward {
  id: number;
  title: string;
  rewardType: string;
  value: string | null;
}

/**
 * Type for VIP tier
 */
export interface VIPTier {
  id: number;
  name: string;
  loyaltyProgramId: number;
  spendRequirement: number | null;
  pointsRequirement: number | null;
  createdAt: Date;
  updatedAt: Date;
  rewards: VIPTierReward[];
}

/**
 * Type for VIP settings
 */
export interface LoyaltyVIPSettings {
  id: number;
  loyaltyProgramId: number;
  entryMethod: string;
  ordersCount: boolean;
  validationType: string;
  validationDays: number;
  expirationType: string;
  expirationDays: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Type for loyalty program with VIP settings
 */
export interface LoyaltyProgramWithVIPSettings {
  id: number;
  shopId: number;
  isActive: boolean;
  programType: LoyaltyProgramType;
  createdAt: Date;
  updatedAt: Date;
  vipSettings: LoyaltyVIPSettings | null;
}

/**
 * Type for loader data
 */
export interface LoaderData {
  vipTiers: VIPTier[];
  loyaltyProgram: LoyaltyProgramWithVIPSettings;
  shop: any;
}

/**
 * Interface for entry method form data
 */
export interface EntryMethodFormData {
  actionType: typeof UPDATE_ENTRY_METHOD;
  entryMethod: string; // "points" or "amount"
  ordersCount: boolean;
}

/**
 * Interface for validation settings form data
 */
export interface ValidationFormData {
  actionType: typeof UPDATE_VALIDATION;
  validationType: string; // "immediately" or "days_after_paid"
  validationDays: number;
}

/**
 * Interface for expiration settings form data
 */
export interface ExpirationFormData {
  actionType: typeof UPDATE_EXPIRATION;
  expirationType: string; // "lifetime" or "period_of_time"
  expirationDays: number;
}

/**
 * Interface for delete VIP tier form data
 */
export interface DeleteVIPTierFormData {
  actionType: typeof DELETE_VIP_TIER;
  tierId: string;
}
