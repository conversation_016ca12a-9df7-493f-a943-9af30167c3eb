import { isRouteErrorResponse, useRouteError } from "@remix-run/react";

export function ErrorBoundary() {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div>
        <h1>
          {error.status} {error.statusText}
        </h1>
        <p>{error.data}</p>
      </div>
    );
  } else if (error instanceof Error) {
    // Check if this is our special error format
    if (error.message.startsWith("SHOP_NOT_FOUND:")) {
      const shop = error.message.split(":")[1];
      return (
        <div>
          <h1>Error</h1>
          <p>Shop not found for id {shop}</p>
        </div>
      );
    }

    return (
      <div>
        <h1>Error</h1>
        <p>{error.message}</p>
        <p>The stack trace is:</p>
        <pre>{error.stack}</pre>
      </div>
    );
  } else {
    return <h1>Unknown Error</h1>;
  }
}

export default ErrorBoundary;
