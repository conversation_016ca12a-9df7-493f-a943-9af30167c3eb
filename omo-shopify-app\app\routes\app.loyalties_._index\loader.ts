import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import type { WaysEarnReward } from "./interface";

/**
 * Loader function for the loyalties index page
 * Fetches loyalty programs, ways to earn rewards, and shop information
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    throw new Error(`Shop not found for id ${session.shop}`);
  }

  // Fetch all loyalty programs for this shop
  const loyaltyPrograms = await db.loyaltyProgram.findMany({
    where: {
      shopId: shop.id,
    },
  });

  // Fetch all default ways to earn reward types
  // This is used to display the available options in the UI
  // and to ensure that the user can select from the available options
  const allDefaults = await db.defaultWaysEarnRewardType.findMany({
    orderBy: { id: "asc" },
  });

  //
  const existing = await db.waysEarnReward.findMany({
    where: { shopId: shop.id },
    select: { defaultWaysEarnRewardTypeId: true },
  });
  const takenIds = new Set(existing.map((e) => e.defaultWaysEarnRewardTypeId));

  //
  const available = allDefaults.filter((d) => !takenIds.has(d.id));

  //
  const saved = await db.waysEarnReward.findMany({
    where: { shopId: shop.id },
    include: { DefaultWaysEarnRewardType: true },
    orderBy: { defaultWaysEarnRewardTypeId: "asc" },
  });

  // Fetch all WaysEarnReward for this shop, order by key
  const waysEarnRewards = await db.waysEarnReward.findMany({
    where: { shopId: shop.id },
    include: { DefaultWaysEarnRewardType: true },
    orderBy: { defaultWaysEarnRewardTypeId: "asc" },
  });

  return {
    shopId: shop.id,
    available,
    saved,
    loyaltyPrograms,
    shop,
    waysEarnRewards,
  };
}

/**
 * Type for the loader data
 */
export type LoaderData = {
  shopId: number;
  available: Array<{
    id: number;
    code: string;
    label?: string;
    subtitle?: string;
  }>;
  saved: WaysEarnReward[];
  loyaltyPrograms: Array<{
    id: number;
    shopId: number;
    programType: string;
    isActive: boolean;
  }>;
  shop: {
    id: number;
    myshopifyDomain: string;
    [key: string]: any;
  };
  waysEarnRewards: WaysEarnReward[];
};
