import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  // Get the shop details from Shopify
  const response = await admin.graphql(`
    #graphql
      query GetShopDetails {
        shop {
          id
          name
          myshopifyDomain
        }
      }
    `);

  const responseJson = await response.json();
  const { id: localShopId } = await db.shop.findFirstOrThrow({
    where: {
      shopId: responseJson.data.shop.id,
    },
    select: {
      id: true,
      shopId: true,
    },
  });

  // Get the birthday loyalty program details
  const birthdayLoyaltyProgram = await db.birthdayProgramSettings.upsert({
    where: {
      shopId: localShopId,
    },
    create: {
      shopId: localShopId,
      pageTitle: "Celebrate a birthday",
    },
    update: {},
    select: {
      id: true,
      pageTitle: true,
      isActive: true,
      rewards: true,
    },
  });
  return birthdayLoyaltyProgram;
};
