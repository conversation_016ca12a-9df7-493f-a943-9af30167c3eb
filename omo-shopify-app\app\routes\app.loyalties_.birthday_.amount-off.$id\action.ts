import type { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import {
  getFunctionConfigurationStatus,
  removeFunctionConfiguration,
  syncTierDiscountConfigToFunction,
} from "./tierDiscountSyncService";
import { syncAllCustomersFromMetafields } from "./tierTagService";

// Helper function to create Shopify AUTOMATIC discount
async function createShopifyDiscount(admin: any, discountData: any) {
  const mutation = `
    mutation discountAutomaticBasicCreate($automaticBasicDiscount: DiscountAutomaticBasicInput!) {
      discountAutomaticBasicCreate(automaticBasicDiscount: $automaticBasicDiscount) {
        automaticDiscountNode {
          id
          automaticDiscount {
            ... on DiscountAutomaticBasic {
              title
              startsAt
              endsAt
              status
              summary
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    automaticBasicDiscount: discountData,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to create Shopify DISCOUNT CODE
async function createShopifyDiscountCode(admin: any, discountData: any) {
  const mutation = `
    mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
      discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
        codeDiscountNode {
          id
          codeDiscount {
            ... on DiscountCodeBasic {
              title
              codes(first: 1) {
                nodes {
                  code
                }
              }
              startsAt
              endsAt
              status
              summary
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    basicCodeDiscount: discountData,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to delete Shopify AUTOMATIC discount
async function deleteShopifyDiscount(admin: any, discountId: string) {
  const mutation = `
    mutation discountAutomaticDelete($id: ID!) {
      discountAutomaticDelete(id: $id) {
        deletedAutomaticDiscountId
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    id: discountId,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to delete Shopify DISCOUNT CODE
async function deleteShopifyDiscountCode(admin: any, discountId: string) {
  const mutation = `
    mutation discountCodeDelete($id: ID!) {
      discountCodeDelete(id: $id) {
        deletedCodeDiscountId
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    id: discountId,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to delete discount (automatic OR code)
async function deleteShopifyDiscountAny(admin: any, discountId: string, isDiscountCode = false) {
  if (isDiscountCode) {
    return await deleteShopifyDiscountCode(admin, discountId);
  } else {
    return await deleteShopifyDiscount(admin, discountId);
  }
}

// Format discount data for AUTOMATIC discounts (single discount)
function formatAutomaticDiscountForShopify(rewardData: any) {
  const {
    title,
    rewardValue,
    discountType,
    minimumRequirement,
    minimumValue,
    expiryMonths,
    combinations,
  } = rewardData;

  // Calculate end date if expiry is set
  let endsAt = null;
  if (expiryMonths) {
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + expiryMonths);
    endsAt = endDate.toISOString();
  }

  // Set up discount value
  let discountValue;
  if (discountType === "PERCENTAGE" || discountType === "percentage") {
    discountValue = {
      percentage: rewardValue / 100,
    };
  } else {
    discountValue = {
      discountAmount: {
        amount: rewardValue.toString(),
        appliesOnEachItem: false,
      },
    };
  }

  // Set up minimum requirement
  let minimumRequirementData = undefined;
  if (minimumRequirement === "AMOUNT" || minimumRequirement === "amount") {
    minimumRequirementData = {
      subtotal: {
        greaterThanOrEqualToSubtotal: minimumValue?.toString() || "0",
      },
    };
  } else if (minimumRequirement === "QUANTITY" || minimumRequirement === "quantity") {
    minimumRequirementData = {
      quantity: {
        greaterThanOrEqualToQuantity: minimumValue?.toString() || "0",
      },
    };
  }

  // Set up discount combinations
  const combinesWith = {
    orderDiscounts: combinations?.order ?? true,
    productDiscounts: combinations?.product ?? true,
    shippingDiscounts: combinations?.shipping ?? true,
  };

  const discountData = {
    title: title || "Birthday Discount",
    startsAt: new Date().toISOString(),
    ...(endsAt && { endsAt }),
    customerGets: {
      value: discountValue,
      items: {
        all: true,
      },
    },
    ...(minimumRequirementData && { minimumRequirement: minimumRequirementData }),
    combinesWith: combinesWith,
  };

  return discountData;
}

// Format discount data for DISCOUNT CODES (tier discounts)
function formatDiscountCodeForShopify(rewardData: any, tierName: string) {
  const {
    title,
    rewardValue,
    discountType,
    minimumRequirement,
    minimumValue,
    expiryMonths,
    combinations,
  } = rewardData;

  // Calculate end date
  let endsAt = null;
  if (expiryMonths) {
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + expiryMonths);
    endsAt = endDate.toISOString();
  }

  // Set up discount value
  let discountValue;
  if (discountType === "PERCENTAGE" || discountType === "percentage") {
    discountValue = {
      percentage: rewardValue / 100,
    };
  } else {
    discountValue = {
      discountAmount: {
        amount: rewardValue.toString(),
        appliesOnEachItem: false,
      },
    };
  }

  const discountTitle = `${title || "Birthday Discount"} - ${tierName}`;

  // Generate unique code
  const discountCode = `BIRTHDAY_${tierName.toUpperCase().replace(/\s+/g, "_")}_${Date.now()}`;

  // Set up minimum requirement
  let minimumRequirementData = undefined;
  if (minimumRequirement === "AMOUNT" || minimumRequirement === "amount") {
    minimumRequirementData = {
      subtotal: {
        greaterThanOrEqualToSubtotal: minimumValue?.toString() || "0",
      },
    };
  } else if (minimumRequirement === "QUANTITY" || minimumRequirement === "quantity") {
    minimumRequirementData = {
      quantity: {
        greaterThanOrEqualToQuantity: minimumValue?.toString() || "0",
      },
    };
  }

  const discountData = {
    title: discountTitle,
    code: discountCode,
    startsAt: new Date().toISOString(),
    ...(endsAt && { endsAt }),
    customerSelection: { all: true },
    customerGets: {
      value: discountValue,
      items: {
        all: true,
      },
    },
    ...(minimumRequirementData && { minimumRequirement: minimumRequirementData }),
    combinesWith: {
      orderDiscounts: combinations?.order ?? true,
      productDiscounts: combinations?.product ?? true,
      shippingDiscounts: combinations?.shipping ?? true,
    },
    usageLimit: 1000,
  };

  return discountData;
}

// Create tier-based discount CODES with Shopify Function integration
async function createTierBasedDiscountCodes(
  admin: any,
  settingsId: number,
  title: string,
  expiryMonths: number | undefined,
  tierDiscounts: any[],
  globalCombinations?: any,
) {
  const results = [];
  const errors = [];

  for (const tierDiscount of tierDiscounts) {
    try {
      // Get tier information
      const tier = await db.loyaltyVIPTier.findUnique({
        where: { id: tierDiscount.tierId },
        select: { name: true },
      });

      if (!tier) {
        console.warn(`Tier ${tierDiscount.tierId} not found`);
        continue;
      }

      const combinationsToUse = tierDiscount.combinations ||
        globalCombinations || {
          product: true,
          order: true,
          shipping: true,
        };

      // Format discount CODE data for Shopify
      const shopifyDiscountData = formatDiscountCodeForShopify(
        {
          title,
          rewardValue: tierDiscount.rewardValue,
          discountType: tierDiscount.discountType,
          minimumRequirement: tierDiscount.minimumRequirement,
          minimumValue: tierDiscount.minimumValue,
          expiryMonths,
          combinations: combinationsToUse,
        },
        tier.name,
      );

      // Create discount CODE in Shopify
      const shopifyResponse = await createShopifyDiscountCode(admin, shopifyDiscountData);

      if (shopifyResponse.data?.discountCodeBasicCreate?.userErrors?.length > 0) {
        const errorMessage = `Shopify Error for tier ${tier.name}: ${shopifyResponse.data.discountCodeBasicCreate.userErrors
          .map((error: any) => error.message)
          .join(", ")}`;
        console.error(errorMessage);
        errors.push(errorMessage);
        continue;
      }

      const shopifyDiscountId = shopifyResponse.data?.discountCodeBasicCreate?.codeDiscountNode?.id;
      const discountCode =
        shopifyResponse.data?.discountCodeBasicCreate?.codeDiscountNode?.codeDiscount?.codes
          ?.nodes?.[0]?.code;

      // Save to database with discount code
      const birthdayReward = await db.birthdayReward.create({
        data: {
          settingsId: Number(settingsId),
          birthdayRewardType: "AMOUNT_OFF",
          title: `${title} - ${tier.name}`,
          expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
          rewardValue: Number(tierDiscount.rewardValue),
          discountType: tierDiscount.discountType.toUpperCase(),
          minimumRequirement: tierDiscount.minimumRequirement.toUpperCase(),
          minimumValue: tierDiscount.minimumValue,
          productDiscounts: combinationsToUse.product,
          orderDiscounts: combinationsToUse.order,
          shippingDiscounts: combinationsToUse.shipping,
          shopifyDiscountId: shopifyDiscountId,
          discountCode: discountCode,
        },
        select: {
          id: true,
          birthdayRewardType: true,
          title: true,
          expiryMonths: true,
          rewardValue: true,
          discountType: true,
          minimumRequirement: true,
          minimumValue: true,
          productDiscounts: true,
          orderDiscounts: true,
          shippingDiscounts: true,
          shopifyDiscountId: true,
          discountCode: true,
        },
      });

      results.push(birthdayReward);
    } catch (error) {
      const errorMessage = `Error creating discount for tier ${tierDiscount.tierId}: ${error instanceof Error ? error.message : "Unknown error"}`;
      console.error(errorMessage);
      errors.push(errorMessage);
    }
  }

  return { results, errors };
}

// Create single AUTOMATIC discount
async function createSingleAutomaticDiscount(admin: any, settingsId: number, rewardData: any) {
  try {
    // Format AUTOMATIC discount data for Shopify
    const shopifyDiscountData = formatAutomaticDiscountForShopify(rewardData);

    // Create AUTOMATIC discount in Shopify
    const shopifyResponse = await createShopifyDiscount(admin, shopifyDiscountData);

    if (shopifyResponse.data?.discountAutomaticBasicCreate?.userErrors?.length > 0) {
      throw new Error(
        `Shopify Error: ${shopifyResponse.data.discountAutomaticBasicCreate.userErrors
          .map((error: any) => error.message)
          .join(", ")}`,
      );
    }

    const shopifyDiscountId =
      shopifyResponse.data?.discountAutomaticBasicCreate?.automaticDiscountNode?.id;

    // Save to database (no discount code for automatic)
    const birthdayReward = await db.birthdayReward.create({
      data: {
        settingsId: Number(settingsId),
        birthdayRewardType: "AMOUNT_OFF",
        title: rewardData.title,
        expiryMonths: rewardData.expiryMonths ? Number(rewardData.expiryMonths) : undefined,
        rewardValue: Number(rewardData.rewardValue),
        discountType: rewardData.discountType.toUpperCase(),
        minimumRequirement: rewardData.minimumRequirement.toUpperCase(),
        minimumValue: rewardData.minimumValue,
        productDiscounts: rewardData.combinations.product,
        orderDiscounts: rewardData.combinations.order,
        shippingDiscounts: rewardData.combinations.shipping,
        shopifyDiscountId: shopifyDiscountId,
        // discountCode: null for automatic discounts
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
        discountType: true,
        minimumRequirement: true,
        minimumValue: true,
        productDiscounts: true,
        orderDiscounts: true,
        shippingDiscounts: true,
        shopifyDiscountId: true,
        discountCode: true,
      },
    });

    return {
      success: true,
      message: "Automatic discount created successfully",
      ...birthdayReward,
    };
  } catch (error) {
    console.error("Error creating single automatic discount:", error);
    throw error;
  }
}

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  if (request.method === "POST") {
    const requestBody = await request.json();
    const {
      settingsId,
      title,
      expiryMonths,
      combinations,
      hasDifferentTiers,
      // Single discount fields
      rewardValue,
      discountType,
      minimumRequirement,
      minimumValue,
      // Tier discount fields
      tierDiscounts,
    } = requestBody;

    try {
      if (hasDifferentTiers && tierDiscounts && tierDiscounts.length > 0) {
        // ✅ Create tier-based discount CODES
        const { results, errors } = await createTierBasedDiscountCodes(
          admin,
          settingsId,
          title,
          expiryMonths,
          tierDiscounts,
          combinations,
        );

        // Sync customer tier tags
        try {
          console.log("🔄 Syncing customer tier tags for tier-based discount codes...");
          const syncResult = await syncAllCustomersFromMetafields(admin);

          if (syncResult.errors.length > 0) {
            console.warn("⚠️ Some customers failed to sync:", syncResult.errors);
          }

          console.log(
            `✅ Successfully synced ${syncResult.results.length} customers with tier tags`,
          );
        } catch (syncError) {
          console.error("❌ Failed to sync customer tiers:", syncError);
        }

        // 🎯 NEW: Sync tier discount configuration to Shopify Function
        try {
          console.log("🔄 Syncing tier discount configuration to Shopify Function...");
          const functionSyncResult = await syncTierDiscountConfigToFunction(admin, settingsId, db);

          console.log(`🎉 Function sync successful: ${functionSyncResult.message}`);
        } catch (functionSyncError) {
          console.error("❌ Failed to sync Shopify Function configuration:", functionSyncError);
          // Don't fail the entire operation for function sync errors
        }

        if (errors.length > 0) {
          return {
            success: false,
            error: `Some discount codes failed to create: ${errors.join("; ")}`,
            discounts: results,
          };
        }

        return {
          success: true,
          message: `Created ${results.length} tier-based discount codes successfully`,
          discounts: results,
        };
      } else {
        // ✅ Create single AUTOMATIC discount
        const singleDiscountResult = await createSingleAutomaticDiscount(admin, settingsId, {
          title,
          rewardValue,
          discountType,
          minimumRequirement,
          minimumValue,
          expiryMonths,
          combinations,
        });

        return singleDiscountResult;
      }
    } catch (error) {
      console.error("Error creating birthday reward:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create discount",
      };
    }
  } else if (request.method === "PATCH") {
    const requestBody = await request.json();
    const {
      id,
      title,
      expiryMonths,
      combinations,
      hasDifferentTiers,
      // Single discount fields
      rewardValue,
      discountType,
      minimumRequirement,
      minimumValue,
      // Tier discount fields
      tierDiscounts,
    } = requestBody;

    try {
      // Get existing reward to get settingsId
      const existingReward = await db.birthdayReward.findUnique({
        where: { id: Number(id) },
        select: {
          settingsId: true,
          shopifyDiscountId: true,
          discountCode: true,
        },
      });

      if (!existingReward) {
        return { success: false, error: "Reward not found" };
      }

      // Clean up all existing rewards for this settings
      const allRewardsInSettings = await db.birthdayReward.findMany({
        where: {
          settingsId: existingReward.settingsId,
          birthdayRewardType: "AMOUNT_OFF",
        },
        select: {
          id: true,
          shopifyDiscountId: true,
          title: true,
          discountCode: true,
        },
      });

      // Delete all existing discounts from Shopify
      for (const reward of allRewardsInSettings) {
        if (reward.shopifyDiscountId) {
          try {
            const isDiscountCode = !!reward.discountCode;
            await deleteShopifyDiscountAny(admin, reward.shopifyDiscountId, isDiscountCode);
          } catch (error) {
            console.warn(`Failed to delete Shopify discount for ${reward.title}:`, error);
          }
        }
      }

      // Delete all existing records from database
      await db.birthdayReward.deleteMany({
        where: {
          settingsId: existingReward.settingsId,
          birthdayRewardType: "AMOUNT_OFF",
        },
      });

      // Create new discounts based on type
      if (hasDifferentTiers && tierDiscounts && tierDiscounts.length > 0) {
        // ✅ Create tier-based discount codes
        const { results, errors } = await createTierBasedDiscountCodes(
          admin,
          existingReward.settingsId,
          title,
          expiryMonths,
          tierDiscounts,
          combinations,
        );

        // 🎯 NEW: Re-sync Shopify Function configuration
        try {
          console.log("🔄 Re-syncing updated tier discount configuration to function...");
          const functionSyncResult = await syncTierDiscountConfigToFunction(
            admin,
            existingReward.settingsId,
            db,
          );

          console.log(`🎉 Function re-sync successful: ${functionSyncResult.message}`);
        } catch (functionSyncError) {
          console.error("❌ Failed to re-sync function configuration:", functionSyncError);
        }

        if (errors.length > 0) {
          return {
            success: false,
            error: `Some discount codes failed to update: ${errors.join("; ")}`,
            discounts: results,
          };
        }

        return {
          success: true,
          message: `Updated to tier-based discount codes (${results.length} tiers)`,
          discounts: results,
        };
      } else {
        const singleDiscountResult = await createSingleAutomaticDiscount(
          admin,
          existingReward.settingsId,
          {
            title,
            rewardValue,
            discountType,
            minimumRequirement,
            minimumValue,
            expiryMonths,
            combinations,
          },
        );

        // 🎯 NEW: Remove Shopify Function configuration when switching to single discount
        try {
          console.log("🔄 Removing function configuration for single discount...");
          await removeFunctionConfiguration(admin);
          console.log("✅ Function configuration removed");
        } catch (functionSyncError) {
          console.error("❌ Failed to remove function configuration:", functionSyncError);
        }

        return {
          ...singleDiscountResult,
          success: true,
          message: "Updated to single automatic discount successfully",
        };
      }
    } catch (error) {
      console.error("Error updating birthday reward:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update discount",
      };
    }
  } else if (request.method === "DELETE") {
    const requestBody = await request.json();
    const { action: deleteAction, id, shopifyDiscountId, tierId, settingsId } = requestBody;

    try {
      if (deleteAction === "deleteTierDiscount") {
        // Delete specific tier discount ONLY
        if (!shopifyDiscountId || !tierId || !settingsId) {
          return {
            success: false,
            error: "Missing required parameters for tier discount deletion",
          };
        }

        const targetReward = await db.birthdayReward.findFirst({
          where: {
            settingsId: Number(settingsId),
            birthdayRewardType: "AMOUNT_OFF",
            shopifyDiscountId: shopifyDiscountId,
            title: {
              contains: ` - `,
            },
          },
          select: {
            id: true,
            title: true,
            shopifyDiscountId: true,
            discountCode: true,
          },
        });

        if (!targetReward) {
          return {
            success: false,
            error: "Tier discount not found in database",
          };
        }

        // Delete from Shopify
        try {
          const isDiscountCode = !!targetReward.discountCode;
          await deleteShopifyDiscountAny(admin, shopifyDiscountId, isDiscountCode);
        } catch (error) {
          console.warn(`Failed to delete Shopify discount:`, error);
        }

        // Delete the EXACT record by ID
        const deletedReward = await db.birthdayReward.delete({
          where: {
            id: targetReward.id,
          },
        });

        // 🎯 NEW: Re-sync function configuration after deleting a tier
        try {
          const remainingTierDiscounts = await db.birthdayReward.count({
            where: {
              settingsId: Number(settingsId),
              birthdayRewardType: "AMOUNT_OFF",
              discountCode: {
                not: null,
              },
            },
          });

          if (remainingTierDiscounts > 0) {
            console.log("🔄 Re-syncing function configuration after tier deletion...");
            const functionSyncResult = await syncTierDiscountConfigToFunction(
              admin,
              Number(settingsId),
              db,
            );
            console.log(`✅ Function re-sync successful: ${functionSyncResult.message}`);
          } else {
            console.log("🔄 Removing function configuration (no tier discounts remaining)...");
            await removeFunctionConfiguration(admin);
            console.log("✅ Function configuration removed");
          }
        } catch (functionSyncError) {
          console.error("❌ Failed to re-sync function configuration:", functionSyncError);
        }

        return {
          success: true,
          message: `Deleted tier discount "${targetReward.title}" successfully`,
          deletedId: deletedReward.id,
        };
      } else {
        // Delete discount group
        const existingReward = await db.birthdayReward.findUnique({
          where: { id: Number(id) },
          select: {
            shopifyDiscountId: true,
            settingsId: true,
            title: true,
            discountCode: true,
          },
        });

        if (!existingReward) {
          return { success: false, error: "Reward not found" };
        }

        const baseTitle = existingReward.title.split(" - ")[0];

        const relatedRewards = await db.birthdayReward.findMany({
          where: {
            settingsId: existingReward.settingsId,
            birthdayRewardType: "AMOUNT_OFF",
            OR: [
              { id: Number(id) },
              {
                title: {
                  startsWith: `${baseTitle} - `,
                },
              },
            ],
          },
          select: {
            id: true,
            shopifyDiscountId: true,
            title: true,
            discountCode: true,
          },
        });

        const errors = [];

        // Delete each Shopify discount
        for (const reward of relatedRewards) {
          if (reward.shopifyDiscountId) {
            try {
              const isDiscountCode = !!reward.discountCode;
              await deleteShopifyDiscountAny(admin, reward.shopifyDiscountId, isDiscountCode);
            } catch (error) {
              const errorMessage = `Failed to delete Shopify discount for ${reward.title}: ${error instanceof Error ? error.message : "Unknown error"}`;
              console.warn(errorMessage);
              errors.push(errorMessage);
            }
          }
        }

        // Delete exact records by IDs
        const rewardIds = relatedRewards.map((r) => r.id);
        const deletedRewards = await db.birthdayReward.deleteMany({
          where: {
            id: {
              in: rewardIds,
            },
          },
        });

        // 🎯 NEW: Remove Shopify Function configuration when deleting all discounts
        try {
          console.log("🔄 Removing function configuration (all discounts deleted)...");
          await removeFunctionConfiguration(admin);
          console.log("✅ Function configuration removed");
        } catch (functionSyncError) {
          console.error("❌ Failed to remove function configuration:", functionSyncError);
        }

        return {
          success: true,
          message: `Deleted ${deletedRewards.count} birthday discount(s) successfully`,
          count: deletedRewards.count,
          deletedTitles: relatedRewards.map((r) => r.title),
          warnings: errors.length > 0 ? errors : undefined,
        };
      }
    } catch (error) {
      console.error("Error deleting birthday reward:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete discount",
      };
    }
  } else if (request.method === "PUT") {
    const url = new URL(request.url);
    if (url.searchParams.get("action") === "syncCustomerTiers") {
      try {
        const syncResult = await syncAllCustomersFromMetafields(admin);

        return {
          success: true,
          message: `Synced ${syncResult.results.length} customers successfully`,
          results: syncResult.results,
          errors: syncResult.errors.length > 0 ? syncResult.errors : undefined,
        };
      } catch (error) {
        console.error("Error syncing customer tiers:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to sync customer tiers",
        };
      }
    } else if (url.searchParams.get("action") === "getFunctionStatus") {
      // 🎯 NEW: Get Shopify Function configuration status
      try {
        const status = await getFunctionConfigurationStatus(admin);
        return {
          success: true,
          functionStatus: status,
        };
      } catch (error) {
        console.error("Error getting function status:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get function status",
        };
      }
    }
  } else {
    return {
      success: false,
      error: "Invalid request method",
    };
  }
};
