import { LoyaltyProgramType } from "@prisma/client";
import type { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import findShop from "../../utils/find-shop.server";
import responseBadRequest from "../../utils/response.badRequest";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const shop = await findShop(admin, session);

  const { id: rewardId } = params;

  if (!rewardId) {
    throw responseBadRequest("Reward ID is required");
  }

  // Fetch VIP tiers - tận dụng logic từ loader-vipTier.ts
  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipSettings: true,
    },
  });

  const vipTiers = await db.loyaltyVIPTier.findMany({
    where: {
      loyaltyProgramId: loyaltyProgram?.id,
    },
    orderBy: {
      createdAt: "asc",
    },
    select: {
      id: true,
      name: true,
      spendRequirement: true,
      pointsRequirement: true,
    },
  });

  if (rewardId === "new") {
    return {
      id: null,
      birthdayRewardType: "AMOUNT_OFF",
      title: "",
      expiryMonths: null,
      rewardValue: 0,
      discountType: "FIXED",
      minimumRequirement: "NONE",
      minimumValue: 0,
      productDiscounts: false,
      orderDiscounts: false,
      shippingDiscounts: false,
      shopifyDiscountId: null,
      hasDifferentTiers: false,
      tierDiscounts: [],
      vipTiers: vipTiers, // Add VIP tiers to response
    };
  } else if (isNaN(Number(rewardId))) {
    throw responseBadRequest("Reward ID is not a number");
  } else {
    // Get reward details from local database
    const reward = await db.birthdayReward.findFirst({
      where: {
        id: Number(rewardId),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
        discountType: true,
        minimumRequirement: true,
        minimumValue: true,
        productDiscounts: true,
        orderDiscounts: true,
        shippingDiscounts: true,
        shopifyDiscountId: true,
        settingsId: true,
      },
    });

    // ✅ Handle case where reward doesn't exist
    if (!reward) {
      return {
        id: null,
        birthdayRewardType: "AMOUNT_OFF",
        title: "",
        expiryMonths: null,
        rewardValue: 0,
        discountType: "FIXED",
        minimumRequirement: "NONE",
        minimumValue: 0,
        productDiscounts: false,
        orderDiscounts: false,
        shippingDiscounts: false,
        shopifyDiscountId: null,
        hasDifferentTiers: false,
        tierDiscounts: [],
        vipTiers: vipTiers,
      };
    }

    // Check if there are multiple rewards with similar titles (indicating tier-based discounts)
    const allRewardsInSettings = await db.birthdayReward.findMany({
      where: {
        settingsId: reward.settingsId,
        birthdayRewardType: "AMOUNT_OFF",
      },
      select: {
        id: true,
        title: true,
        rewardValue: true,
        discountType: true,
        minimumRequirement: true,
        minimumValue: true,
        shopifyDiscountId: true,
        // ✅ Include combinations fields
        productDiscounts: true,
        orderDiscounts: true,
        shippingDiscounts: true,
      },
    });

    // Determine if this is tier-based by checking if there are multiple rewards
    // and if titles follow the pattern "Base Title - Tier Name"
    const hasDifferentTiers =
      allRewardsInSettings.length > 1 && allRewardsInSettings.every((r) => r.title.includes(" - "));

    if (hasDifferentTiers) {
      // Extract base title (everything before the first " - ")
      const baseTitle = reward.title.split(" - ")[0];

      // Get tier information for all tier-based rewards
      const tierDiscounts = await Promise.all(
        allRewardsInSettings.map(async (r) => {
          const tierName = r.title.split(" - ")[1];

          // Try to find the VIP tier by name
          const tier = await db.loyaltyVIPTier.findFirst({
            where: {
              name: tierName,
            },
            select: {
              id: true,
              name: true,
            },
          });

          return {
            tierId: tier?.id || 0,
            tierName: tierName || "Unknown Tier",
            rewardValue: r.rewardValue || 0,
            discountType: (r.discountType?.toLowerCase() || "fixed") as "percentage" | "fixed",
            minimumRequirement: (r.minimumRequirement?.toLowerCase() || "none") as
              | "none"
              | "amount"
              | "quantity",
            minimumValue: r.minimumValue,
            hasDiscount: (r.rewardValue || 0) > 0,
            shopifyDiscountId: r.shopifyDiscountId || undefined,
            // ✅ Include combinations for each tier
            combinations: {
              product: r.productDiscounts || false,
              order: r.orderDiscounts || false,
              shipping: r.shippingDiscounts || false,
            },
          };
        }),
      );

      return {
        ...reward,
        title: baseTitle, // Return base title without tier suffix
        hasDifferentTiers: true,
        tierDiscounts: tierDiscounts,
        vipTiers: vipTiers,
      };
    } else {
      // Single discount
      return {
        ...reward,
        hasDifferentTiers: false,
        tierDiscounts: [],
        vipTiers: vipTiers,
      };
    }
  }
};
