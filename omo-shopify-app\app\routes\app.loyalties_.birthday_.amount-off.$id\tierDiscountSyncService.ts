interface TierDiscountConfig {
  tierName: string;
  discountCode: string;
  rewardValue: number;
  discountType: "PERCENTAGE" | "FIXED_AMOUNT";
  minimumRequirement?: "AMOUNT" | "QUANTITY";
  minimumValue?: number;
  title: string;
  enabled: boolean;
}

interface BirthdayDiscountConfiguration {
  enabled: boolean;
  birthday_discount_prefix: string;
  auto_apply: boolean;
  debug_mode?: boolean;
  tierDiscounts: TierDiscountConfig[];
}

/**
 * Sync tier discount configuration to Shopify Function metafield
 */
export async function syncTierDiscountConfigToFunction(admin: any, settingsId: number, db: any) {
  try {
    console.log("🔄 Starting sync of tier discount configuration to function...");

    // Get birthday settings with related tier discounts
    const settings = await db.birthdaySettings.findUnique({
      where: { id: settingsId },
      include: {
        birthdayRewards: {
          where: {
            birthdayRewardType: "AMOUNT_OFF",
            discountCode: {
              not: null, // Only get tier-based discount codes
            },
          },
          orderBy: {
            id: "asc",
          },
        },
      },
    });

    if (!settings) {
      throw new Error(`Birthday settings with ID ${settingsId} not found`);
    }

    // Transform database records to function configuration
    const tierDiscounts: TierDiscountConfig[] = settings.birthdayRewards.map((reward: any) => {
      // Extract tier name from title (format: "Title - TierName")
      const tierName = reward.title.includes(" - ")
        ? reward.title.split(" - ").pop() || ""
        : reward.title;

      return {
        tierName,
        discountCode: reward.discountCode!,
        rewardValue: reward.rewardValue,
        discountType: reward.discountType as "PERCENTAGE" | "FIXED_AMOUNT",
        minimumRequirement: reward.minimumRequirement as "AMOUNT" | "QUANTITY" | undefined,
        minimumValue: reward.minimumValue || undefined,
        title: reward.title,
        enabled: true, // All saved discounts are considered enabled
      };
    });

    // Create function configuration
    const functionConfig: BirthdayDiscountConfiguration = {
      enabled: settings.enabled || false,
      birthday_discount_prefix: "BIRTHDAY_",
      auto_apply: true, // Default to auto apply for better UX
      debug_mode: false, // Can be made configurable
      tierDiscounts,
    };

    console.log(`📝 Generated configuration for ${tierDiscounts.length} tier discounts`);

    // Find or create the Shopify Function discount
    const result = await findOrCreateFunctionDiscount(admin, functionConfig);

    console.log("✅ Successfully synced tier discount configuration to function");

    return {
      success: true,
      functionDiscountId: result.functionDiscountId,
      configuredTiers: tierDiscounts.length,
      tierNames: tierDiscounts.map((t) => t.tierName),
      message: `Synced ${tierDiscounts.length} tier discounts to function`,
    };
  } catch (error) {
    console.error("❌ Failed to sync tier discount configuration:", error);
    throw error;
  }
}

/**
 * Find existing function discount or create a new one
 */
async function findOrCreateFunctionDiscount(admin: any, config: BirthdayDiscountConfiguration) {
  try {
    // Search for existing function discount
    const searchQuery = `
      query {
        discountNodes(first: 50, query: "function_id:tier-birthday-discount") {
          edges {
            node {
              id
              discount {
                __typename
                ... on DiscountAutomaticApp {
                  title
                  discountId
                  status
                }
              }
            }
          }
        }
      }
    `;

    const searchResponse = await admin.graphql(searchQuery);
    const searchJson = await searchResponse.json();

    let functionDiscountId = null;

    if (searchJson.data?.discountNodes?.edges?.length > 0) {
      // Use existing function discount
      functionDiscountId = searchJson.data.discountNodes.edges[0].node.id;
      console.log("🔍 Found existing function discount:", functionDiscountId);
    } else {
      // Create new function discount
      functionDiscountId = await createFunctionDiscount(admin);
      console.log("🆕 Created new function discount:", functionDiscountId);
    }

    // Update function configuration metafield
    await updateFunctionMetafield(admin, functionDiscountId, config);

    return { functionDiscountId };
  } catch (error) {
    console.error("❌ Error in findOrCreateFunctionDiscount:", error);
    throw error;
  }
}

/**
 * Create new Shopify Function discount
 */
async function createFunctionDiscount(admin: any) {
  const createMutation = `
    mutation discountAutomaticAppCreate($automaticAppDiscount: DiscountAutomaticAppInput!) {
      discountAutomaticAppCreate(automaticAppDiscount: $automaticAppDiscount) {
        automaticAppDiscount {
          discountId
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    automaticAppDiscount: {
      title: "Tier Birthday Discount Function",
      functionId: "tier-birthday-discount",
      startsAt: new Date().toISOString(),
      combinesWith: {
        orderDiscounts: true,
        productDiscounts: true,
        shippingDiscounts: false,
      },
    },
  };

  const response = await admin.graphql(createMutation, { variables });
  const json = await response.json();

  if (json.data?.discountAutomaticAppCreate?.userErrors?.length > 0) {
    const errors = json.data.discountAutomaticAppCreate.userErrors
      .map((e: any) => e.message)
      .join(", ");
    throw new Error(`Failed to create function discount: ${errors}`);
  }

  return json.data?.discountAutomaticAppCreate?.automaticAppDiscount?.discountId;
}

/**
 * Update function configuration metafield
 */
async function updateFunctionMetafield(
  admin: any,
  functionDiscountId: string,
  config: BirthdayDiscountConfiguration,
) {
  const updateMutation = `
    mutation metafieldSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          id
          namespace
          key
          value
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: [
      {
        ownerId: functionDiscountId,
        namespace: "$app",
        key: "birthday_discount_config",
        value: JSON.stringify(config),
        type: "json",
      },
    ],
  };

  const response = await admin.graphql(updateMutation, { variables });
  const json = await response.json();

  if (json.data?.metafieldsSet?.userErrors?.length > 0) {
    const errors = json.data.metafieldsSet.userErrors.map((e: any) => e.message).join(", ");
    throw new Error(`Failed to update function metafield: ${errors}`);
  }

  console.log("📝 Updated function configuration metafield");
}

/**
 * Remove function configuration (when all tier discounts are deleted)
 */
export async function removeFunctionConfiguration(admin: any) {
  try {
    console.log("🗑️ Removing function configuration...");

    const searchQuery = `
      query {
        discountNodes(first: 50, query: "function_id:tier-birthday-discount") {
          edges {
            node {
              id
              discount {
                __typename
                ... on DiscountAutomaticApp {
                  discountId
                }
              }
            }
          }
        }
      }
    `;

    const response = await admin.graphql(searchQuery);
    const json = await response.json();

    if (json.data?.discountNodes?.edges?.length > 0) {
      const functionDiscountId = json.data.discountNodes.edges[0].node.id;

      // Delete the function discount
      const deleteMutation = `
        mutation discountAutomaticDelete($id: ID!) {
          discountAutomaticDelete(id: $id) {
            deletedAutomaticDiscountId
            userErrors {
              field
              message
            }
          }
        }
      `;

      await admin.graphql(deleteMutation, {
        variables: { id: functionDiscountId },
      });

      console.log("✅ Function discount configuration removed");
    } else {
      console.log("⚠️ No function discount found to remove");
    }

    return { success: true };
  } catch (error) {
    console.error("❌ Failed to remove function configuration:", error);
    throw error;
  }
}

/**
 * Get current function configuration status
 */
export async function getFunctionConfigurationStatus(admin: any) {
  try {
    const searchQuery = `
      query {
        discountNodes(first: 50, query: "function_id:tier-birthday-discount") {
          edges {
            node {
              id
              discount {
                __typename
                ... on DiscountAutomaticApp {
                  title
                  status
                  discountId
                }
              }
              metafield(namespace: "$app", key: "birthday_discount_config") {
                value
              }
            }
          }
        }
      }
    `;

    const response = await admin.graphql(searchQuery);
    const json = await response.json();

    if (json.data?.discountNodes?.edges?.length > 0) {
      const node = json.data.discountNodes.edges[0].node;

      let config = null;
      try {
        config = node.metafield?.value ? JSON.parse(node.metafield.value) : null;
      } catch (e) {
        console.warn("Failed to parse function configuration");
      }

      return {
        exists: true,
        functionDiscountId: node.id,
        status: node.discount?.status,
        title: node.discount?.title,
        configuredTiers: config?.tierDiscounts?.length || 0,
        enabled: config?.enabled || false,
        autoApply: config?.auto_apply || false,
      };
    }

    return {
      exists: false,
      configuredTiers: 0,
    };
  } catch (error) {
    console.error("❌ Failed to get function configuration status:", error);
    return {
      exists: false,
      configuredTiers: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
