/**
 * Sync all customers' tier tags based on their vip_tier metafields
 */
export async function syncAllCustomersFromMetafields(admin: any) {
  const results: any[] = [];
  const errors: any[] = [];

  try {
    console.log("🔄 Starting sync of all customer tier tags...");

    // Get app namespace for metafields
    const namespace = await getNamespaceMetafield(admin);
    if (!namespace) {
      throw new Error("App namespace not found");
    }

    // Fetch all customers with vip_tier metafields in batches
    let cursor: string | null = null;
    let hasNextPage = true;
    let totalProcessed = 0;

    while (hasNextPage) {
      const customersQuery = `
        query GetCustomersWithTiers($cursor: String) {
          customers(first: 50, after: $cursor) {
            pageInfo {
              hasNextPage
              endCursor
            }
            edges {
              node {
                id
                tags
                metafield(namespace: "${namespace}", key: "vip_tier") {
                  value
                }
              }
            }
          }
        }
      `;

      const customersResponse: any = await admin.graphql(customersQuery, {
        variables: { cursor },
      });

      const customersJson: any = await customersResponse.json();

      if (customersJson.errors) {
        throw new Error(`GraphQL Error: ${JSON.stringify(customersJson.errors)}`);
      }

      const customers = customersJson.data?.customers?.edges || [];

      // Process each customer
      for (const customerEdge of customers) {
        const customer = customerEdge.node;
        const customerId = customer.id;
        const currentTags = customer.tags || [];
        const vipTier = customer.metafield?.value;

        try {
          if (vipTier) {
            // Update customer tier tags
            const updateResult = await updateCustomerTierComplete(admin, customerId, vipTier);

            if (updateResult.success) {
              results.push({
                customerId,
                tier: vipTier,
                action: "updated",
                previousTags: currentTags,
                newTags: updateResult.newTags,
              });
            } else {
              errors.push({
                customerId,
                tier: vipTier,
                error: updateResult.error,
              });
            }
          } else {
            // Customer has no tier metafield - remove tier tags if any
            const tierTags = currentTags.filter(
              (tag: string) => tag.startsWith("tier_") || tag.startsWith("vip_"),
            );

            if (tierTags.length > 0) {
              const cleanResult = await removeCustomerTierTags(admin, customerId, tierTags);

              if (cleanResult.success) {
                results.push({
                  customerId,
                  tier: null,
                  action: "cleaned",
                  removedTags: tierTags,
                });
              } else {
                errors.push({
                  customerId,
                  tier: null,
                  error: cleanResult.error,
                });
              }
            }
          }

          totalProcessed++;
        } catch (error) {
          errors.push({
            customerId,
            tier: vipTier,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Update pagination
      hasNextPage = customersJson.data?.customers?.pageInfo?.hasNextPage || false;
      cursor = customersJson.data?.customers?.pageInfo?.endCursor || null;

      // Log progress
      console.log(`📊 Processed ${totalProcessed} customers...`);
    }

    console.log(
      `✅ Customer tier sync completed: ${results.length} updated, ${errors.length} errors`,
    );

    return {
      success: true,
      results,
      errors,
      totalProcessed,
      message: `Synced ${results.length} customers successfully`,
    };
  } catch (error) {
    console.error("❌ Failed to sync customer tier tags:", error);

    return {
      success: false,
      results,
      errors: [
        ...errors,
        {
          error: error instanceof Error ? error.message : "Unknown sync error",
        },
      ],
      totalProcessed: results.length,
    };
  }
}

/**
 * Update customer tier tags completely (remove old tier tags and add new ones)
 */
export async function updateCustomerTierComplete(admin: any, customerId: string, newTier: string) {
  try {
    // Get current customer tags
    const customerQuery = `
      query GetCustomer($id: ID!) {
        customer(id: $id) {
          id
          tags
        }
      }
    `;

    const customerResponse = await admin.graphql(customerQuery, {
      variables: { id: customerId },
    });

    const customerJson = await customerResponse.json();

    if (customerJson.errors) {
      throw new Error(`GraphQL Error: ${JSON.stringify(customerJson.errors)}`);
    }

    const customer = customerJson.data?.customer;

    if (!customer) {
      throw new Error(`Customer ${customerId} not found`);
    }

    const currentTags = customer.tags || [];

    // Remove existing tier tags
    const nonTierTags = currentTags.filter(
      (tag: string) => !tag.startsWith("tier_") && !tag.startsWith("vip_"),
    );

    // Add new tier tag
    const newTierTag = `tier_${newTier.toLowerCase().replace(/\s+/g, "_")}`;
    const newTags = [...nonTierTags, newTierTag];

    // Update customer tags
    const updateMutation = `
      mutation customerUpdate($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
            tags
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const updateResponse = await admin.graphql(updateMutation, {
      variables: {
        input: {
          id: customerId,
          tags: newTags,
        },
      },
    });

    const updateJson = await updateResponse.json();

    if (updateJson.data?.customerUpdate?.userErrors?.length > 0) {
      const errors = updateJson.data.customerUpdate.userErrors
        .map((e: any) => e.message)
        .join(", ");
      throw new Error(`Customer update failed: ${errors}`);
    }

    console.log(`✅ Updated customer ${customerId} with tier tag: ${newTierTag}`);

    return {
      success: true,
      newTags,
      newTierTag,
      removedTags: currentTags.filter(
        (tag: string) => tag.startsWith("tier_") || tag.startsWith("vip_"),
      ),
    };
  } catch (error) {
    console.error(`❌ Failed to update customer ${customerId} tier:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Remove specific tier tags from customer
 */
async function removeCustomerTierTags(admin: any, customerId: string, tagsToRemove: string[]) {
  try {
    // Get current customer tags
    const customerQuery = `
      query GetCustomer($id: ID!) {
        customer(id: $id) {
          id
          tags
        }
      }
    `;

    const customerResponse = await admin.graphql(customerQuery, {
      variables: { id: customerId },
    });

    const customerJson = await customerResponse.json();

    if (customerJson.errors) {
      throw new Error(`GraphQL Error: ${JSON.stringify(customerJson.errors)}`);
    }

    const customer = customerJson.data?.customer;

    if (!customer) {
      throw new Error(`Customer ${customerId} not found`);
    }

    const currentTags = customer.tags || [];

    // Remove specified tags
    const cleanedTags = currentTags.filter((tag: string) => !tagsToRemove.includes(tag));

    // Update customer tags
    const updateMutation = `
      mutation customerUpdate($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
            tags
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const updateResponse = await admin.graphql(updateMutation, {
      variables: {
        input: {
          id: customerId,
          tags: cleanedTags,
        },
      },
    });

    const updateJson = await updateResponse.json();

    if (updateJson.data?.customerUpdate?.userErrors?.length > 0) {
      const errors = updateJson.data.customerUpdate.userErrors
        .map((e: any) => e.message)
        .join(", ");
      throw new Error(`Customer tag cleanup failed: ${errors}`);
    }

    console.log(`🧹 Cleaned tier tags from customer ${customerId}: ${tagsToRemove.join(", ")}`);

    return {
      success: true,
      removedTags: tagsToRemove,
      remainingTags: cleanedTags,
    };
  } catch (error) {
    console.error(`❌ Failed to clean customer ${customerId} tier tags:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get app namespace for metafields
 */
export async function getNamespaceMetafield(admin: any): Promise<string> {
  try {
    // Try to get namespace from app metafield or use default
    const appQuery = `
      query {
        currentAppInstallation {
          id
          metafield(namespace: "app_config", key: "namespace") {
            value
          }
        }
      }
    `;

    const appResponse = await admin.graphql(appQuery);
    const appJson = await appResponse.json();

    const namespace = appJson.data?.currentAppInstallation?.metafield?.value || "$app";

    console.log(`📝 Using metafield namespace: ${namespace}`);
    return namespace;
  } catch (error) {
    console.warn("⚠️ Failed to get app namespace, using default:", error);
    return "$app"; // Default namespace
  }
}

/**
 * Sync single customer tier based on metafield
 */
export async function syncSingleCustomerTier(admin: any, customerId: string) {
  try {
    const namespace = await getNamespaceMetafield(admin);

    const customerQuery = `
      query GetCustomer($id: ID!) {
        customer(id: $id) {
          id
          tags
          metafield(namespace: "${namespace}", key: "vip_tier") {
            value
          }
        }
      }
    `;

    const customerResponse = await admin.graphql(customerQuery, {
      variables: { id: customerId },
    });

    const customerJson = await customerResponse.json();

    if (customerJson.errors) {
      throw new Error(`GraphQL Error: ${JSON.stringify(customerJson.errors)}`);
    }

    const customer = customerJson.data?.customer;

    if (!customer) {
      throw new Error(`Customer ${customerId} not found`);
    }

    const vipTier = customer.metafield?.value;

    if (vipTier) {
      const result = await updateCustomerTierComplete(admin, customerId, vipTier);
      return {
        success: result.success,
        customerId,
        tier: vipTier,
        action: "updated",
        newTags: result.newTags,
        newTierTag: result.newTierTag,
        removedTags: result.removedTags,
        error: result.error,
      };
    } else {
      return {
        success: true,
        customerId,
        tier: null,
        action: "no_tier_metafield",
        message: "Customer has no tier metafield",
      };
    }
  } catch (error) {
    console.error(`❌ Failed to sync single customer ${customerId}:`, error);

    return {
      success: false,
      customerId,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get customer tier tag statistics
 */
export async function getCustomerTierTagStats(admin: any) {
  try {
    const namespace = await getNamespaceMetafield(admin);

    // Get customers with tier metafields
    const customersQuery = `
      query {
        customers(first: 250, query: "metafield:${namespace}.vip_tier:*") {
          edges {
            node {
              id
              tags
              metafield(namespace: "${namespace}", key: "vip_tier") {
                value
              }
            }
          }
        }
      }
    `;

    const customersResponse = await admin.graphql(customersQuery);
    const customersJson = await customersResponse.json();

    if (customersJson.errors) {
      throw new Error(`GraphQL Error: ${JSON.stringify(customersJson.errors)}`);
    }

    const customers = customersJson.data?.customers?.edges || [];

    const stats = {
      totalCustomersWithTiers: customers.length,
      tierDistribution: {} as Record<string, number>,
      tagSyncStatus: {
        synced: 0,
        needsSync: 0,
        noTags: 0,
      },
    };

    customers.forEach((customerEdge: any) => {
      const customer = customerEdge.node;
      const tier = customer.metafield?.value;
      const tags = customer.tags || [];

      if (tier) {
        // Count tier distribution
        stats.tierDistribution[tier] = (stats.tierDistribution[tier] || 0) + 1;

        // Check tag sync status
        const expectedTag = `tier_${tier.toLowerCase().replace(/\s+/g, "_")}`;
        const hasTierTag = tags.includes(expectedTag);

        if (hasTierTag) {
          stats.tagSyncStatus.synced++;
        } else {
          stats.tagSyncStatus.needsSync++;
        }
      } else {
        stats.tagSyncStatus.noTags++;
      }
    });

    return {
      success: true,
      stats,
      message: `Found ${stats.totalCustomersWithTiers} customers with tier metafields`,
    };
  } catch (error) {
    console.error("❌ Failed to get customer tier tag stats:", error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
