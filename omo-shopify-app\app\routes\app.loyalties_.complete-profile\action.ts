import { DiscountType, RewardType } from "@prisma/client";
import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import {
  AmountOffOrderRewardInterface,
  FreeShippingRewardInterface,
  PointRewardInterface,
  REQUIREMENT_TYPES,
  REWARD_TYPES,
  RewardTypeInterface,
  StoreCreditRewardInterface,
} from "../../components/RewardsSection";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { ActionResponse } from "../app.loyalties_.signup/types";
import {
  BaseRewardDataType,
  CompleteProfileSettings,
  MinRequirementType,
  Reward,
  RewardData,
  ShopType,
  ValidatedRequestData,
} from "./types";

// Helper function to map reward type to enum
function mapRewardType(type: string): RewardType {
  switch (type) {
    case REWARD_TYPES.POINTS:
      return RewardType.POINTS;
    case REWARD_TYPES.STORE_CREDIT:
      return RewardType.STORE_CREDIT;
    case REWARD_TYPES.AMOUNT_OFF:
      return RewardType.AMOUNT_OFF;
    case REWARD_TYPES.FREE_SHIPPING:
      return RewardType.FREE_SHIPPING;
    default:
      return RewardType.POINTS;
  }
}

// Helper function to map discount type to enum
function mapDiscountType(type: string | undefined): DiscountType | null {
  if (!type) return null;
  return type?.toUpperCase() === DiscountType.PERCENTAGE
    ? DiscountType.PERCENTAGE
    : DiscountType.FIXED;
}

// Helper function to map requirement type to enum
function mapRequirementType(type: string | undefined): MinRequirementType | null {
  if (!type) return null;
  switch (type) {
    case REQUIREMENT_TYPES.AMOUNT:
      return MinRequirementType.MIN_PURCHASE_AMOUNT;
    case REQUIREMENT_TYPES.QUANTITY:
      return MinRequirementType.MIN_QUANTITY_ITEMS;
    default:
      return MinRequirementType.NO_MINIMUM;
  }
}

// Add type-specific fields to reward data
function addTypeSpecificFields(
  reward: RewardTypeInterface,
  baseData: BaseRewardDataType,
): RewardData {
  let rewardData = { ...baseData } as RewardData;

  if (reward.type === REWARD_TYPES.AMOUNT_OFF) {
    const amountOffReward = reward as AmountOffOrderRewardInterface;
    rewardData = {
      ...rewardData,
      discountType: mapDiscountType(amountOffReward.discountType),
      minRequirementType: mapRequirementType(amountOffReward.minimumRequirement),
      minRequirementValue: amountOffReward.minimumValue
        ? parseFloat(amountOffReward.minimumValue)
        : null,

      productDiscounts: amountOffReward?.combinations?.productDiscounts ?? false,
      orderDiscounts: amountOffReward?.combinations?.orderDiscounts ?? false,
      shippingDiscounts: amountOffReward?.combinations?.shippingDiscounts ?? false,
    };
  } else if (reward.type === REWARD_TYPES.FREE_SHIPPING) {
    const freeShippingReward = reward as FreeShippingRewardInterface;
    rewardData = {
      ...rewardData,
      minRequirementType: mapRequirementType(freeShippingReward.minimumRequirement),
      minRequirementValue: freeShippingReward.minimumValue
        ? parseFloat(freeShippingReward.minimumValue)
        : null,

      productDiscounts: freeShippingReward?.combinations?.productDiscounts ?? false,
      orderDiscounts: freeShippingReward?.combinations?.orderDiscounts ?? false,
    };
  }

  return rewardData;
}

// Update existing reward
async function updateReward(
  reward: RewardTypeInterface,
  baseRewardData: BaseRewardDataType,
  rewardId: number,
): Promise<void> {
  const updateData = addTypeSpecificFields(reward, baseRewardData);

  await db.completeProfileReward.update({
    where: {
      id: rewardId,
    },
    data: updateData,
  });
}

// Create new reward
async function createReward(
  reward: RewardTypeInterface,
  baseRewardData: BaseRewardDataType,
  completeProfileSettingsId: number,
): Promise<void> {
  const createData: RewardData = {
    ...baseRewardData,
    completeProfileSettingsId: completeProfileSettingsId,
  } as RewardData;
  const finalData = addTypeSpecificFields(reward, createData);

  await db.completeProfileReward.create({
    data: finalData,
  });
}

// Create base reward data
function createBaseRewardData(reward: RewardTypeInterface): BaseRewardDataType {
  // Handle different reward types
  let value: string | null = null;
  if (reward.type === REWARD_TYPES.POINTS) {
    value = (reward as PointRewardInterface).value;
  } else if (reward.type === REWARD_TYPES.STORE_CREDIT) {
    value = (reward as StoreCreditRewardInterface).value;
  } else if (reward.type === REWARD_TYPES.AMOUNT_OFF) {
    value = (reward as AmountOffOrderRewardInterface).value;
  }

  return {
    title: reward.title,
    type: mapRewardType(reward.type),
    value,
  };
}

// Process rewards
async function processRewards(
  rewards: RewardTypeInterface[],
  existingRewardIds: number[],
  updatedCompleteProfile: CompleteProfileSettings,
): Promise<void> {
  for (const reward of rewards) {
    const baseRewardData = createBaseRewardData(reward);
    const isExistingReward =
      !isNaN(Number(reward.id)) && existingRewardIds.includes(Number(reward.id));

    if (isExistingReward) {
      await updateReward(reward, baseRewardData, Number(reward.id));
    } else {
      await createReward(reward, baseRewardData, updatedCompleteProfile.id);
    }
  }
}

// Delete removed rewards
async function deleteRemovedRewards(
  existingRewards: Reward[],
  updatedRewards: RewardTypeInterface[],
): Promise<void> {
  const existingRewardIds = existingRewards.map((reward) => reward.id);
  const updatedRewardIds = updatedRewards
    .filter((reward) => !isNaN(Number(reward.id)))
    .map((reward) => Number(reward.id));

  // Find rewards to delete (existing rewards not in the updated list)
  const rewardsToDelete = existingRewardIds.filter((id) => !updatedRewardIds.includes(id));

  // Delete rewards that are no longer in the list
  if (rewardsToDelete.length > 0) {
    await db.completeProfileReward.deleteMany({
      where: {
        id: {
          in: rewardsToDelete,
        },
      },
    });
  }
}

// Update Complete Profile
async function updateCompleteProfile(
  completeProfileSettingsId: number | undefined,
  pageTitle: string,
  pageStatus: boolean,
): Promise<CompleteProfileSettings> {
  if (!completeProfileSettingsId) {
    throw new Error("completeProfileSettings ID is required");
  }

  const result = await db.completeProfileSettings.update({
    where: {
      id: Number(completeProfileSettingsId),
    },
    data: {
      pageTitle: pageTitle,
      isActive: pageStatus,
    },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });

  return result as CompleteProfileSettings;
}

// Get shop information
async function getShopInfo(shopDomain: string): Promise<Response | { shop: ShopType }> {
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: shopDomain,
    },
  });

  if (!shop) {
    return Response.json(
      { success: false, error: `Shop not found for domain ${shopDomain}` },
      { status: 400 },
    );
  }

  return { shop: shop as ShopType };
}

// Get Complete Profile information
async function getCompleteProfile(
  completeProfileSettingsId: number | undefined,
): Promise<Response | { existingCompleteProfile: CompleteProfileSettings }> {
  const existingCompleteProfile = await db.completeProfileSettings.findUnique({
    where: {
      id: Number(completeProfileSettingsId),
    },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });

  if (!existingCompleteProfile) {
    return Response.json(
      { success: false, error: `Complete Profile not found with ID ${completeProfileSettingsId}` },
      { status: 404 },
    );
  }

  return { existingCompleteProfile: existingCompleteProfile as CompleteProfileSettings };
}

// Validate request data
async function validateRequestData(
  request: Request,
  completeProfileSettingsId: number | undefined,
): Promise<ValidatedRequestData | Response> {
  if (!completeProfileSettingsId || isNaN(Number(completeProfileSettingsId))) {
    return Response.json(
      { success: false, error: "Invalid completeProfileSettings ID" },
      { status: 400 },
    );
  }

  // Parse JSON data from request
  const requestJson = await request.json();
  const { pageTitle, pageStatus, rewards } = requestJson;

  if (!pageTitle) {
    return Response.json({ success: false, error: "pageTitle is required" }, { status: 400 });
  }

  return { pageTitle, pageStatus, rewards };
}

// Get refreshed Complete Profile data
async function getRefreshedCompleteProfile(
  completeProfileSettingsId: number | undefined,
): Promise<CompleteProfileSettings | null> {
  if (!completeProfileSettingsId) {
    return null;
  }

  const result = await db.completeProfileSettings.findUnique({
    where: {
      id: Number(completeProfileSettingsId),
    },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });

  return result as CompleteProfileSettings;
}

/**
 * Action function for the signup program page
 */
export async function action({
  request,
}: ActionFunctionArgs): Promise<TypedResponse<ActionResponse>> {
  try {
    const { session } = await authenticate.admin(request);
    const shop = await db.shop.findFirst({ where: { myshopifyDomain: session.shop } });
    if (!shop) {
      throw new Error("Shop not found");
    }
    const completeProfileSettings = await db.completeProfileSettings.findFirst({
      where: { shopId: shop.id },
      include: {
        rewards: true,
        customProfile: {
          include: {
            gender: true,
          },
        },
      },
    });

    // Validate request data
    const validationResult = await validateRequestData(request, completeProfileSettings?.id);
    if (validationResult instanceof Response) {
      return validationResult;
    }
    const { pageTitle, pageStatus, rewards } = validationResult;

    // Get shop information
    const shopResult = await getShopInfo(session.shop);
    if (shopResult instanceof Response) {
      return shopResult;
    }

    // Get Complete Profile Settings information
    const completeProfileResult = await getCompleteProfile(completeProfileSettings?.id);
    if (completeProfileResult instanceof Response) {
      return completeProfileResult;
    }
    const { existingCompleteProfile } = completeProfileResult;

    // Delete removed rewards
    await deleteRemovedRewards(existingCompleteProfile.rewards, rewards);

    // Update Complete Profile
    const updatedCompleteProfile = await updateCompleteProfile(
      completeProfileSettings?.id,
      pageTitle,
      pageStatus === "true",
    );

    // Process rewards
    await processRewards(
      rewards,
      updatedCompleteProfile.rewards.map((r: Reward) => r.id),
      updatedCompleteProfile,
    );

    // Get refreshed Complete Profile data
    const refreshedTier = await getRefreshedCompleteProfile(completeProfileSettings?.id);
    return Response.json({ success: true, completeProfileSettings: refreshedTier });
  } catch (error) {
    console.error("Error updating Complete Profile:", error);
    return Response.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
