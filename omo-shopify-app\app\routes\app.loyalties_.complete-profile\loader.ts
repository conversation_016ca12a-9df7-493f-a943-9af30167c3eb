import type { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

/**
 * Loader function for the signup program page
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);

  const shop = await db.shop.findFirst({ where: { myshopifyDomain: session.shop } });
  if (!shop) {
    throw new Error("Shop not found");
  }

  let completeProfileSettings = await db.completeProfileSettings.findFirst({
    where: { shopId: shop.id },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });

  if (!completeProfileSettings) {
    const customProfile = await db.customProfile.findFirst();

    completeProfileSettings = await db.completeProfileSettings.create({
      data: { shopId: shop.id, customProfileId: customProfile?.id },
      include: {
        rewards: true,
        customProfile: {
          include: {
            gender: true,
          },
        },
      },
    });
  }

  const initialRewards = await db.completeProfileReward.findMany({
    where: { completeProfileSettingsId: completeProfileSettings?.id },
  });

  return Response.json({ initialRewards, completeProfileSettings });
}
