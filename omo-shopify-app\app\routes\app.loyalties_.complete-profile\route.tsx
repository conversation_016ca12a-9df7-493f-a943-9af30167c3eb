import { useFetcher, useLoaderD<PERSON>, useNavigate, useNavigation } from "@remix-run/react";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  Form,
  FormLayout,
  InlineStack,
  Layout,
  Link,
  List,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { Info } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  DISCOUNT_TYPES,
  PointRewardInterface,
  REQUIREMENT_TYPES,
  REWARD_TYPES,
  RewardsSection,
  RewardTypeInterface,
} from "../../components/RewardsSection";

// Import loader and action from separate files
import { action } from "./action";
import { loader } from "./loader";
import {
  ActionResponse,
  LoaderData,
  LocalDiscountType,
  LocalRequirementType,
  LocalRewardType,
} from "./types";

// Export the loader and action functions
export { action, loader };

export default function ProfileCompletion() {
  const { initialRewards, completeProfileSettings } = useLoaderData<typeof loader>() as LoaderData;
  const navigation = useNavigation();
  const navigate = useNavigate();
  const fetcher = useFetcher();
  const { t } = useTranslation();

  const [pageTitle, setPageTitle] = useState(completeProfileSettings.pageTitle);
  const [shouldUpdate, setShouldUpdate] = useState(false);

  const getRequirementType = useCallback((requirementType: LocalRequirementType | null) => {
    if (requirementType === LocalRequirementType.AMOUNT) {
      return REQUIREMENT_TYPES.AMOUNT;
    } else if (requirementType === LocalRequirementType.QUANTITY) {
      return REQUIREMENT_TYPES.QUANTITY;
    } else {
      return REQUIREMENT_TYPES.NONE;
    }
  }, []);

  const mapDbRewardsToUiRewards = useCallback(
    (dbRewards: LoaderData["initialRewards"]) => {
      return dbRewards.map((reward: LoaderData["initialRewards"][0]) => {
        const baseReward = {
          id: reward.id.toString(),
          title: reward.title,
          value: String(reward.value) ?? "0",
        };

        switch (reward.type) {
          case LocalRewardType.POINTS:
            return {
              ...baseReward,
              type: REWARD_TYPES.POINTS,
            } as PointRewardInterface;
          case LocalRewardType.STORE_CREDIT:
            return {
              ...baseReward,
              type: REWARD_TYPES.STORE_CREDIT,
            };
          case LocalRewardType.AMOUNT_OFF:
            return {
              ...baseReward,
              type: REWARD_TYPES.AMOUNT_OFF,
              discountType:
                reward.discountType === LocalDiscountType.PERCENTAGE
                  ? DISCOUNT_TYPES.PERCENTAGE
                  : DISCOUNT_TYPES.FIXED,
              minimumRequirement: getRequirementType(
                reward.minRequirementType as LocalRequirementType | null,
              ),
              minimumValue: reward.minRequirementValue?.toString(),
              combinations: {
                productDiscounts: reward.productDiscounts ?? false,
                orderDiscounts: reward.orderDiscounts ?? false,
                shippingDiscounts: reward.shippingDiscounts ?? false,
              },
            };
          case LocalRewardType.FREE_SHIPPING:
            return {
              ...baseReward,
              type: REWARD_TYPES.FREE_SHIPPING,
              minimumRequirement: getRequirementType(
                reward.minRequirementType as LocalRequirementType | null,
              ),
              minimumValue: reward.minRequirementValue?.toString(),
              combinations: {
                productDiscounts: reward.productDiscounts ?? false,
                orderDiscounts: reward.orderDiscounts ?? false,
              },
            };
          default:
            // For any unknown reward type, treat it as a points reward
            return {
              ...baseReward,
              type: REWARD_TYPES.POINTS,
            } as PointRewardInterface;
        }
      });
    },
    [getRequirementType],
  );

  const [rewards, setRewards] = useState<RewardTypeInterface[]>(() =>
    mapDbRewardsToUiRewards(initialRewards),
  );

  const handleUpdateReward = useCallback(() => {
    // Create JSON data to submit
    const jsonData = {
      completeProfileSettingsId: completeProfileSettings.id,
      pageTitle: pageTitle,
      pageStatus: true,
      rewards,
    };
    // Submit the JSON data to the action using fetcher
    fetcher.submit(JSON.stringify(jsonData), { method: "post", encType: "application/json" });
  }, [completeProfileSettings.id, pageTitle, rewards, fetcher]);

  const handleSubmit = useCallback(() => {
    setShouldUpdate(true);
  }, []);

  useEffect(() => {
    if (shouldUpdate) {
      handleUpdateReward();
      setShouldUpdate(false);
    }
    if (fetcher.data) {
      const responseData = fetcher.data as ActionResponse;
      if (responseData.success) {
        // Use App Bridge Toast API for success message
        shopify?.toast.show(t("completeProfile.toastMessages.completeProfileUpdated"), {
          isError: false,
          duration: 4500,
        });

        // Navigate back to the VIP tiers list after a short delay
        setTimeout(() => {
          navigate("/app/loyalties/complete-profile");
        }, 1500);
      } else {
        // Use App Bridge Toast API for error message
        shopify?.toast.show(
          responseData.error ?? t("completeProfile.toastMessages.completeProfileUpdateFailed"),
          {
            isError: true,
            duration: 4500,
          },
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, navigate, rewards]);

  return (
    <Page
      backAction={{ content: t("completeProfile.backAction"), url: "/app/loyalties" }}
      title={t("completeProfile.pageTitle")}
    >
      <Form onSubmit={handleSubmit}>
        <Layout>
          <Layout.Section>
            <BlockStack gap="400">
              <Card>
                <BlockStack gap="400">
                  <FormLayout>
                    <Text as="p" variant="headingMd">
                      {t("completeProfile.title.label")}
                    </Text>
                    <TextField
                      label=""
                      value={pageTitle}
                      onChange={setPageTitle}
                      autoComplete="off"
                      helpText={t("completeProfile.title.helpText")}
                    />
                  </FormLayout>
                </BlockStack>
              </Card>

              <RewardsSection
                isSubmitting={navigation.state === "submitting" || fetcher.state === "submitting"}
                onRewardsChange={setRewards}
                initialRewards={rewards}
                titleText={t("signup.rewards.title")}
                addNewText={t("signup.rewards.addNew")}
                descriptionText={t("signup.rewards.description")}
              />
              <Card>
                <BlockStack gap="400">
                  <Text as="p" variant="headingMd">
                    {t("completeProfile.birthday.title")}
                  </Text>
                  <List type="bullet">
                    <List.Item>
                      {completeProfileSettings?.customProfile?.editable
                        ? t("completeProfile.birthday.editableInfo")
                        : t("completeProfile.birthday.nonEditableInfo")}
                    </List.Item>
                  </List>
                  <Text as="p" variant="headingMd">
                    {t("completeProfile.gender.title")}
                  </Text>
                  <List type="bullet">
                    {completeProfileSettings?.customProfile?.gender.map((option, index) => (
                      <List.Item key={index}>{option.name}</List.Item>
                    ))}
                  </List>
                  <Box paddingBlockStart="200" paddingBlockEnd="200">
                    <InlineStack gap="200" align="start">
                      <Info fill="gray" color="white" size="20" />
                      <Text as="p" variant="bodySm" tone="subdued">
                        {t("completeProfile.eligibilityInfo")}
                      </Text>
                    </InlineStack>
                  </Box>
                </BlockStack>
              </Card>
            </BlockStack>
            <Box paddingBlockStart="500" paddingBlockEnd="500">
              <InlineStack align="space-between">
                <Button
                  size="large"
                  variant="primary"
                  onClick={handleUpdateReward}
                  submit
                  loading={navigation.state === "submitting" || fetcher.state === "submitting"}
                  disabled={fetcher.state === "submitting"}
                >
                  {t("completeProfile.buttons.save")}
                </Button>
              </InlineStack>
            </Box>
          </Layout.Section>
          <Layout.Section variant="oneThird">
            <BlockStack gap="400">
              <Card>
                <BlockStack gap="400">
                  <InlineStack align="space-between">
                    <Text as="p" variant="headingMd">
                      {t("completeProfile.status.title")}
                    </Text>
                    <Badge tone="success">{t("completeProfile.status.active")}</Badge>
                  </InlineStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    {t("completeProfile.summary.title")}
                  </Text>
                  <List type="bullet">
                    <List.Item>{t("completeProfile.summary.userInput")}</List.Item>
                  </List>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
        <Box paddingBlockEnd="400">
          <Text as="p" variant="bodyMd" alignment="center">
            {t("completeProfile.learnMore")}{" "}
            <Link url="/help/completing-user-profile">
              {t("completeProfile.completingUserProfile")}
            </Link>
          </Text>
        </Box>
      </Form>
    </Page>
  );
}
