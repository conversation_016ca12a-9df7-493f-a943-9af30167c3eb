import { json } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  ChoiceList,
  FormLayout,
  InlineStack,
  Layout,
  List,
  Page,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import { Gift, Plus, ToggleLeft, ToggleRight } from "lucide-react";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { CustomRewardLoaderData } from "../../types/loyaltyTypes";

export const loader = async () => {
  // Fetch existing reward data
  return json({
    reward: {
      title: "customReward.title",
      status: "active",
      isActive: true,
      type: "discount_code",
      description: "No",
      discountCode: "GENERAL0",
      conditions: "No",
      isCustom: true,
    },
    summary: {
      title: "customReward.title",
      details: "10% off one-time purchase products in Automated Collection",
    },
  });
};

export default function CustomReward() {
  const { reward, summary } = useLoaderData<CustomRewardLoaderData>();
  const submit = useSubmit();
  const { t } = useTranslation();

  const [title, setTitle] = useState(reward.title);
  const [isActive, setIsActive] = useState(reward.isActive);
  const [description, setDescription] = useState(reward.description);
  const [discountCode, setDiscountCode] = useState(reward.discountCode);
  const [conditions, setConditions] = useState(reward.conditions);
  const [rewardType, setRewardType] = useState(reward.type);

  const handleSubmit = useCallback(() => {
    const formData = new FormData();
    formData.append("title", title);
    formData.append("isActive", isActive.toString());
    formData.append("description", description);
    formData.append("discountCode", discountCode);
    formData.append("conditions", conditions);
    formData.append("rewardType", rewardType);

    submit(formData, { method: "post" });
  }, [title, isActive, description, discountCode, conditions, rewardType, submit]);

  const handleDelete = useCallback(() => {
    alert("Delete function");
  }, []);

  const handleToggle = () => {
    const newActiveState = !isActive;
    setIsActive(newActiveState);
  };

  const handleUpload = useCallback(() => {
    alert("Upload function");
  }, []);

  const handleChangeIcon = useCallback(() => {
    alert("Change icon function");
  }, []);

  return (
    <Page
      backAction={{ content: t("customReward.backAction"), url: "/app/loyalties" }}
      title={t("customReward.pageTitle")}
    >
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <FormLayout>
                  <Text as="p" variant="headingMd">
                    {t("customReward.titleField.title")}
                  </Text>
                  <TextField
                    label=""
                    value={title}
                    onChange={setTitle}
                    autoComplete="off"
                    helpText={t("customReward.titleField.helpText")}
                  />
                </FormLayout>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="p" variant="headingMd">
                  {t("customReward.rewardType.title")}
                </Text>

                <ChoiceList
                  title={t("customReward.rewardType.title")}
                  titleHidden
                  choices={[
                    { label: t("customReward.rewardType.discountCode"), value: "discount_code" },
                    { label: t("customReward.rewardType.custom"), value: "custom" },
                  ]}
                  selected={[rewardType]}
                  onChange={(selected) => setRewardType(selected[0])}
                />
              </BlockStack>
              <div style={{ paddingTop: "20px", paddingBottom: "20px" }}>
                <BlockStack gap="400">
                  <Text as="p" variant="headingMd">
                    {t("customReward.description.title")}
                  </Text>
                  <TextField
                    label={t("customReward.description.title")}
                    labelHidden
                    value={description}
                    onChange={setDescription}
                    multiline={2}
                    autoComplete="off"
                    helpText={t("customReward.description.helpText")}
                  />
                </BlockStack>
              </div>
              <BlockStack gap="400">
                <Text as="p" variant="headingMd">
                  {t("customReward.discountCode.title")}
                </Text>
                <InlineStack align="space-between">
                  <TextField
                    label={t("customReward.discountCode.title")}
                    labelHidden
                    value={discountCode}
                    onChange={setDiscountCode}
                    autoComplete="off"
                  />
                  <Select
                    label={t("customReward.discountCode.select")}
                    labelHidden
                    options={[{ label: t("customReward.discountCode.select"), value: "" }]}
                    disabled
                  />
                </InlineStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="p" variant="headingMd">
                  {t("customReward.conditions.title")}
                </Text>
                <TextField
                  label={t("customReward.conditions.title")}
                  labelHidden
                  value={conditions}
                  onChange={setConditions}
                  multiline={2}
                  autoComplete="off"
                  helpText={t("customReward.conditions.helpText")}
                />
              </BlockStack>
            </Card>
          </BlockStack>

          <Box paddingBlockStart="500" paddingBlockEnd="500">
            <InlineStack align="space-between">
              <Button size="large" onClick={handleDelete}>
                {t("customReward.buttons.delete")}
              </Button>
              <Button size="large" variant="primary" onClick={handleSubmit}>
                {t("customReward.buttons.save")}
              </Button>
            </InlineStack>
          </Box>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <InlineStack align="space-between">
                  <Text as="p" variant="headingMd">
                    {t("customReward.status.title")}
                  </Text>
                  <Badge tone="success">{t("customReward.status.active")}</Badge>
                </InlineStack>
                <InlineStack align="space-between">
                  <Text as="span">{t("customReward.status.on")}</Text>
                  {isActive ? (
                    <ToggleLeft color="gray" onClick={handleToggle} />
                  ) : (
                    <ToggleRight color="blue" onClick={handleToggle} />
                  )}
                </InlineStack>
                <Text as="p" variant="bodySm" tone="subdued">
                  {t("customReward.status.pauseInfo")}
                </Text>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="p" variant="headingMd">
                  {t("customReward.summary.title")}
                </Text>
                <List type="bullet">
                  <List.Item>{t(summary.title)}</List.Item>
                  <List.Item>{summary.details}</List.Item>
                </List>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="p" variant="headingMd">
                  {t("customReward.icon.title")}
                </Text>
                <ChoiceList
                  title={t("customReward.icon.title")}
                  titleHidden
                  choices={[
                    {
                      label: (
                        <BlockStack gap="200">
                          <Text as="span" variant="bodyMd">
                            {t("customReward.icon.defaultIcon")}
                          </Text>
                          <Gift size={24} color="#637381" />
                        </BlockStack>
                      ),
                      value: "default",
                    },
                    {
                      label: (
                        <BlockStack gap="200">
                          <Text as="span" variant="bodyMd">
                            {t("customReward.icon.customIcon")}
                          </Text>
                          <Button
                            icon={<Plus size={12} color="#637381" />}
                            onClick={handleUpload}
                            accessibilityLabel={t("customReward.icon.upload")}
                          >
                            {t("customReward.icon.upload")}
                          </Button>
                        </BlockStack>
                      ),
                      value: "custom",
                    },
                  ]}
                  selected={["default"]}
                  onChange={handleChangeIcon}
                />
                <Text as="p" variant="bodySm" tone="subdued">
                  {t("customReward.icon.sizeInfo")}
                </Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
