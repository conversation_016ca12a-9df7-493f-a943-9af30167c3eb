import { useFetcher, useLoaderData, useNavigation } from "@remix-run/react";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  Grid,
  InlineStack,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  PointRewardInterface,
  REQUIREMENT_TYPES,
  REWARD_TYPES,
  RewardsSection,
  RewardTypeInterface,
} from "../../components/RewardsTierSection";
import { useShopifyToast } from "../../hooks/useShopifyToast";
import { action } from "./action";
import { loader } from "./loader";
import {
  LoaderData,
  PlaceAnOrderRewardType as LocalRewardType,
  LoyaltyVIPTier,
  PlaceAnOrderReward,
  RequirementType,
} from "./types";

// No local enums needed as we're using the ones from types.ts

// Export the loader and action functions
export { action, loader };

export default function LoyaltyProgramPlaceAnOrder() {
  const { waysEarnReward, vipTiers, pointsSettings } = useLoaderData<typeof loader>() as LoaderData;
  const navigation = useNavigation();
  const fetcher = useFetcher();
  const { t } = useTranslation();
  const { showSuccessToast, showErrorToast } = useShopifyToast();

  const [title, setTitle] = useState(waysEarnReward?.title ?? "");
  const [isActive, setIsActive] = useState(waysEarnReward?.isActive ?? false);

  // Helper function to get requirement type
  const getRequirementType = useCallback((requirementType: RequirementType | undefined | null) => {
    if (requirementType === RequirementType.AMOUNT) {
      return REQUIREMENT_TYPES.AMOUNT;
    } else if (requirementType === RequirementType.QUANTITY) {
      return REQUIREMENT_TYPES.QUANTITY;
    } else {
      return REQUIREMENT_TYPES.NONE;
    }
  }, []);

  // Helper function to map database rewards to UI rewards
  const mapDbRewardsToUiRewards = useCallback(
    (dbRewards: PlaceAnOrderReward[]) => {
      return dbRewards.map((reward) => {
        const baseReward = {
          id: reward.id.toString(),
          title: reward.title,
          value: reward.value ?? "0",
        };

        switch (reward.rewardType) {
          case LocalRewardType.STORE_CREDIT:
            return {
              ...baseReward,
              type: REWARD_TYPES.STORE_CREDIT,
            };

          case LocalRewardType.POINTS:
          default:
            return {
              ...baseReward,
              type: REWARD_TYPES.POINTS,
            } as PointRewardInterface;
        }
      });
    },
    [getRequirementType],
  );

  const [rewards, setRewards] = useState<RewardTypeInterface[]>(() => {
    if (waysEarnReward?.purchaseRewards && waysEarnReward.purchaseRewards.length > 0) {
      const mappedRewards = mapDbRewardsToUiRewards(waysEarnReward.purchaseRewards);
      return mappedRewards;
    }
    return [];
  });

  const [tiers, setTiers] = useState<LoyaltyVIPTier[]>(() => {
    return [];
  });

  const handleSubmit = useCallback(() => {
    // Convert rewards to a serializable format
    const serializedRewards = rewards.map((reward) => {
      // Create a base object with common properties
      const serializedReward = {
        id: reward.id,
        type: reward.type,
        title: reward.title,
        value: "value" in reward ? reward.value : "0",
      };

      // Create a type-safe way to add optional properties
      const result: Record<string, unknown> = { ...serializedReward };

      // Add optional properties if they exist
      if ("discountType" in reward) result.discountType = reward.discountType;
      if ("minimumRequirement" in reward) result.minimumRequirement = reward.minimumRequirement;
      if ("minimumValue" in reward) result.minimumValue = reward.minimumValue;
      if ("productDiscounts" in reward) result.productDiscounts = reward.productDiscounts;
      if ("orderDiscounts" in reward) result.orderDiscounts = reward.orderDiscounts;
      if ("shippingDiscounts" in reward) result.shippingDiscounts = reward.shippingDiscounts;

      return result;
    });

    // Serialize VIP tiers
    const serializedVipTiers = tiers.map((tier) => ({
      id: tier.id,
      name: tier.name,
      spendRequirement: tier.spendRequirement ?? null,
      pointsRequirement: tier.pointsRequirement ?? null,
      spendAmount: tier.spendAmount ?? null,
      pointEarn: tier.pointEarn ?? null,
      basedOnDiffTier: tier.basedOnDiffTier,
    }));

    // Convert to JSON string and then submit
    const jsonData = JSON.stringify({
      title,
      isActive,
      rewards: serializedRewards,
      vipTiers: serializedVipTiers,
    });

    fetcher.submit(jsonData, { method: "post", encType: "application/json" });
  }, [title, isActive, rewards, fetcher]);

  const handleStatusToggle = useCallback(() => {
    setIsActive(!isActive);
  }, [isActive]);

  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      const response = fetcher.data as { success: boolean; error?: string };
      if (response.success) {
        // Show success toast
        showSuccessToast(t("signup.toastMessages.saved") || "Settings saved successfully");
      } else if (response.error) {
        // Show error toast
        showErrorToast(
          response.error || t("signup.toastMessages.error") || "Failed to save settings",
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.state]);

  return (
    <Page
      title={t("placeOrder.pageTitle")}
      backAction={{ content: t("placeOrder.backAction"), url: "/app/loyalties" }}
    >
      <Grid columns={{ xs: 1, md: 2, lg: 6 }}>
        <Grid.Cell columnSpan={{ xs: 6, md: 3, lg: 4, xl: 4 }}>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <Box paddingBlockStart="400" paddingInline="400">
                  <Text as="h2" variant="headingMd" fontWeight="semibold">
                    {t("placeOrder.titleValue")}
                  </Text>
                </Box>
                <Box paddingInline="400">
                  <TextField
                    label={undefined}
                    value={title}
                    onChange={setTitle}
                    autoComplete="off"
                    labelHidden
                  />
                </Box>
                <Box paddingInline="400" paddingBlockEnd="400">
                  <Text as="p" variant="bodyMd" tone="subdued">
                    {t("placeOrder.descriptionValue")}
                  </Text>
                </Box>
              </BlockStack>
            </Card>

            <RewardsSection
              isSubmitting={navigation.state === "submitting" || fetcher.state === "submitting"}
              onRewardsChange={(updatedRewards) => {
                setRewards(updatedRewards);
              }}
              onVipTiersChange={(updateVipTiers) => {
                setTiers(updateVipTiers);
              }}
              initialRewards={rewards}
              initialVipTiers={vipTiers}
              initialPointsData={pointsSettings}
              titleText={t("placeOrder.rewards")}
              addNewText={t("placeOrder.addNew")}
              descriptionText={t("placeOrder.rewardsDescription")}
            />
          </BlockStack>
        </Grid.Cell>

        <Grid.Cell columnSpan={{ xs: 6, md: 3, lg: 2, xl: 2 }}>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <Box paddingBlockStart="400" paddingInline="400">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text as="h2" variant="headingMd" fontWeight="semibold">
                      {t("signup.status.title")}
                    </Text>
                    <Badge tone={isActive ? "success" : "info"}>
                      {isActive ? t("signup.status.active") : t("signup.status.inactive")}
                    </Badge>
                  </InlineStack>
                </Box>
                <Box paddingInline="400">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text as="span" variant="bodyMd">
                      {t("signup.status.on")}
                    </Text>
                    <button
                      className={`w-10 h-6 rounded-full transition-colors duration-200 ease-in-out flex items-center ${
                        isActive ? "bg-green-500" : "bg-gray-300"
                      }`}
                      onClick={handleStatusToggle}
                      role="switch"
                      aria-checked={isActive}
                      aria-label="Toggle program status"
                      tabIndex={0}
                    >
                      <div
                        className={`w-4 h-4 rounded-full bg-white shadow-md transform transition-transform duration-200 ease-in-out ${
                          isActive ? "translate-x-5" : "translate-x-1"
                        }`}
                      ></div>
                    </button>
                  </InlineStack>
                </Box>
                <Box paddingInline="400" paddingBlockEnd="400">
                  <Text as="p" variant="bodyMd" tone="subdued">
                    {t("signup.status.pauseInfo")}
                  </Text>
                </Box>
              </BlockStack>
            </Card>
          </BlockStack>
        </Grid.Cell>
      </Grid>
      <div className="flex justify-end w-full items-center mb-5 mt-5">
        <Button
          variant="primary"
          onClick={handleSubmit}
          loading={navigation.state === "submitting" || fetcher.state === "submitting"}
        >
          {t("placeOrder.save")}
        </Button>
      </div>
    </Page>
  );
}
