import { DiscountType, RequirementType, RewardType, WaysEarnRewardType } from "@prisma/client";
import type { ActionFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { RewardData } from "./types";

/**
 * Helper function to map frontend reward type to Prisma enum
 */
function mapRewardType(type: string): RewardType {
  switch (type) {
    case "points":
      return RewardType.POINTS;
    case "store-credit":
      return RewardType.STORE_CREDIT;
    case "amount-off":
      return RewardType.AMOUNT_OFF;
    case "free-shipping":
      return RewardType.FREE_SHIPPING;
    default:
      return RewardType.POINTS;
  }
}

/**
 * Helper function to map frontend discount type to Prisma enum
 */
function mapDiscountType(type: string | undefined | null): DiscountType | undefined {
  if (!type) return undefined;
  return type === "percentage" ? DiscountType.PERCENTAGE : DiscountType.FIXED;
}

/**
 * Helper function to map frontend requirement type to Prisma enum
 */
function mapRequirementType(type: string | undefined | null): RequirementType | undefined {
  if (!type) return undefined;
  switch (type) {
    case "none":
      return RequirementType.NONE;
    case "amount":
      return RequirementType.AMOUNT;
    case "quantity":
      return RequirementType.QUANTITY;
    default:
      return undefined;
  }
}

/**
 * Process rewards for a WaysEarnReward
 */
async function processRewards(waysEarnRewardId: number, rewards: RewardData[]) {
  // First, delete all existing rewards for this WaysEarnReward
  try {
    await db.signupReward.deleteMany({
      where: {
        waysEarnRewardId,
      },
    });
  } catch (error) {
    console.error("Error deleting existing rewards:", error);
  }

  // Process each reward
  for (const reward of rewards) {
    try {
      // Create a new reward using Prisma's type-safe API

      const {
        minimumRequirement,
        minimumValue,
        productDiscounts,
        orderDiscounts,
        shippingDiscounts,
        title,
        type,
        value,
        discountType,
      } = reward;
      await db.signupReward.create({
        data: {
          waysEarnRewardId,
          title,
          rewardType: mapRewardType(type),
          value,
          discountType: discountType ? mapDiscountType(discountType) : undefined,
          minimumRequirement: minimumRequirement
            ? mapRequirementType(minimumRequirement)
            : undefined,
          minimumValue: minimumValue ? parseFloat(minimumValue.toString()) : undefined,
          productDiscounts: productDiscounts ?? false,
          orderDiscounts: orderDiscounts ?? false,
          shippingDiscounts: shippingDiscounts ?? false,
        },
      });
    } catch (error) {
      console.error("Error creating reward:", error);
    }
  }
}

/**
 * Action function for the signup program page
 */
export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = await db.shop.findFirst({ where: { myshopifyDomain: session.shop } });
  if (!shop) {
    throw new Error("Shop not found");
  }
  const data = await request.json();
  const { title, isActive, rewards } = data;

  // Find or create the WaysEarnReward for signup
  let waysEarnReward = await db.waysEarnReward.findFirst({
    where: {
      shopId: shop.id,
      typeEarnReward: WaysEarnRewardType.SIGN_UP,
    },
  });

  if (waysEarnReward) {
    // Update the existing WaysEarnReward
    waysEarnReward = await db.waysEarnReward.update({
      where: { id: waysEarnReward.id },
      data: {
        title,
        isActive,
      },
    });
  } else {
    // Create a new WaysEarnReward
    waysEarnReward = await db.waysEarnReward.create({
      data: {
        shopId: shop.id,
        typeEarnReward: WaysEarnRewardType.SIGN_UP,
        title,
        isActive,
      },
    });
  }

  // Process rewards if they are provided
  if (rewards && Array.isArray(rewards)) {
    // Use the extracted processRewards function
    await processRewards(waysEarnReward.id, rewards);
  }

  return { success: true };
}
