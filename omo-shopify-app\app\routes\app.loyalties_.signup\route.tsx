import {
  DISCOUNT_TYPES,
  PointRewardInterface,
  REQUIREMENT_TYPES,
  REWARD_TYPES,
  RewardsSection,
  RewardTypeInterface,
} from "@/components/RewardsSection";
import { useShopifyToast } from "@/hooks/useShopifyToast";
import { useFetcher, useLoaderData, useNavigation } from "@remix-run/react";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  Grid,
  InlineStack,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { action } from "./action";
import { loader } from "./loader";
import {
  DiscountType,
  LoaderData,
  RewardType as LocalRewardType,
  RequirementType,
  SignupReward,
} from "./types";

// Export the loader and action functions
export { action, loader };

export default function LoyaltyProgramSignup() {
  const { waysEarnReward } = useLoaderData<typeof loader>() as LoaderData;
  const navigation = useNavigation();
  const fetcher = useFetcher();
  const { t } = useTranslation();
  const { showSuccessToast, showErrorToast } = useShopifyToast();

  const [title, setTitle] = useState(waysEarnReward?.title ?? "");
  const [isActive, setIsActive] = useState(waysEarnReward?.isActive ?? false);

  // Helper function to get requirement type
  const getRequirementType = useCallback((requirementType: RequirementType | undefined | null) => {
    if (requirementType === RequirementType.AMOUNT) {
      return REQUIREMENT_TYPES.AMOUNT;
    } else if (requirementType === RequirementType.QUANTITY) {
      return REQUIREMENT_TYPES.QUANTITY;
    } else {
      return REQUIREMENT_TYPES.NONE;
    }
  }, []);

  // Helper function to map database rewards to UI rewards
  const mapDbRewardsToUiRewards = useCallback(
    (dbRewards: SignupReward[]) => {
      return dbRewards.map((reward) => {
        const {
          minimumRequirement,
          minimumValue,
          productDiscounts,
          orderDiscounts,
          shippingDiscounts,
          title,
          rewardType,
          value,
          discountType,
          id,
        } = reward;

        const baseReward = {
          id: id.toString(),
          title,
          value,
        };

        switch (rewardType) {
          case LocalRewardType.STORE_CREDIT:
            return {
              ...baseReward,
              type: REWARD_TYPES.STORE_CREDIT,
            };

          case LocalRewardType.AMOUNT_OFF:
            return {
              ...baseReward,
              type: REWARD_TYPES.AMOUNT_OFF,
              discountType:
                discountType === DiscountType.PERCENTAGE
                  ? DISCOUNT_TYPES.PERCENTAGE
                  : DISCOUNT_TYPES.FIXED,
              minimumRequirement: getRequirementType(minimumRequirement),
              minimumValue: minimumValue?.toString(),
              combinations: {
                productDiscounts: productDiscounts ?? false,
                orderDiscounts: orderDiscounts ?? false,
                shippingDiscounts: shippingDiscounts ?? false,
              },
            };

          case LocalRewardType.FREE_SHIPPING:
            return {
              ...baseReward,
              type: REWARD_TYPES.FREE_SHIPPING,
              minimumRequirement: getRequirementType(minimumRequirement),
              minimumValue: minimumValue?.toString(),
              combinations: {
                productDiscounts: productDiscounts ?? false,
                orderDiscounts: orderDiscounts ?? false,
              },
            };

          case LocalRewardType.POINTS:
          default:
            return {
              ...baseReward,
              type: REWARD_TYPES.POINTS,
            } as PointRewardInterface;
        }
      });
    },
    [getRequirementType],
  );

  const [rewards, setRewards] = useState<RewardTypeInterface[]>(() => {
    if (waysEarnReward?.rewards && waysEarnReward.rewards.length > 0) {
      const mappedRewards = mapDbRewardsToUiRewards(waysEarnReward.rewards);
      return mappedRewards;
    }
    return [];
  });

  const handleSubmit = useCallback(() => {
    // Convert rewards to a serializable format
    const serializedRewards = rewards.map((reward) => {
      // Create a base object with common properties
      const serializedReward = {
        id: reward.id,
        type: reward.type,
        title: reward.title,
        value: "value" in reward ? reward.value : "0",
      };

      // Create a type-safe way to add optional properties
      const result: Record<string, unknown> = { ...serializedReward };

      // Add optional properties if they exist
      if ("discountType" in reward) result.discountType = reward.discountType;
      if ("minimumRequirement" in reward) result.minimumRequirement = reward.minimumRequirement;
      if ("minimumValue" in reward) result.minimumValue = reward.minimumValue;
      if ("combinations" in reward && "productDiscounts" in reward.combinations)
        result.productDiscounts = reward.combinations.productDiscounts;
      if ("combinations" in reward && "orderDiscounts" in reward.combinations)
        result.orderDiscounts = reward.combinations.orderDiscounts;
      if ("combinations" in reward && "shippingDiscounts" in reward.combinations)
        result.shippingDiscounts = reward.combinations.shippingDiscounts;

      return result;
    });

    // Convert to JSON string and then submit
    const jsonData = JSON.stringify({
      title,
      isActive,
      rewards: serializedRewards,
    });

    fetcher.submit(jsonData, { method: "post", encType: "application/json" });
  }, [title, isActive, rewards, fetcher]);

  const handleStatusToggle = useCallback(() => {
    setIsActive(!isActive);
  }, [isActive]);

  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      const response = fetcher.data as { success: boolean; error?: string };
      if (response.success) {
        // Show success toast
        showSuccessToast(t("signup.toastMessages.saved") || "Settings saved successfully");
      } else if (response.error) {
        // Show error toast
        showErrorToast(
          response.error || t("signup.toastMessages.error") || "Failed to save settings",
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.state]);

  return (
    <Page
      title={t("signup.pageTitle")}
      backAction={{ content: t("signup.backAction"), url: "/app/loyalties" }}
    >
      <Grid columns={{ xs: 1, md: 2, lg: 6 }}>
        <Grid.Cell columnSpan={{ xs: 6, md: 3, lg: 4, xl: 4 }}>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <Box paddingBlockStart="400" paddingInline="400">
                  <Text as="h2" variant="headingMd" fontWeight="semibold">
                    {t("signup.title.label")}
                  </Text>
                </Box>
                <Box paddingInline="400">
                  <TextField
                    label={undefined}
                    value={title}
                    onChange={setTitle}
                    autoComplete="off"
                    labelHidden
                  />
                </Box>
                <Box paddingInline="400" paddingBlockEnd="400">
                  <Text as="p" variant="bodyMd" tone="subdued">
                    {t("signup.title.helpText")}
                  </Text>
                </Box>
              </BlockStack>
            </Card>

            <RewardsSection
              isSubmitting={navigation.state === "submitting" || fetcher.state === "submitting"}
              onRewardsChange={(updatedRewards) => {
                setRewards(updatedRewards);
              }}
              initialRewards={rewards}
              titleText={t("signup.rewards.title")}
              addNewText={t("signup.rewards.addNew")}
              descriptionText={t("signup.rewards.description")}
            />
          </BlockStack>
        </Grid.Cell>

        <Grid.Cell columnSpan={{ xs: 6, md: 3, lg: 2, xl: 2 }}>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="400">
                <Box paddingBlockStart="400" paddingInline="400">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text as="h2" variant="headingMd" fontWeight="semibold">
                      {t("signup.status.title")}
                    </Text>
                    <Badge tone={isActive ? "success" : "info"}>
                      {isActive ? t("signup.status.active") : t("signup.status.inactive")}
                    </Badge>
                  </InlineStack>
                </Box>
                <Box paddingInline="400">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text as="span" variant="bodyMd">
                      {t("signup.status.on")}
                    </Text>
                    <button
                      className={`w-10 h-6 rounded-full transition-colors duration-200 ease-in-out flex items-center ${
                        isActive ? "bg-green-500" : "bg-gray-300"
                      }`}
                      onClick={handleStatusToggle}
                      role="switch"
                      aria-checked={isActive}
                      aria-label="Toggle program status"
                      tabIndex={0}
                    >
                      <div
                        className={`w-4 h-4 rounded-full bg-white shadow-md transform transition-transform duration-200 ease-in-out ${
                          isActive ? "translate-x-5" : "translate-x-1"
                        }`}
                      ></div>
                    </button>
                  </InlineStack>
                </Box>
                <Box paddingInline="400" paddingBlockEnd="400">
                  <Text as="p" variant="bodyMd" tone="subdued">
                    {t("signup.status.pauseInfo")}
                  </Text>
                </Box>
              </BlockStack>
            </Card>
          </BlockStack>
        </Grid.Cell>
      </Grid>
      <div className="flex justify-end w-full items-center mb-5 mt-5">
        <Button
          variant="primary"
          onClick={handleSubmit}
          loading={navigation.state === "submitting" || fetcher.state === "submitting"}
        >
          {t("signup.buttons.save")}
        </Button>
      </div>
    </Page>
  );
}
