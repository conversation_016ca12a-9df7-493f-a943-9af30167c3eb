// Local enum to match Prisma's SignupRewardType
export enum SignupRewardType {
  POINTS = "POINTS",
  STORE_CREDIT = "STORE_CREDIT",
  AMOUNT_OFF_ORDER = "AMOUNT_OFF_ORDER",
  FREE_SHIPPING = "FREE_SHIPPING",
}

// Local enum to match Prisma's WaysEarnRewardType
export enum WaysEarnRewardType {
  SIGN_UP = "SIGN_UP",
  REFERRAL = "REFERRAL",
  CUSTOM_REWARD = "CUSTOM_REWARD",
  COMPLETE_PROFILE = "COMPLETE_PROFILE",
  PURCHASE = "PURCHASE",
  CELEBRATE_BIRTHDAY = "CELEBRATE_BIRTHDAY",
}

// Local enum to match Prisma's RewardType
export enum RewardType {
  POINTS = "POINTS",
  STORE_CREDIT = "STORE_CREDIT",
  AMOUNT_OFF = "AMOUNT_OFF",
  FREE_SHIPPING = "FREE_SHIPPING",
}

// Local enum to match Prisma's DiscountType
export enum DiscountType {
  PERCENTAGE = "PERCENTAGE",
  FIXED = "FIXED",
}

// Local enum to match Prisma's RequirementType
export enum RequirementType {
  NONE = "NONE",
  AMOUNT = "AMOUNT",
  QUANTITY = "QUANTITY",
}

export interface SignupProgramSettings {
  id: number;
  pageTitle: string;
  isActive: boolean;
}

export interface Reward {
  id: number;
  type: SignupRewardType;
  title: string;
  value: number;
  hasExpiry: boolean;
  expiryMonths: number | null;
  signupSettingsId: number;
}

export interface DefaultWaysEarnRewardType {
  id: number;
  code: WaysEarnRewardType;
}

export interface SignupReward {
  id: number;
  waysEarnRewardId: number;
  title: string;
  rewardType: RewardType;
  value: string;
  discountType?: DiscountType;
  minimumRequirement?: RequirementType;
  minimumValue?: number;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface WaysEarnReward {
  id: number;
  typeEarnReward: WaysEarnRewardType;
  title: string;
  subtitle: string | null;
  isActive: boolean;
  shopId: number | null;
  defaultWaysEarnRewardTypeId: number | null;
  DefaultWaysEarnRewardType: DefaultWaysEarnRewardType | null;
  rewards?: SignupReward[];
}

export interface LoaderData {
  waysEarnReward?: WaysEarnReward;
}

export interface ActionResponse {
  success: boolean;
  errors?: Array<{ field: string; message: string }>;
}

export interface RewardData {
  id?: string;
  type: string;
  title: string;
  value: string;
  discountType?: string | null;
  minimumRequirement?: string | null;
  minimumValue?: string | null;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
}
