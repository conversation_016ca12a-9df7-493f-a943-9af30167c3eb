import { DiscountType, RequirementType, RewardType } from "@prisma/client";
import { ActionFunctionArgs } from "@remix-run/node";
import {
  AmountOffOrderRewardInterface,
  DISCOUNT_TYPES,
  FreeShippingRewardInterface,
  PointRewardInterface,
  REQUIREMENT_TYPES,
  R<PERSON>WARD_TYPES,
  RewardTypeInterface,
  StoreCreditRewardInterface,
} from "../../components/RewardsSection/interface";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { AMOUNT_ENTRY_METHOD, POINT_ENTRY_METHOD } from "../app.loyalties.vip/constants";
import {
  BaseRewardDataType,
  RewardData,
  ShopType,
  ValidatedRequestData,
  VIPRewardType,
  VIPTierType,
} from "./types";

// Helper function to map reward type to enum
function mapRewardType(type: string): RewardType {
  switch (type) {
    case REWARD_TYPES.POINTS:
      return RewardType.POINTS;
    case REWARD_TYPES.STORE_CREDIT:
      return RewardType.STORE_CREDIT;
    case REWARD_TYPES.AMOUNT_OFF:
      return RewardType.AMOUNT_OFF;
    case REWARD_TYPES.FREE_SHIPPING:
      return RewardType.FREE_SHIPPING;
    default:
      return RewardType.POINTS;
  }
}

// Helper function to map discount type to enum
function mapDiscountType(type: string | undefined): DiscountType | null {
  if (!type) return null;
  return type === DISCOUNT_TYPES.PERCENTAGE ? DiscountType.PERCENTAGE : DiscountType.FIXED;
}

// Helper function to map requirement type to enum
function mapRequirementType(type: string | undefined): RequirementType | null {
  if (!type) return null;
  switch (type) {
    case REQUIREMENT_TYPES.AMOUNT:
      return RequirementType.AMOUNT;
    case REQUIREMENT_TYPES.QUANTITY:
      return RequirementType.QUANTITY;
    default:
      return RequirementType.NONE;
  }
}

// Validate request data
async function validateRequestData(
  request: Request,
  tierId: string | undefined,
): Promise<ValidatedRequestData | Response> {
  if (!tierId || isNaN(Number(tierId))) {
    return Response.json({ success: false, error: "Invalid tier ID" }, { status: 400 });
  }

  // Parse JSON data from request
  const requestJson = await request.json();
  const {
    tierName,
    rewards,
    entryMethod,
    spendRequirement: spendRequirementStr,
    pointsRequirement: pointsRequirementStr,
  } = requestJson;

  if (![POINT_ENTRY_METHOD, AMOUNT_ENTRY_METHOD].includes(entryMethod)) {
    return Response.json(
      { success: false, error: "Invalid entry method supplied" },
      { status: 400 },
    );
  }

  if (!tierName) {
    return Response.json({ success: false, error: "Tier name is required" }, { status: 400 });
  }

  if (entryMethod === POINT_ENTRY_METHOD) {
    const pointsRequirement = parseFloat(pointsRequirementStr);
    if (isNaN(pointsRequirement)) {
      return Response.json(
        { success: false, error: "Points requirement must be a valid number" },
        { status: 400 },
      );
    }

    return { tierName, rewards, entryMethod, pointsRequirement };
  } else {
    const spendRequirement = parseFloat(spendRequirementStr);
    if (isNaN(spendRequirement)) {
      return Response.json(
        { success: false, error: "Spend requirement must be a valid number" },
        { status: 400 },
      );
    }

    return { tierName, rewards, entryMethod, spendRequirement };
  }
}

// Get shop information
async function getShopInfo(shopDomain: string): Promise<Response | { shop: ShopType }> {
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: shopDomain,
    },
  });

  if (!shop) {
    return Response.json(
      { success: false, error: `Shop not found for domain ${shopDomain}` },
      { status: 400 },
    );
  }

  return { shop: shop as ShopType };
}

// Get VIP tier information
async function getVipTier(
  tierId: string | undefined,
): Promise<Response | { existingTier: VIPTierType }> {
  if (!tierId) {
    return Response.json({ success: false, error: "Tier ID is required" }, { status: 400 });
  }

  const existingTier = await db.loyaltyVIPTier.findUnique({
    where: {
      id: Number(tierId),
    },
    include: {
      rewards: true,
    },
  });

  if (!existingTier) {
    return Response.json(
      { success: false, error: `VIP tier not found with ID ${tierId}` },
      { status: 404 },
    );
  }

  return { existingTier: existingTier as VIPTierType };
}

// Delete removed rewards
async function deleteRemovedRewards(
  existingRewards: VIPRewardType[],
  updatedRewards: RewardTypeInterface[],
): Promise<void> {
  const existingRewardIds = existingRewards.map((reward) => reward.id);
  const updatedRewardIds = updatedRewards
    .filter((reward) => !isNaN(Number(reward.id)))
    .map((reward) => Number(reward.id));

  // Find rewards to delete (existing rewards not in the updated list)
  const rewardsToDelete = existingRewardIds.filter((id) => !updatedRewardIds.includes(id));

  // Delete rewards that are no longer in the list
  if (rewardsToDelete.length > 0) {
    await db.loyaltyVIPReward.deleteMany({
      where: {
        id: {
          in: rewardsToDelete,
        },
      },
    });
  }
}

// Update VIP tier
async function updateVipTier(
  tierId: string | undefined,
  tierName: string,
  entryMethod: string,
  spendRequirement?: number,
  pointsRequirement?: number,
): Promise<VIPTierType> {
  if (!tierId) {
    throw new Error("Tier ID is required");
  }

  const result = await db.loyaltyVIPTier.update({
    where: {
      id: Number(tierId),
    },
    data: {
      name: tierName,
      pointsRequirement: entryMethod === POINT_ENTRY_METHOD ? (pointsRequirement ?? 0) : 0,
      spendRequirement: entryMethod === AMOUNT_ENTRY_METHOD ? (spendRequirement ?? 0) : 0,
    },
    include: {
      rewards: true,
    },
  });

  return result as VIPTierType;
}

// Create base reward data
function createBaseRewardData(reward: RewardTypeInterface): BaseRewardDataType {
  // Handle different reward types
  let value: string | null = null;

  if (reward.type === REWARD_TYPES.POINTS) {
    value = (reward as PointRewardInterface).value;
  } else if (reward.type === REWARD_TYPES.STORE_CREDIT) {
    value = (reward as StoreCreditRewardInterface).value;
  } else if (reward.type === REWARD_TYPES.AMOUNT_OFF) {
    value = (reward as AmountOffOrderRewardInterface).value;
  }

  return {
    title: reward.title,
    rewardType: mapRewardType(reward.type),
    value,
  };
}

// Add type-specific fields to reward data
function addTypeSpecificFields(
  reward: RewardTypeInterface,
  baseData: BaseRewardDataType,
): RewardData {
  let rewardData = { ...baseData } as RewardData;

  if (reward.type === REWARD_TYPES.AMOUNT_OFF) {
    const amountOffReward = reward as AmountOffOrderRewardInterface;
    rewardData = {
      ...rewardData,
      discountType: mapDiscountType(amountOffReward.discountType),
      minimumRequirement: mapRequirementType(amountOffReward.minimumRequirement),
      minimumValue: amountOffReward.minimumValue ? parseFloat(amountOffReward.minimumValue) : null,
      productDiscounts: amountOffReward.combinations?.productDiscounts ?? false,
      orderDiscounts: amountOffReward.combinations?.orderDiscounts ?? false,
      shippingDiscounts: amountOffReward.combinations?.shippingDiscounts ?? false,
    };
  } else if (reward.type === REWARD_TYPES.FREE_SHIPPING) {
    const freeShippingReward = reward as FreeShippingRewardInterface;
    rewardData = {
      ...rewardData,
      minimumRequirement: mapRequirementType(freeShippingReward.minimumRequirement),
      minimumValue: freeShippingReward.minimumValue
        ? parseFloat(freeShippingReward.minimumValue)
        : null,
      productDiscounts: freeShippingReward.combinations?.productDiscounts ?? false,
      orderDiscounts: freeShippingReward.combinations?.orderDiscounts ?? false,
    };
  }

  return rewardData;
}

// Update existing reward
async function updateReward(
  reward: RewardTypeInterface,
  baseRewardData: BaseRewardDataType,
  rewardId: number,
): Promise<void> {
  const updateData = addTypeSpecificFields(reward, baseRewardData);

  await db.loyaltyVIPReward.update({
    where: {
      id: rewardId,
    },
    data: updateData,
  });
}

// Create new reward
async function createReward(
  reward: RewardTypeInterface,
  baseRewardData: BaseRewardDataType,
  vipTierId: number,
): Promise<void> {
  const createData: RewardData = {
    ...baseRewardData,
    vipTierId: vipTierId,
  } as RewardData;

  const finalData = addTypeSpecificFields(reward, createData);

  await db.loyaltyVIPReward.create({
    data: finalData,
  });
}

// Process rewards
async function processRewards(
  rewards: RewardTypeInterface[],
  existingRewardIds: number[],
  updatedVipTier: VIPTierType,
): Promise<void> {
  for (const reward of rewards) {
    const baseRewardData = createBaseRewardData(reward);
    const isExistingReward =
      !isNaN(Number(reward.id)) && existingRewardIds.includes(Number(reward.id));

    if (isExistingReward) {
      await updateReward(reward, baseRewardData, Number(reward.id));
    } else {
      await createReward(reward, baseRewardData, updatedVipTier.id);
    }
  }
}

// Get refreshed tier data
async function getRefreshedTier(tierId: string | undefined): Promise<VIPTierType | null> {
  if (!tierId) {
    return null;
  }

  const result = await db.loyaltyVIPTier.findUnique({
    where: {
      id: Number(tierId),
    },
    include: {
      rewards: true,
    },
  });

  return result as VIPTierType;
}

/**
 * Action function for updating an existing VIP tier
 */
export async function action({ request, params }: ActionFunctionArgs) {
  try {
    const { session } = await authenticate.admin(request);
    const tierId = params.id;

    // Validate request data
    const validationResult = await validateRequestData(request, tierId);
    if (validationResult instanceof Response) {
      return validationResult;
    }
    const { tierName, rewards, entryMethod, spendRequirement, pointsRequirement } =
      validationResult;

    // Get shop information
    const shopResult = await getShopInfo(session.shop);
    if (shopResult instanceof Response) {
      return shopResult;
    }

    // Get VIP tier information
    const tierResult = await getVipTier(tierId);
    if (tierResult instanceof Response) {
      return tierResult;
    }
    const { existingTier } = tierResult;

    // Delete removed rewards
    await deleteRemovedRewards(existingTier.rewards, rewards);

    // Update VIP tier
    const updatedVipTier = await updateVipTier(
      tierId,
      tierName,
      entryMethod,
      spendRequirement,
      pointsRequirement,
    );

    // Process rewards
    await processRewards(
      rewards,
      existingTier.rewards.map((r: VIPRewardType) => r.id),
      updatedVipTier,
    );

    // Get refreshed tier data
    const refreshedTier = await getRefreshedTier(tierId);

    return Response.json({ success: true, vipTier: refreshedTier });
  } catch (error) {
    console.error("Error updating VIP tier:", error);
    return Response.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
