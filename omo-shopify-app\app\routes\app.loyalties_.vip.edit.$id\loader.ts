import { LoyaltyProgramType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { DEFAULT_ENTRY_METHOD } from "../app.loyalties.vip/constants";

// Loader function to fetch the VIP tier data
export async function loader({ params, request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const tierId = params.id;

  if (!tierId || isNaN(Number(tierId))) {
    throw new Response("Invalid tier ID", { status: 400 });
  }

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    throw new Response(`Shop not found for domain ${session.shop}`, { status: 404 });
  }

  // Find the entry method
  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop?.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipSettings: true,
    },
  });
  const entryMethod = loyaltyProgram?.vipSettings?.entryMethod ?? DEFAULT_ENTRY_METHOD;

  // Find the VIP tier
  const vipTier = await db.loyaltyVIPTier.findUnique({
    where: {
      id: Number(tierId),
    },
    include: {
      rewards: true,
    },
  });

  if (!vipTier) {
    throw new Response(`VIP tier not found with ID ${tierId}`, { status: 404 });
  }

  return Response.json({ vipTier, entryMethod });
}
