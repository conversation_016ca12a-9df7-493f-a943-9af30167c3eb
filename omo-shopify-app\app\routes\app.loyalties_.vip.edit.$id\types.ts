import { RewardTypeInterface } from "@/components/RewardsSection/interface";
import { DiscountType, RequirementType, RewardType } from "@prisma/client";

// Define types for database models
export type ShopType = {
  id: number;
  shopId: string;
  myshopifyDomain: string;
  [key: string]: unknown;
};

export type VIPTierType = {
  id: number;
  name: string;
  spendRequirement?: number;
  pointsRequirement?: number;
  rewards: VIPRewardType[];
  [key: string]: unknown;
};

export type VIPRewardType = {
  id: number;
  title: string;
  rewardType: RewardType;
  value: string | null;
  [key: string]: unknown;
};

// Type for base reward data
export type BaseRewardDataType = {
  title: string;
  rewardType: RewardType;
  value: string | null;
};

// Define interface for reward data
export interface RewardData {
  title: string;
  rewardType: RewardType;
  value: string | null;
  vipTierId: number;
  discountType?: DiscountType | null;
  minimumRequirement?: RequirementType | null;
  minimumValue?: number | null;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
}

// Interface for validated request data
export interface ValidatedRequestData {
  tierName: string;
  rewards: RewardTypeInterface[];
  entryMethod: string;
  spendRequirement?: number;
  pointsRequirement?: number;
}

// Define the loader data interface
export interface LoaderData {
  vipTier: {
    id: number;
    name: string;
    spendRequirement: number | null;
    pointsRequirement: number | null;
    rewards: Array<{
      id: number;
      title: string;
      rewardType: RewardType;
      value: string | null;
      discountType: DiscountType | null;
      minimumRequirement: RequirementType | null;
      minimumValue: number | null;
      productDiscounts: boolean | null;
      orderDiscounts: boolean | null;
      shippingDiscounts: boolean | null;
    }>;
  };
  entryMethod: string;
}
