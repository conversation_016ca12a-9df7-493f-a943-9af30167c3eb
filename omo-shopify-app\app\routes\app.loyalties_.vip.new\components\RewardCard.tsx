import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>tonGroup, Card, InlineStack, Text } from "@shopify/polaris";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { DISCOUNT_TYPES, REWARD_TYPES, RewardTypeInterface } from "../interface";

interface RewardCardProps {
  reward: RewardTypeInterface;
  /**
   * Callback function when the Edit button is clicked
   */
  onEdit?: (reward: RewardTypeInterface) => void;
  /**
   * Callback function when the Delete button is clicked
   */
  onDelete?: (rewardId: string) => void;
}

export default function RewardCard({ reward, onEdit, onDelete }: Readonly<RewardCardProps>) {
  const { t } = useTranslation();

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(reward);
    }
  }, [onEdit, reward]);

  const handleDelete = useCallback(() => {
    if (onDelete) {
      onDelete(reward.id);
    }
  }, [onDelete, reward.id]);

  return (
    <Card key={reward.id}>
      <InlineStack align="space-between" blockAlign="center">
        <BlockStack gap="200">
          <Text variant="headingMd" as="h4">
            {reward.title}
          </Text>
          {reward.type === REWARD_TYPES.POINTS && (
            <Text variant="bodyMd" as="p">
              {reward.value} {t("loyalties.rewards.pointsText")}
            </Text>
          )}
          {reward.type === REWARD_TYPES.STORE_CREDIT && (
            <Text variant="bodyMd" as="p">
              {reward.value} {t("loyalties.rewards.storeCreditsText")}
            </Text>
          )}
          {reward.type === REWARD_TYPES.AMOUNT_OFF && (
            <Text variant="bodyMd" as="p">
              {reward.discountType === DISCOUNT_TYPES.PERCENTAGE
                ? `${reward.value}%`
                : `$${reward.value}`}{" "}
              {t("loyalties.rewards.offOrderText")}
            </Text>
          )}
          {reward.type === REWARD_TYPES.FREE_SHIPPING && (
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.freeShippingText")}
            </Text>
          )}
        </BlockStack>

        <ButtonGroup>
          <Button onClick={handleEdit}>{t("common.edit")}</Button>
          <Button tone="critical" onClick={handleDelete}>
            {t("common.delete")}
          </Button>
        </ButtonGroup>
      </InlineStack>
    </Card>
  );
}
