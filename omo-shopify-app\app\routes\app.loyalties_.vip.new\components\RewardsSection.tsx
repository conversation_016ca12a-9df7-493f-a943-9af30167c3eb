import { useAppBridge } from "@shopify/app-bridge-react";
import { BlockSta<PERSON>, <PERSON>ton, Card, InlineStack, Text } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { MODAL_IDS, PointRewardInterface, REWARD_TYPES, RewardTypeInterface } from "../interface";
import AddRewardModal from "./AddRewardModal";
import DeleteRewardConfirmationModal from "./DeleteRewardConfirmationModal";
import PointRewardModal from "./PointRewardModal";
import RewardCard from "./RewardCard";

interface RewardsSectionProps {
  isSubmitting: boolean;
  onRewardsChange: (rewards: RewardTypeInterface[]) => void;
  initialRewards?: RewardTypeInterface[];
}

export default function RewardsSection({
  isSubmitting,
  onRewardsChange,
  initialRewards = [],
}: Readonly<RewardsSectionProps>) {
  const { t } = useTranslation();
  const shopify = useAppBridge();

  // State for rewards
  const [rewards, setRewards] = useState<RewardTypeInterface[]>(initialRewards);

  // State for edit and delete modals
  const [selectedReward, setSelectedReward] = useState<RewardTypeInterface | null>(null);
  const [rewardToDelete, setRewardToDelete] = useState<{ id: string; title: string } | null>(null);

  // Update parent component when rewards change
  const updateRewards = useCallback(
    (newRewards: RewardTypeInterface[]) => {
      setRewards(newRewards);
      onRewardsChange(newRewards);
    },
    [onRewardsChange],
  );

  // Define callback functions for modal operations
  const handleOpenModal = useCallback(() => {
    shopify?.modal?.show(MODAL_IDS.ADD_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCloseModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAddReward = useCallback(
    (value: string) => {
      handleCloseModal();
      if (value === REWARD_TYPES.POINTS) {
        shopify?.modal?.show(MODAL_IDS.ADD_POINT_REWARD);
      } else {
        // Show toast for reward types that are not yet implemented
        shopify?.toast.show(
          t("loyalties.vip.toastMessages.rewardNotImplemented", { type: value }),
          {
            isError: false,
            duration: 3000,
          },
        );
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleCloseModal, t],
  );

  // Handler functions for managing rewards
  const handleAddPointReward = useCallback(
    (reward: PointRewardInterface) => {
      const newRewards = [...rewards, reward];
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);

      // Show success toast when reward is added
      shopify?.toast.show(t("loyalties.vip.toastMessages.rewardAdded", { title: reward.title }), {
        isError: false,
        duration: 3000,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleClosePointRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.ADD_POINT_REWARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Edit reward handlers
  const handleEditReward = useCallback(
    (reward: RewardTypeInterface) => {
      setSelectedReward(reward);

      if (reward.type === REWARD_TYPES.POINTS) {
        shopify?.modal?.show(MODAL_IDS.EDIT_POINT_REWARD);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const handleUpdatePointReward = useCallback(
    (updatedReward: PointRewardInterface) => {
      const newRewards = rewards.map((reward) =>
        reward.id === updatedReward.id ? updatedReward : reward,
      );
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.EDIT_POINT_REWARD);
      setSelectedReward(null);

      // Show success toast when reward is updated
      shopify?.toast.show(
        t("loyalties.vip.toastMessages.rewardUpdated", { title: updatedReward.title }),
        {
          isError: false,
          duration: 3000,
        },
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards, t, updateRewards],
  );

  const handleCloseEditPointRewardModal = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.EDIT_POINT_REWARD);
    setSelectedReward(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Delete reward handlers
  const handleDeleteReward = useCallback(
    (rewardId: string) => {
      const rewardToDelete = rewards.find((r) => r.id === rewardId);
      if (rewardToDelete) {
        setRewardToDelete({ id: rewardId, title: rewardToDelete.title });
        shopify?.modal?.show(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewards],
  );

  const handleConfirmDelete = useCallback(
    (rewardId?: string) => {
      // Store the title before removing the reward
      const rewardTitle = rewardToDelete?.title ?? t("loyalties.vip.newTier.rewards.title");

      const newRewards = rewards.filter((reward) => reward.id !== rewardId);
      updateRewards(newRewards);
      shopify?.modal?.hide(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
      setRewardToDelete(null);

      // Show success toast when reward is deleted
      shopify?.toast.show(t("loyalties.vip.toastMessages.rewardDeleted", { title: rewardTitle }), {
        isError: false,
        duration: 3000,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rewardToDelete, rewards, t, updateRewards],
  );

  const handleCancelDelete = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.DELETE_REWARD_CONFIRMATION);
    setRewardToDelete(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Card>
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h2">
              {t("loyalties.vip.newTier.rewards.title")}
            </Text>
            <Button onClick={handleOpenModal} disabled={isSubmitting}>
              {t("loyalties.vip.newTier.rewards.addNew")}
            </Button>
          </InlineStack>

          <Text as="p" variant="bodyMd">
            {t("loyalties.vip.newTier.rewards.description")}
          </Text>

          {rewards.length > 0 && (
            <BlockStack gap="300">
              <Text variant="headingMd" as="h3">
                {t("loyalties.vip.newTier.rewards.addedRewards")}
              </Text>
              {rewards.map((reward) => (
                <RewardCard
                  key={reward.id}
                  reward={reward}
                  onEdit={handleEditReward}
                  onDelete={handleDeleteReward}
                />
              ))}
            </BlockStack>
          )}
        </BlockStack>
      </Card>

      {/* Add Reward Modals */}
      <AddRewardModal
        id={MODAL_IDS.ADD_REWARD}
        onSubmit={handleAddReward}
        onClose={handleCloseModal}
        existingRewards={rewards}
      />
      <PointRewardModal
        mode="add"
        onSave={handleAddPointReward}
        onClose={handleClosePointRewardModal}
      />

      <PointRewardModal
        mode="edit"
        reward={selectedReward as PointRewardInterface}
        onSave={handleUpdatePointReward}
        onClose={handleCloseEditPointRewardModal}
      />

      <DeleteRewardConfirmationModal
        rewardId={rewardToDelete?.id}
        rewardTitle={rewardToDelete?.title}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </>
  );
}
