import { LoyaltyProgramType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { DEFAULT_ENTRY_METHOD } from "../app.loyalties.vip/constants";

// Loader function to fetch the VIP tier data
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    throw new Response(`Shop not found for domain ${session.shop}`, { status: 404 });
  }
  // Find or create loyalty program
  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipSettings: true,
    },
  });
  const entryMethod = loyaltyProgram?.vipSettings?.entryMethod ?? DEFAULT_ENTRY_METHOD;

  return Response.json({ entryMethod });
}
