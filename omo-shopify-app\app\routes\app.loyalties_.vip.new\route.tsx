import { DiscountType, RequirementType, RewardType } from "@prisma/client";
import { useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Button,
  Card,
  Form,
  InlineStack,
  Layout,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { RewardsSection, RewardTypeInterface } from "../../components/RewardsSection";
import { AMOUNT_ENTRY_METHOD, POINT_ENTRY_METHOD } from "../app.loyalties.vip/constants";
import { LoaderData } from "./interface";

// Export the action function from the actions file
export { action } from "./actions";
export { loader } from "./loader";

export default function LoyaltyVipNewPage() {
  const shopify = useAppBridge();
  const navigate = useNavigate();
  const fetcher = useFetcher();
  const { t } = useTranslation();
  const { entryMethod } = useLoaderData<LoaderData>();
  const [goal, setGoal] = useState("0");
  const [tierName, setTierName] = useState("");
  const [tierNameError, setTierNameError] = useState("");
  const [goalError, setGoalError] = useState("");
  const [rewards, setRewards] = useState<RewardTypeInterface[]>([]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTierNameError("");
    setGoalError("");

    // Validate tier name
    if (!tierName.trim()) {
      setTierNameError(t("loyalties.vip.newTier.validation.tierNameRequired"));
      isValid = false;
    }

    // Validate goal
    if (!goal.trim()) {
      setGoalError(t("loyalties.vip.newTier.validation.entryGoalRequired"));
      isValid = false;
    } else if (isNaN(Number(goal)) || Number(goal) < 0) {
      setGoalError(t("loyalties.vip.newTier.validation.entryGoalInvalid"));
      isValid = false;
    }

    return isValid;
  }, [tierName, goal, t]);

  const handleSaveVipTier = useCallback(() => {
    // Validate form before submitting
    if (validateForm()) {
      // Create JSON data to submit
      const jsonData = {
        tierName,
        entryMethod,
        rewards,
        ...(entryMethod === POINT_ENTRY_METHOD
          ? { pointsRequirement: goal }
          : { spendRequirement: goal }),
      };

      // Submit the JSON data to the action using fetcher
      fetcher.submit(JSON.stringify(jsonData), { method: "post", encType: "application/json" });
    }
  }, [tierName, goal, rewards, fetcher, validateForm]);

  // Define the response type
  interface VipReward {
    id: number;
    title: string;
    rewardType: RewardType;
    value: string | null;
    discountType?: DiscountType | null;
    minimumRequirement?: RequirementType | null;
    minimumValue?: number | null;
    productDiscounts?: boolean | null;
    orderDiscounts?: boolean | null;
    shippingDiscounts?: boolean | null;
  }

  interface ActionResponse {
    success: boolean;
    error?: string;
    vipTier?: {
      id: number;
      name: string;
      spendRequirement: number | null;
      rewards: VipReward[];
    };
  }

  // Handle the response from the action
  useEffect(() => {
    if (fetcher.data) {
      const responseData = fetcher.data as ActionResponse;
      if (responseData.success) {
        // Use App Bridge Toast API for success message
        shopify?.toast.show(t("loyalties.vip.toastMessages.tierCreated"), {
          isError: false,
          duration: 4500,
        });

        // Navigate back to the VIP tiers list after a short delay
        setTimeout(() => {
          navigate("/app/loyalties/vip");
        }, 1500);
      } else {
        // Use App Bridge Toast API for error message
        shopify?.toast.show(
          responseData.error ?? t("loyalties.vip.toastMessages.tierCreateFailed"),
          {
            isError: true,
            duration: 4500,
          },
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, navigate, t]);

  const handleSubmit = useCallback(() => {
    handleSaveVipTier();
  }, [handleSaveVipTier]);

  return (
    <Page
      backAction={{ content: t("loyalties.vip.newTier.backAction"), url: "/app/loyalties/vip" }}
      title={t("loyalties.vip.newTier.title")}
    >
      <Form onSubmit={handleSubmit}>
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap={"400"}>
                <Text as="h2" variant="headingMd">
                  {t("loyalties.vip.newTier.tierName")}
                </Text>
                <TextField
                  label=""
                  value={tierName}
                  onChange={setTierName}
                  autoComplete="off"
                  error={tierNameError}
                  disabled={fetcher.state === "submitting"}
                />
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  {t("loyalties.vip.newTier.entryGoal")}
                </Text>

                <InlineStack gap="200" align="start" blockAlign="center">
                  <div style={{ width: "150px" }}>
                    <TextField
                      label=""
                      type="number"
                      value={goal}
                      onChange={setGoal}
                      autoComplete="off"
                      min={0}
                      labelHidden
                      prefix={entryMethod === POINT_ENTRY_METHOD ? "" : "$"}
                      error={goalError}
                      disabled={fetcher.state === "submitting"}
                    />
                  </div>
                  <Text as="span" variant="bodyMd">
                    {entryMethod === POINT_ENTRY_METHOD &&
                      t("loyalties.vip.editTier.pointsSinceStartDate")}
                    {entryMethod === AMOUNT_ENTRY_METHOD &&
                      t("loyalties.vip.editTier.spentSinceStartDate")}
                  </Text>
                </InlineStack>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section>
            <RewardsSection
              initialRewards={rewards}
              onRewardsChange={setRewards}
              isSubmitting={fetcher.state === "submitting"}
              titleText={t("loyalties.vip.newTier.rewards.title")}
              addNewText={t("loyalties.vip.newTier.rewards.addNew")}
              descriptionText={t("loyalties.vip.newTier.rewards.description")}
            />
          </Layout.Section>

          <Layout.Section>
            <div className="flex justify-center w-full items-center mb-5">
              <Button
                size="large"
                variant="primary"
                onClick={handleSaveVipTier}
                submit
                loading={fetcher.state === "submitting"}
                disabled={fetcher.state === "submitting"}
              >
                {fetcher.state === "submitting"
                  ? t("loyalties.vip.newTier.saving")
                  : t("loyalties.vip.newTier.save")}
              </Button>
            </div>
          </Layout.Section>
        </Layout>
      </Form>
    </Page>
  );
}
