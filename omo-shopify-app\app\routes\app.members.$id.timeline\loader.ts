import { gidPrefix } from "@/constants";
import db from "@/db.server";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { CustomerTimelineType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";

const mapType = (type: string | null): CustomerTimelineType | undefined => {
  switch (type) {
    case "POINTS":
      return CustomerTimelineType.POINTS;
    case "DISCOUNT":
      return CustomerTimelineType.DISCOUNT;
    case "ORDER":
      return CustomerTimelineType.ORDER;
    case "OTHER":
      return CustomerTimelineType.OTHER;
    default:
      return undefined;
  }
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const shop = await findShop(admin, session);
  const { id } = params;
  const url = new URL(request.url);
  const type = url.searchParams.get("type");
  const take = parseInt(url.searchParams.get("take") ?? "10", 10);
  const skip = parseInt(url.searchParams.get("skip") ?? "0", 0);

  const whereClause = {
    shopId: shop.id,
    customerId: `${gidPrefix.CUSTOMER}${id}`,
    type: mapType(type),
  };

  const [count, customerTimelines] = await db.$transaction([
    db.customerTimeline.count({ where: whereClause }),
    db.customerTimeline.findMany({
      where: whereClause,
      take,
      skip,
      orderBy: {
        createdAt: "desc",
      },
    }),
  ]);
  return { customerTimelines, count };
}
