import { gidPrefix } from "@/constants";
import logger from "@/logger.server";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ShopifyUserError } from "@/utils/ShopifyUserError";
import type { ActionFunction } from "@remix-run/node";
import { updateDetails, updatePoints, updateVipTier } from "./handlers";

const getShopifyCustomerGid = (id: string) =>
  id.startsWith(gidPrefix.CUSTOMER) ? id : `${gidPrefix.CUSTOMER}${id}`;

export const action: ActionFunction = async ({ request, params }) => {
  const { admin, session } = await authenticate.admin(request);
  const { id } = params;

  const shop = await findShop(admin, session);

  if (!id) {
    logger.error("Customer ID is required for customer update.");
    return responseBadRequest(
      [{ field: "id", code: "missing_field", message: "Customer ID is required" }],
      "Customer ID is required",
    );
  }

  const payload = (await request.json().catch((error) => {
    logger.error(`Error parsing customer update payload: ${error}`);
    return responseBadRequest(
      [{ field: "_action", code: "invalid", message: "Invalid JSON payload" }],
      "Invalid JSON payload",
    );
  })) as { _action: string; points?: number; birthday?: string; gender?: string; vipTier?: string };

  const actionType = payload._action as string;
  const shopifyCustomerId = getShopifyCustomerGid(id);

  try {
    switch (actionType) {
      case "updateDetails": {
        return updateDetails(admin, shopifyCustomerId, payload);
      }

      case "updatePoints": {
        return updatePoints(admin, shopifyCustomerId, payload, shop);
      }

      case "updateVipTier": {
        return updateVipTier(admin, shopifyCustomerId, payload, shop);
      }

      default:
        return responseBadRequest(
          [{ field: "_action", code: "invalid", message: "Invalid action" }],
          "Invalid action",
        );
    }
  } catch (error) {
    if (error instanceof ShopifyUserError) {
      logger.error(`Customer update failed: ${error.errors[0].message}`, { errors: error.errors });
      return responseBadRequest(error.errors, "Customer update failed");
    }

    logger.error(`Unexpected error updating customer: ${error}`, { error });
    return responseBadRequest(
      [{ field: "_action", code: "error", message: "An unexpected error occurred" }],
      "Customer update failed",
    );
  }
};
