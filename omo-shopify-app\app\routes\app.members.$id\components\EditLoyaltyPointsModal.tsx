import { useShopifyToast } from "@/hooks/useShopifyToast";
import { useFetcher } from "@remix-run/react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { Box, Form, FormLayout, TextField } from "@shopify/polaris";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

interface EditLoyaltyPointsModalProps {
  open: boolean;
  onClose: () => void;
  currentPoints: string;
}

export function EditLoyaltyPointsModal({
  open,
  onClose,
  currentPoints,
}: Readonly<EditLoyaltyPointsModalProps>) {
  const [points, setPoints] = useState(currentPoints);
  const [pointsError, setPointsError] = useState("");
  const fetcher = useFetcher();
  const { showSuccessToast } = useShopifyToast();
  const { t } = useTranslation();

  useEffect(() => {
    setPoints(currentPoints);
  }, [currentPoints]);

  const handlePointsChange = (value: string) => {
    // Only allow numeric input
    if (value === "" || /^\d+$/.test(value)) {
      setPoints(value);
    }
  };

  const validateForm = useCallback(() => {
    let isValid = true;
    setPointsError("");

    // Validate points is not empty
    if (!points.trim()) {
      setPointsError(t("members.edit.validation.pointsRequired"));
      isValid = false;
    }
    // Validate points is a valid number
    else if (isNaN(Number(points))) {
      setPointsError(t("members.edit.validation.pointsInvalid"));
      isValid = false;
    }
    // Validate points is not negative
    else if (Number(points) < 0) {
      setPointsError(t("members.edit.validation.pointsNegative"));
      isValid = false;
    }

    return isValid;
  }, [points, t]);

  const handleSave = useCallback(() => {
    if (validateForm()) {
      fetcher.submit(
        {
          points: points,
          _action: "updatePoints",
        },
        {
          method: "POST",
          encType: "application/json",
        },
      );
    }
  }, [fetcher, points, validateForm]);

  const loading = useMemo(() => fetcher.state === "submitting", [fetcher.state]);

  const handleModalClose = useCallback(() => {
    if (!loading) {
      fetcher.data = undefined;
      onClose();
    }
  }, [loading, onClose, fetcher]);

  useEffect(() => {
    if (
      fetcher.data &&
      typeof fetcher.data === "object" &&
      "success" in fetcher.data &&
      fetcher.data.success
    ) {
      showSuccessToast(t("members.edit.loyaltyPointsUpdated"));
      handleModalClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, handleModalClose, onClose, showSuccessToast]);

  return (
    <Modal open={open} onHide={handleModalClose}>
      <Box padding={"400"}>
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSave();
          }}
        >
          <FormLayout>
            <TextField
              label={t("members.loyaltyPoints")}
              type="number"
              value={points}
              onChange={handlePointsChange}
              autoComplete="off"
              min={0}
              step={1}
              error={pointsError}
            />
          </FormLayout>
        </Form>
      </Box>

      <TitleBar title={t("members.editLoyaltyPoints")}>
        <button onClick={handleModalClose} disabled={loading}>
          {t("common.cancel")}
        </button>
        <button
          onClick={handleSave}
          disabled={loading || points === currentPoints}
          variant="primary"
        >
          {loading ? t("common.saving") : t("common.save")}
        </button>
      </TitleBar>
    </Modal>
  );
}
