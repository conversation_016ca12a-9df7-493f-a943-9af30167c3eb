import { DatePicking } from "@/components";
import { useShopifyToast } from "@/hooks/useShopifyToast";
import { useFetcher } from "@remix-run/react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Box, Form, FormLayout, Select, Text } from "@shopify/polaris";
import dayjs from "dayjs";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

interface EditMemberDetailsModalProps {
  open: boolean;
  onClose: () => void;
  currentGender: string | undefined;
  currentDob: string | undefined;
  genderOptions: { name: string }[] | null;
}

export function EditMemberDetailsModal({
  open,
  onClose,
  currentGender,
  currentDob,
  genderOptions,
}: Readonly<EditMemberDetailsModalProps>) {
  const [gender, setGender] = useState(currentGender || "");
  const [dob, setDob] = useState(currentDob ? new Date(currentDob) : undefined);
  const [genderError, setGenderError] = useState("");
  const [dobError, setDobError] = useState("");
  const fetcher = useFetcher();
  const { showSuccessToast } = useShopifyToast();
  const { t } = useTranslation();

  useEffect(() => {
    setGender(currentGender || "");
    setDob(currentDob ? new Date(currentDob) : undefined);
  }, [currentGender, currentDob]);

  const handleGenderChange = (value: string) => {
    setGender(value);
  };

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setGenderError("");
    setDobError("");

    // Validate gender
    if (!gender.trim()) {
      setGenderError(t("members.edit.validation.genderRequired"));
      isValid = false;
    }

    // Validate date of birth
    if (!dob) {
      setDobError(t("members.edit.validation.dobRequired"));
      isValid = false;
    } else if (dayjs(dob).isAfter(dayjs())) {
      setDobError(t("members.edit.validation.dobInvalid"));
      isValid = false;
    }

    return isValid;
  }, [gender, dob, t]);

  const handleSave = useCallback(() => {
    if (validateForm()) {
      fetcher.submit(
        {
          birthday: dayjs(dob).format("YYYY-MM-DD") || "",
          gender: gender,
          _action: "updateDetails",
        },
        {
          method: "POST",
          encType: "application/json",
        },
      );
    }
  }, [validateForm, fetcher, dob, gender]);

  const loading = useMemo(() => fetcher.state === "submitting", [fetcher.state]);

  useEffect(() => {
    if (fetcher.data) {
      showSuccessToast(t("members.edit.successMessage"));
      onClose();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, onClose]);

  return (
    <Modal open={open} onHide={onClose}>
      <BlockStack gap="300">
        <Box padding="400">
          <Form
            onSubmit={(e) => {
              e.preventDefault();
            }}
          >
            <FormLayout>
              <Select
                label={t("members.edit.gender")}
                options={
                  genderOptions?.map((option) => ({
                    label: option.name,
                    value: option.name,
                  })) || []
                }
                onChange={handleGenderChange}
                value={gender}
                error={genderError}
                placeholder={t("members.edit.selectGender")}
              />
              <Box paddingBlockEnd="200">
                <DatePicking value={dob} setValue={setDob} label={t("members.edit.dateOfBirth")} />
                {dobError && (
                  <Text as="p" variant="bodyMd" tone="critical">
                    {dobError}
                  </Text>
                )}
              </Box>
            </FormLayout>
          </Form>
        </Box>
      </BlockStack>
      <TitleBar title="Edit birthday and gender">
        <button variant="primary" onClick={handleSave} disabled={loading}>
          {loading ? t("common.saving") : t("common.save")}
        </button>
        <button onClick={onClose} disabled={loading}>
          {t("common.cancel")}
        </button>
      </TitleBar>
    </Modal>
  );
}
