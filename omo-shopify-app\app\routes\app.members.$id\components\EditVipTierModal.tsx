import { useShopifyToast } from "@/hooks/useShopifyToast";
import { useFetcher } from "@remix-run/react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { Box, Form, FormLayout, Select, Text } from "@shopify/polaris";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

interface EditVipTierModalProps {
  open: boolean;
  onClose: () => void;
  currentTier: string;
  vipTierOptions: { name: string }[];
}

export function EditVipTierModal({
  open,
  onClose,
  currentTier,
  vipTierOptions,
}: EditVipTierModalProps) {
  const [selectedTier, setSelectedTier] = useState(currentTier);
  const fetcher = useFetcher();
  const { showSuccessToast } = useShopifyToast();
  const { t } = useTranslation();

  // Update local state when currentTier changes
  useEffect(() => {
    setSelectedTier(currentTier || "");
  }, [currentTier]);

  const handleTierChange = (value: string) => {
    setSelectedTier(value);
  };

  const handleSave = () => {
    fetcher.submit(
      {
        vipTier: selectedTier,
        _action: "updateVipTier",
      },
      {
        method: "POST",
        encType: "application/json",
      },
    );
  };

  const loading = useMemo(() => fetcher.state === "submitting", [fetcher.state]);

  // Handle successful update
  useEffect(() => {
    if (
      fetcher.data &&
      typeof fetcher.data === "object" &&
      "success" in fetcher.data &&
      fetcher.data.success
    ) {
      showSuccessToast("VIP tier updated successfully");
      onClose();
    }
  }, [fetcher.data, onClose, showSuccessToast]);

  const handleModalClose = useCallback(() => {
    if (!loading) {
      onClose();
    }
  }, [loading, onClose]);

  return (
    <Modal open={open} onHide={handleModalClose}>
      <Box padding="400">
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSave();
          }}
        >
          <FormLayout>
            <Select
              label={t("members.vipTier")}
              options={vipTierOptions.map((tier) => ({ label: tier.name, value: tier.name }))}
              onChange={handleTierChange}
              value={selectedTier}
              placeholder="Select a tier"
            />
            {selectedTier === "" && (
              <Text as="p" variant="bodySm" tone="subdued">
                {t("members.noVipTierDescription")}
              </Text>
            )}
          </FormLayout>
        </Form>
      </Box>

      <TitleBar title={t("members.editVipTier")}>
        <button onClick={handleModalClose} disabled={loading}>
          {t("common.cancel")}
        </button>
        <button
          onClick={handleSave}
          disabled={loading || selectedTier === currentTier}
          variant="primary"
        >
          {loading ? t("common.saving") : t("common.save")}
        </button>
      </TitleBar>
    </Modal>
  );
}
