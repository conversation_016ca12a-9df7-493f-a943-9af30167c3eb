import { CustomerMetafield } from "@/constants";
import { numberFormat } from "@/utils/helper";
import { BlockStack, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { useMemo, useState } from "react";
import { CustomerInterface } from "../types";
import { EditLoyaltyPointsModal } from "./EditLoyaltyPointsModal";

interface LoyaltyPointsCardProps {
  customer: CustomerInterface | null;
}

export function LoyaltyPointsCard({ customer }: LoyaltyPointsCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const points = useMemo(
    () =>
      customer?.metafields?.edges?.find((edge) => edge.node.key === CustomerMetafield.POINTS)?.node
        ?.value || "0",
    [customer],
  );

  return (
    <>
      <Card padding="400">
        <InlineStack align="space-between">
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">
              Loyalty Points
            </Text>
            <Text as="p" variant="bodyMd" tone="subdued">
              {numberFormat(points)} points available
            </Text>
          </BlockStack>
          <BlockStack align="center">
            <Button onClick={() => setIsModalOpen(true)}>Edit balance</Button>
          </BlockStack>
        </InlineStack>
      </Card>

      <EditLoyaltyPointsModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        currentPoints={points}
      />
    </>
  );
}
