import { CustomerMetafield } from "@/constants";
import {
  Avatar,
  BlockStack,
  Button,
  Card,
  Divider,
  InlineGrid,
  InlineStack,
  Text,
} from "@shopify/polaris";
import { useCallback, useMemo, useState } from "react";
import type { CustomerInterface } from "../types";
import { EditMemberDetailsModal } from "./EditMemberDetailsModal";

interface MemberProfileCardProps {
  customer: CustomerInterface | null;
  genderOptions: { name: string }[] | null;
}

export function MemberProfileCard({ customer, genderOptions }: MemberProfileCardProps) {
  const [openEditModal, setOpenEditModal] = useState(false);

  const gender = useMemo(() => {
    return customer?.metafields?.edges.find((edge) => edge.node.key === CustomerMetafield.GENDER)
      ?.node.value;
  }, [customer]);

  const dob = useMemo(() => {
    return customer?.metafields?.edges.find(
      (edge) => edge.node.key === CustomerMetafield.BIRTH_DATE,
    )?.node.value;
  }, [customer]);

  const onEdit = useCallback(() => {
    setOpenEditModal(true);
  }, []);

  const handleCloseEditModal = useCallback(() => {
    setOpenEditModal(false);
  }, []);

  return (
    <>
      <Card padding={{ xs: "400", md: "500" }}>
        <BlockStack gap="300">
          <InlineStack align="start" blockAlign="center" gap="400">
            <Avatar
              size="lg"
              initials={customer?.displayName?.charAt(0) || ""}
              name={customer?.displayName || "Customer"}
              source={customer?.image?.url}
            />
            <Text variant="headingMd" as="h2">
              {customer?.displayName || "No Name"}
            </Text>
          </InlineStack>

          <Text variant="bodyMd" as="p">
            {customer?.phone || "No phone provided"}
          </Text>

          <Text variant="bodyMd" as="p">
            {customer?.email || "No email provided"}
          </Text>

          <Divider />

          <Text variant="bodyMd" as="p">
            {customer?.addresses?.[0]?.address1 || "No address provided"}
          </Text>
          <Divider />

          <InlineGrid columns="1fr auto" gap="200" alignItems="center">
            <BlockStack gap="0">
              <Text variant="bodyMd" as="p">
                {gender || "Gender not specified"}
              </Text>
              <Text variant="bodyMd" as="p">
                {dob || "Date of birth not specified"}
              </Text>
            </BlockStack>
            <Button onClick={onEdit}>Edit</Button>
          </InlineGrid>
        </BlockStack>
      </Card>

      {openEditModal && (
        <EditMemberDetailsModal
          open={openEditModal}
          onClose={handleCloseEditModal}
          currentGender={gender}
          currentDob={dob}
          genderOptions={genderOptions}
        />
      )}
    </>
  );
}
