import { Box, BlockStack, Text, Card } from "@shopify/polaris";

interface StatCardProps {
  label: string;
  value: string | number;
}

export function StatCard({ label, value }: StatCardProps) {
  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="100">
          <Text variant="bodyMd" as="p" tone="subdued">
            {label}
          </Text>
          <Text variant="headingLg" as="h3">
            {value}
          </Text>
        </BlockStack>
      </Box>
    </Card>
  );
}
