import { moneyFormat, numberFormat } from "@/utils/helper";
import { InlineGrid } from "@shopify/polaris";
import { useMemo } from "react";
import { CustomerInterface } from "../types";
import { StatCard } from "./StatCard";

interface StatsGridProps {
  customer: CustomerInterface | null;
}

export function StatsGrid({ customer }: StatsGridProps) {
  const stats = useMemo(() => {
    return [
      { label: "Orders Count", value: numberFormat(customer?.numberOfOrders) },
      {
        label: "Total spent",
        value: moneyFormat(customer?.amountSpent?.amount),
      },
      { label: "Lifetime orders from Shopify", value: numberFormat(customer?.numberOfOrders) },
      {
        label: "Lifetime spent from Shopify",
        value: moneyFormat(customer?.amountSpent?.amount),
      },
    ];
  }, [customer]);

  return (
    <InlineGrid columns={{ xs: "1fr", sm: "1fr 1fr" }} gap="400">
      {stats.map((stat, index) => (
        <StatCard key={index} label={stat.label} value={stat.value} />
      ))}
    </InlineGrid>
  );
}
