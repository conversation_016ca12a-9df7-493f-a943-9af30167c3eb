import { BlockStack, SkeletonBodyText, SkeletonDisplayText } from "@shopify/polaris";
import DOMPurify from "isomorphic-dompurify";
import { useMemo } from "react";

export interface TimelineEvent {
  id: string;
  date: string;
  time: string;
  description: string;
}

interface TimelineGroup {
  date: string;
  events: TimelineEvent[];
}

/**
 * Timeline component that displays a vertical timeline of events grouped by date
 */
interface TimelineProps {
  events: TimelineEvent[];
  className?: string;
  loading?: boolean;
}

export function Timeline({ events, className = "", loading }: TimelineProps) {
  // Group events by date
  const groupedEvents = useMemo(
    () =>
      events.reduce(
        (acc, event) => {
          if (!acc[event.date]) {
            acc[event.date] = [];
          }
          acc[event.date].push(event);
          return acc;
        },
        {} as Record<string, TimelineEvent[]>,
      ),
    [events],
  );

  const timelineGroups: TimelineGroup[] = useMemo(
    () =>
      Object.entries(groupedEvents).map(([date, events]) => ({
        date,
        events,
      })),
    [groupedEvents],
  );

  return (
    <div className={`max-w-2xl ${className}`}>
      {loading && (
        <div className="mt-3">
          <BlockStack gap={"500"}>
            <BlockStack gap={"200"}>
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText />
            </BlockStack>

            <BlockStack gap={"200"}>
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText />
            </BlockStack>
          </BlockStack>
        </div>
      )}

      {!loading && (
        <div className="relative mt-3">
          {/* Timeline line */}
          <div className="absolute left-2 top-0 bottom-0 w-px bg-gray-200"></div>

          {/* Timeline events grouped by date */}
          <div className="space-y-8">
            {timelineGroups.map((group) => (
              <div key={group.date} className="relative">
                {/* Date header with dot */}
                <div className="relative flex items-center mb-4">
                  <div className="relative z-10 w-4 h-4 bg-white border-2 border-gray-400 rounded-full flex-shrink-0">
                    <div className="absolute inset-[1.75px] bg-gray-700 rounded-full"></div>
                  </div>
                  <time className="ml-2 text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-full">
                    {group.date}
                  </time>
                </div>

                {/* Events for this date */}
                <div className="ml-6 space-y-4">
                  {group.events.map((event) => (
                    <div key={event.id} className="relative">
                      <div className="pl-2">
                        <div className="font-semibold text-black mb-1">{event.time}</div>
                        <div
                          className="text-gray-600 leading-relaxed"
                          dangerouslySetInnerHTML={{
                            __html: DOMPurify.sanitize(event.description),
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
