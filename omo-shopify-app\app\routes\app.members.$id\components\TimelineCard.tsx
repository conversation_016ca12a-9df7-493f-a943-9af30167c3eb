import { useFetcher } from "@remix-run/react";
import { BlockStack, Box, Button, Card, Tabs, Text } from "@shopify/polaris";
import dayjs from "dayjs";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Timeline } from "./Timeline";

const tabs = [
  { id: "all", content: "All" },
  { id: "POINTS", content: "Points" },
  { id: "DISCOUNT", content: "Discount" },
  { id: "ORDER", content: "Order" },
  { id: "OTHER", content: "Other" },
];

export function TimelineCard() {
  const fetcher = useFetcher<{
    customerTimelines: { id: string; date: string; createdAt: string; message: string }[];
    count: number;
  }>();
  const [selected, setSelected] = useState(0);

  const fetcherLoad = useCallback(
    ({ skip, id }: { skip: number; id?: string }) => {
      fetcher.load(`timeline?type=${id}&skip=${skip}`);
    },
    [fetcher],
  );

  const handleSelect = useCallback(
    (id: number) => {
      setSelected(id);
      fetcherLoad({ skip: 0, id: tabs[id].id });
    },
    [fetcherLoad],
  );

  useEffect(() => {
    fetcherLoad({ skip: 0 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const events = useMemo(
    () =>
      fetcher.data?.customerTimelines.map((timeline) => ({
        id: timeline.id,
        date: dayjs(timeline.date).format("YYYY-MM-DD"),
        time: dayjs(timeline.createdAt).format("HH:mm"),
        description: timeline.message,
      })) ?? [],
    [fetcher.data],
  );

  const loading = useMemo(
    () => fetcher.state === "submitting" || fetcher.state === "loading",
    [fetcher.state],
  );

  return (
    <>
      <Card>
        <Box padding="200">
          <BlockStack gap="200">
            <Text variant="headingLg" as="h2">
              Timeline
            </Text>

            <Tabs tabs={tabs} selected={selected} onSelect={handleSelect}>
              <Timeline events={events} loading={loading} />
            </Tabs>
          </BlockStack>
        </Box>
      </Card>

      {(fetcher.data?.count ?? 0) > events.length && (
        <div className="flex justify-center mb-3">
          <Button onClick={() => fetcherLoad({ skip: events.length })} loading={loading}>
            Load more
          </Button>
        </div>
      )}
    </>
  );
}
