import { CustomerMetafield } from "@/constants";
import { BlockStack, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { useMemo, useState } from "react";
import { CustomerInterface } from "../types";
import { EditVipTierModal } from "./EditVipTierModal";

interface VipTierCardProps {
  customer: CustomerInterface | null;
  vipTierOptions: { name: string }[] | null;
}

export function VipTierCard({ customer, vipTierOptions }: VipTierCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const vipTier = useMemo(
    () =>
      customer?.metafields?.edges?.find((edge) => edge.node.key === CustomerMetafield.VIP_TIER)
        ?.node?.value || "",
    [customer],
  );

  const displayTier = vipTier || "No tier";

  return (
    <>
      <Card padding="400">
        <InlineStack align="space-between">
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">
              VIP Tier
            </Text>
            <Text as="p" variant="bodyMd" tone="subdued">
              {displayTier}
            </Text>
          </BlockStack>
          <BlockStack align="center">
            <Button onClick={() => setIsModalOpen(true)}>Edit tier</Button>
          </BlockStack>
        </InlineStack>
      </Card>

      <EditVipTierModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        currentTier={vipTier}
        vipTierOptions={vipTierOptions || []}
      />
    </>
  );
}
