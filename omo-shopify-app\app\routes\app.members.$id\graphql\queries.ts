export const CUSTOMER_QUERY = `
      #graphql
      query getCustomer($id: ID!) {
        customer(id: $id) {
          id
          legacyResourceId
          displayName
          email
          phone
          firstName
          lastName
          numberOfOrders
          image {
            url
          }
          amountSpent {amount currencyCode}
          addresses(first: 1) {address1 city country}
          createdAt
          metafields(first: 20, namespace: "$app") {
            edges {
              node {
                namespace
                key
                value
                createdAt
                updatedAt
              }
            }
          }
        }
      }
`;
