import { CustomerMetafield, MetafieldType } from "@/constants";
import db from "@/db.server";
import logger from "@/logger.server";
import { authenticate } from "@/shopify.server";
import { MetafieldInput } from "@/types/MetafieldInput";
import responseBadRequest from "@/utils/response.badRequest";
import { validatePayload } from "@/utils/validate.utils";
import { getMemberUpdateSchema } from "../member.schema";
import updateCustomerMetafields from "../services/updateCustomerMetafields";

export async function updateDetails(
  admin: Awaited<ReturnType<typeof authenticate.admin>>["admin"],
  customerId: string,
  payload: { birthday?: string; gender?: string },
) {
  const schema = await getMemberUpdateSchema(db);
  const { success, errors, data } = validatePayload(schema, payload);

  if (!success) {
    logger.error(`Validation failed for updateDetails: ${JSON.stringify(errors)}`);
    return responseBadRequest(errors, "Validation failed");
  }

  const metafields: MetafieldInput[] = [
    {
      key: CustomerMetafield.BIRTH_DATE,
      value: data.birthday,
      type: MetafieldType.DATE,
    },
    {
      key: CustomerMetafield.GENDER,
      value: data.gender,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
    },
  ];

  await updateCustomerMetafields(admin, customerId, metafields);
  logger.info(`Customer details updated successfully - ID: ${customerId}`);
  return { success: true, message: "Customer updated successfully" };
}
