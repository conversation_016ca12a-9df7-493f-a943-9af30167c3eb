import { CustomerMetafield, MetafieldType } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { authenticate } from "@/shopify.server";
import { MetafieldInput } from "@/types/MetafieldInput";
import responseBadRequest from "@/utils/response.badRequest";
import { validatePayload } from "@/utils/validate.utils";
import { CustomerTimelineType, Shop } from "@prisma/client";
import { getPointsSchema } from "../member.schema";
import updateCustomerMetafields from "../services/updateCustomerMetafields";

export async function updatePoints(
  admin: Awaited<ReturnType<typeof authenticate.admin>>["admin"],
  customerId: string,
  payload: { points?: number },
  shop: Shop,
) {
  const schema = await getPointsSchema();
  const { success, errors, data } = validatePayload(schema, payload);

  if (!success) {
    logger.error(`Validation failed for updatePoints: ${JSON.stringify(errors)}`);
    return responseBadRequest(errors, "Validation failed");
  }

  const metafields: MetafieldInput[] = [
    {
      key: CustomerMetafield.POINTS,
      value: data.points.toString(),
      type: MetafieldType.NUMBER_INTEGER,
    },
  ];

  await updateCustomerMetafields(admin, customerId, metafields);
  logger.info(`Customer points updated successfully - ID: ${customerId}`);

  await addCustomerTimeline({
    shopId: shop.id,
    customerId,
    message: `Points updated to ${data.points}`,
    type: CustomerTimelineType.POINTS,
  });

  return { success: true, message: "Customer updated successfully" };
}
