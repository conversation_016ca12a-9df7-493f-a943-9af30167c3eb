import { CustomerMetafield, MetafieldType } from "@/constants";
import db from "@/db.server";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { authenticate } from "@/shopify.server";
import { MetafieldInput } from "@/types/MetafieldInput";
import responseBadRequest from "@/utils/response.badRequest";
import { validatePayload } from "@/utils/validate.utils";
import { CustomerTimelineType, Shop } from "@prisma/client";
import { getVipTierSchema } from "../member.schema";
import updateCustomerMetafields from "../services/updateCustomerMetafields";

export async function updateVipTier(
  admin: Awaited<ReturnType<typeof authenticate.admin>>["admin"],
  customerId: string,
  payload: { vipTier?: string },
  shop: Shop,
) {
  const schema = await getVipTierSchema(db, shop);
  const { success, errors, data } = validatePayload(schema, payload);

  logger.debug(`Payload for updateVipTier: ${JSON.stringify(payload)}`);
  logger.debug(`Validation result for updateVipTier: ${JSON.stringify({ success, errors, data })}`);

  if (!success) {
    logger.error(`Validation failed for updateVipTier: ${JSON.stringify(errors)}`);
    return responseBadRequest(errors, "Validation failed");
  }

  const metafields: MetafieldInput[] = [
    {
      key: CustomerMetafield.VIP_TIER,
      value: data.vipTier,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
    },
  ];

  await updateCustomerMetafields(admin, customerId, metafields);
  logger.info(`Customer vip tier updated successfully - ID: ${customerId}`);

  await addCustomerTimeline({
    shopId: shop.id,
    customerId,
    message: `VIP tier updated to ${data.vipTier}`,
    type: CustomerTimelineType.OTHER,
  });

  return { success: true, message: "Customer updated successfully" };
}
