import {gidPrefix} from "@/constants";
import db from "@/db.server";
import logger from "@/logger.server";
import {authenticate} from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import {LoyaltyProgramType} from "@prisma/client";
import {LoaderFunctionArgs} from "@remix-run/node";
import {CUSTOMER_QUERY} from "./graphql";
import {CustomerInterface} from "./types";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const { id } = params;

  if (!id) return { notFound: true, customer: null, shop: null, error: null };

  try {
    const shopifyCustomerId = id.includes("gid:") ? id : `${gidPrefix.CUSTOMER}${id}`;

    const response = await admin.graphql(CUSTOMER_QUERY, {
      variables: { id: shopifyCustomerId },
    });

    const responseJson = await response.json();

    if (!responseJson.data?.customer) return { notFound: true };

    const customer = responseJson.data.customer as CustomerInterface;

    const shopDomain = session.shop;
    const shop = await findShop(admin, session);

    const genderOptions = await db.gender.findMany({});

    const loyaltyProgram = await db.loyaltyProgram.findFirst({
      where: {
        shopId: shop.id,
        programType: LoyaltyProgramType.VIP_TIER,
      },
      include: {
        vipTiers: true,
      },
    });

    return {
      customer,
      genderOptions,
      vipTierOptions: loyaltyProgram?.vipTiers || [],
      notFound: false,
      shop: shopDomain,
      id,
    };
  } catch (error) {
    logger.error("Error fetching customer:", error);
    return {
      notFound: true,
      error: String(error),
      customer: null,
      shop: null,
      vipTierOptions: [],
      genderOptions: [],
    };
  }
};
