import { LoyaltyProgramType, PrismaClient, Shop } from "@prisma/client";
import <PERSON><PERSON> from "joi";

export const getPointsSchema = async () => {
  return Joi.object({
    points: Joi.number().integer().min(0).required().messages({
      "number.base": "Points must be a number",
      "number.integer": "Points must be an integer",
      "number.min": "Points cannot be negative",
      "any.required": "Points are required",
    }),
    _action: Joi.string().optional().default(""),
  });
};

export const getMemberUpdateSchema = async (db: PrismaClient) => {
  const genders = await db.gender.findMany({ select: { name: true } });
  const validGenders = genders.map((g) => g.name);

  return Joi.object({
    birthday: Joi.string().isoDate().required().messages({
      "string.isoDate": "Birthday must be a valid ISO date string (YYYY-MM-DD)",
      "any.required": "Birthday is required",
    }),
    gender: Joi.string()
      .valid(...validGenders)
      .required()
      .messages({
        "any.only": "Gender must be one of " + validGenders.join(", ") + ".",
        "any.required": "Gender is required",
      }),
    _action: Joi.string().optional().default(""),
  });
};

export const getVipTierSchema = async (db: PrismaClient, shop: Shop) => {
  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: { shopId: shop.id, programType: LoyaltyProgramType.VIP_TIER },
    include: {
      vipTiers: true,
    },
  });
  const vipTiers = loyaltyProgram?.vipTiers;
  const validVipTiers = vipTiers?.map((v) => v.name) ?? [];

  return Joi.object({
    vipTier: Joi.string()
      .valid(...validVipTiers)
      .required()
      .messages({
        "any.only": "Vip tier must be one of " + validVipTiers.join(", ") + ".",
        "any.required": "Vip tier is required",
      }),
    _action: Joi.string().optional().default(""),
  });
};
