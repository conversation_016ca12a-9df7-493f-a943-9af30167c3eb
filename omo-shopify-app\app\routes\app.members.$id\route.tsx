import { useLoaderData, useNavigate } from "@remix-run/react";
import { BlockStack, InlineGrid, Layout, Page, Text } from "@shopify/polaris";
import { action } from "./action";
import {
  LoyaltyPointsCard,
  MemberProfileCard,
  StatsGrid,
  TimelineCard,
  VipTierCard,
} from "./components";

import { TitleBar } from "@shopify/app-bridge-react";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { loader } from "./loader";
import { MemberLoaderData } from "./types";

export { action, loader };

export default function MemberDetailPage() {
  const { customer, genderOptions, vipTierOptions, id } = useLoaderData<
    typeof loader
  >() as MemberLoaderData;

  const navigate = useNavigate();
  const { t } = useTranslation();

  const shopifyUrl = `shopify://admin/customers/${id}`;

  return (
    <Page fullWidth>
      <TitleBar title={customer?.displayName ?? ""}>
        <button variant="breadcrumb" id="members" onClick={() => navigate("/app/members")}>
          {t("members.title")}
        </button>
        <a href={shopifyUrl} target="_top">
          View in Shopify
        </a>
      </TitleBar>
      <Layout>
        <Layout.Section>
          <InlineGrid columns={{ xs: "1fr", md: "2fr 1fr" }} gap="400">
            <BlockStack gap="400">
              <StatsGrid customer={customer} />

              <TimelineCard />
            </BlockStack>

            <BlockStack gap="400">
              <MemberProfileCard customer={customer} genderOptions={genderOptions} />
              <LoyaltyPointsCard customer={customer} />
              <VipTierCard customer={customer} vipTierOptions={vipTierOptions} />

              <div className="pl-3">
                <Text as="p" variant="bodyMd">
                  Created at: {dayjs(customer?.createdAt).format("YYYY-MM-DD HH:mm:ss")}
                </Text>
              </div>
            </BlockStack>
          </InlineGrid>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
