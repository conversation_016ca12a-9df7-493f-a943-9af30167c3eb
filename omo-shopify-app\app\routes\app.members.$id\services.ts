import db from "@/db.server";

/**
 * Fetches customers history list
 */
export async function getCustomerHistory(shopifyCustomerId: string) {
  if (!shopifyCustomerId) return;
  const events = await db.customerHistory.findMany({
    where: {
      customerId: shopifyCustomerId,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
  return events;
}

export async function createCustomerHistory(data: any) {
  await db.customerHistory.create({
    data: data,
  });
}
