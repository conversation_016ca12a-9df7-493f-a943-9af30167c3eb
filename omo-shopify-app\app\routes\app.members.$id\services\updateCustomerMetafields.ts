import { authenticate } from "@/shopify.server";
import { MetafieldInput } from "@/types/MetafieldInput";
import { ShopifyUserError } from "@/utils/ShopifyUserError";
import { CUSTOMER_UPDATE_MUTATION } from "../graphql";

export default async function updateCustomerMetafields(
  adminClient: Awaited<ReturnType<typeof authenticate.admin>>["admin"],
  customerId: string,
  metafields: MetafieldInput[],
) {
  const response = await adminClient.graphql(CUSTOMER_UPDATE_MUTATION, {
    variables: { input: { id: customerId, metafields } },
  });
  const json = await response.json();
  const userErrors = json.data?.customerUpdate?.userErrors;
  if (userErrors?.length) {
    const errors = userErrors.map((err: { field?: string[]; message: string }) => ({
      field: err.field?.join(".") ?? "",
      code: "GRAPHQL_ERROR",
      message: err.message,
    }));
    throw new ShopifyUserError(errors);
  }
}
