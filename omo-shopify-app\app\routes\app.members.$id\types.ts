import { MemberVipTier } from "@/constants";

export interface MemberInterface {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  points: number;
  vipTier: MemberVipTier;
  ordersCount: number;
  totalSpent: {
    amount: number;
    currencyCode: string;
  };
  address?: string;
  birthday?: string;
  socialLinks?: {
    facebook?: string;
    google?: string;
    line?: string;
  };
  avatar?: string;
  createdAt: string;
}

export interface MemberEventInterface {
  id: string;
  type:
    | "order_placed"
    | "order_payment"
    | "vip_upgrade"
    | "points_earned"
    | "points_redeemed"
    | "refund";
  description: string;
  timestamp: string;
  data?: {
    orderId?: string;
    pointsAmount?: number;
    vipTier?: MemberVipTier;
    orderNumericId?: string;
    amount?: number;
  };
}

export interface MemberDetails extends MemberInterface {
  events: MemberEventInterface[];
  lifetimeOrders: number;
  lifetimeSpent: {
    amount: number;
    currencyCode: string;
  };
}

export interface CustomerMetafieldsInterface {
  points?: string;
  vipTier?: string;
  birthday?: string;
  gender?: string;
  registerDate?: string;
  registerEmployee?: string;
  registerLocation?: string;
}

export interface MoneyInterface {
  amount: string;
  currencyCode: string;
}

export interface MoneySetInterface {
  shopMoney: MoneyInterface;
}

export interface AddressInterface {
  address1: string;
  city: string;
  country: string;
}

export interface TransactionInterface {
  id: string;
  kind: string;
  status: string;
  amountSet: MoneySetInterface;
  processedAt: string; // ISO timestamp
}

export interface RefundInterface {
  id: string;
  createdAt: string; // ISO timestamp
  totalRefundedSet: MoneySetInterface;
}

export interface OrderInterface {
  id: string;
  legacyResourceId: string;
  name: string;
  processedAt: string; // ISO timestamp
  displayFinancialStatus: string;
  displayFulfillmentStatus: string;
  closed: boolean;
  cancelledAt: string | null; // ISO timestamp or null
  totalPriceSet: MoneySetInterface;
  subtotalPriceSet: MoneySetInterface;
  totalDiscountsSet: MoneySetInterface;
  fullyPaid: boolean;
  transactions: TransactionInterface[];
  refunds: RefundInterface[];
}

export interface OrderEdgeInterface {
  node: OrderInterface;
}

export interface OrdersConnectionInterface {
  edges: OrderEdgeInterface[];
}
export interface MetafieldInterface {
  namespace: string;
  key: string;
  value: string;
  createdAt: string; // ISO timestamp
  updatedAt: string; // ISO timestamp
}

export interface MetafieldEdgeInterface {
  node: MetafieldInterface;
}

export interface MetafieldsConnectionInterface {
  edges: MetafieldEdgeInterface[];
}

export interface CustomerInterface {
  id: string;
  legacyResourceId: string;
  displayName: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  numberOfOrders: number;
  amountSpent: MoneyInterface;
  addresses: AddressInterface[];
  orders: OrdersConnectionInterface;
  metafields: MetafieldsConnectionInterface;
  image: {
    url: string;
  };
  createdAt: string;
}

export interface GetCustomerData {
  customer: CustomerInterface | null;
}

export interface GetCustomerVars {
  id: string;
}

export interface MemberLoaderData {
  notFound: boolean;
  customer: CustomerInterface | null;
  shop: string | null;
  customerEvents: MemberEventInterface[] | null;
  error: string | null;
  genderOptions: { name: string }[] | null;
  vipTierOptions: { name: string }[] | null;
  id: string;
}
