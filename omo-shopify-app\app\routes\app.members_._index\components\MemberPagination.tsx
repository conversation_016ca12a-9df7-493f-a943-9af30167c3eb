import { Button, Text } from "@shopify/polaris";
import { ArrowLeftIcon, ArrowRightIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { PageInfo } from "../../../types/memberTypes";
import { ITEMS_PER_PAGE } from "../constants";

interface MemberPaginationProps {
  customers: any[];
  pageInfo: PageInfo;
  currentPage: number;
  totalCustomers: number;
  isLoading: boolean;
  onPageChange: (direction: "next" | "previous") => void;
}

export function MemberPagination({
  customers,
  pageInfo,
  currentPage,
  totalCustomers,
  isLoading,
  onPageChange,
}: MemberPaginationProps) {
  const { t } = useTranslation();

  if (customers.length === 0) {
    return null;
  }

  // Calculate displayed range
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE + 1;
  const endIndex = (currentPage - 1) * ITEMS_PER_PAGE + customers.length;
  const displayedRange = `${startIndex}-${endIndex}`;

  return (
    <div className="p-4 border-t border-gray-200 flex justify-between items-center">
      <Text variant="bodySm" tone="subdued" as="span">
        {`${t("members.pagination.showing")} ${displayedRange} ${t("members.pagination.of")} ${totalCustomers}`}
      </Text>

      <div className="flex items-center">
        <Button
          icon={ArrowLeftIcon}
          disabled={!pageInfo.hasPreviousPage || isLoading}
          onClick={() => onPageChange("previous")}
          accessibilityLabel={t("members.pagination.previous")}
          variant="plain"
        />

        <Button
          icon={ArrowRightIcon}
          disabled={!pageInfo.hasNextPage || isLoading}
          onClick={() => onPageChange("next")}
          accessibilityLabel={t("members.pagination.next")}
          variant="plain"
        />
      </div>
    </div>
  );
}
