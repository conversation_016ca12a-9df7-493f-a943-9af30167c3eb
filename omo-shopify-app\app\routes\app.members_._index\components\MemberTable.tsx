import { Member } from "@/types";
import { Link } from "@remix-run/react";
import { Avatar, Box, EmptySearchResult, IndexTable, Spinner, Text } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";
import { MEMBER_RESOURCE_NAME } from "../constants";

interface MemberTableProps {
  customers: Member[];
  isLoading: boolean;
  emptyStateMarkup?: React.ReactNode;
}

export function MemberTable({
  customers,
  isLoading,
  emptyStateMarkup = (
    <EmptySearchResult
      title="No members found"
      description="Try changing filters or search keywords"
      withIllustration
    />
  ),
}: MemberTableProps) {
  const { t } = useTranslation();

  return (
    <div style={{ position: "relative" }}>
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 flex justify-center items-center z-10">
          <Spinner size="large" />
        </div>
      )}

      <IndexTable
        resourceName={MEMBER_RESOURCE_NAME}
        itemCount={customers.length}
        selectable={false}
        headings={[
          { title: t("members.table.name"), alignment: "start" },
          { title: t("members.table.points"), alignment: "start" },
          { title: t("members.table.vipTier"), alignment: "start" },
          { title: t("members.table.orders"), alignment: "start" },
          { title: t("members.table.totalSpent"), alignment: "start" },
        ]}
        emptyState={emptyStateMarkup}
      >
        {customers.map((customer: Member, index: number) => (
          <MemberTableRow customer={customer} index={index} key={customer.id} />
        ))}
      </IndexTable>
    </div>
  );
}

interface MemberTableRowProps {
  customer: Member;
  index: number;
}

function MemberTableRow({ customer, index }: MemberTableRowProps) {
  return (
    <IndexTable.Row id={customer.id} position={index}>
      {/* Name Cell */}
      <IndexTable.Cell>
        <Box>
          <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <Avatar customer size="md" name={customer.fullName.charAt(0)} />
            <div>
              <Link
                to={`/app/members/${customer.numericId}`}
                prefetch="intent"
                style={{ textDecoration: "none" }}
              >
                <Text variant="bodyMd" fontWeight="bold" as="span">
                  {customer.fullName}
                </Text>
              </Link>

              {customer.email && (
                <Text variant="bodySm" as="p" tone="subdued">
                  {customer.email}
                </Text>
              )}
            </div>
          </div>
        </Box>
      </IndexTable.Cell>

      {/* Points Cell */}
      <IndexTable.Cell>
        <Text
          variant="bodyMd"
          tone={customer.points && Number(customer.points) > 0 ? "success" : "subdued"}
          as="span"
        >
          {customer.points && Number(customer.points) > 0 ? customer.points : "No points"}
        </Text>
      </IndexTable.Cell>

      {/* VIP Tier Cell */}
      <IndexTable.Cell>
        {customer.vipTier ? (
          <Text variant="bodyMd" tone="subdued" as="span">
            {customer.vipTier}
          </Text>
        ) : (
          <Text variant="bodyMd" tone="subdued" as="span">
            No tier
          </Text>
        )}
      </IndexTable.Cell>

      {/* Orders Count Cell */}
      <IndexTable.Cell>
        <Text variant="bodyMd" as="span">
          {customer.ordersCount}
        </Text>
      </IndexTable.Cell>

      {/* Total Spent Cell */}
      <IndexTable.Cell>
        <Text variant="bodyMd" as="span">
          {customer.totalSpent.currencyCode}
          {Number(customer.totalSpent.amount).toFixed(2)}
        </Text>
      </IndexTable.Cell>
    </IndexTable.Row>
  );
}
