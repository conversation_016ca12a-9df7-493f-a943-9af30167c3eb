// File: app/routes/app.members/hooks.ts
import { useLocation, useSubmit } from "@remix-run/react";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { PageInfo } from "../../types/memberTypes";

/**
 * Hook for handling member search and pagination functionality
 */
export function useMemberSearch(initialData: {
  searchQuery: string;
  searchType: string;
  currentPage: number;
  pageInfo: PageInfo;
}) {
  const { t } = useTranslation();

  // State for search parameters
  const [queryValue, setQueryValue] = useState(initialData.searchQuery);
  const [selectedSearchType, setSelectedSearchType] = useState(initialData.searchType || "email");

  const submit = useSubmit();
  const location = useLocation();

  // Check if we're in detail view
  const isDetailView = location.pathname.split("/").length > 3;

  // Handle search input change
  const handleFiltersQueryChange = useCallback((value: string) => {
    setQueryValue(value);
  }, []);

  // Handle search type change
  const handleSearchTypeChange = useCallback((value: string) => {
    setSelectedSearchType(value);
  }, []);

  // Get placeholder text based on search type
  const getPlaceholder = useCallback(() => {
    return selectedSearchType === "email"
      ? t("members.search.placeholder.email")
      : t("members.search.placeholder.name");
  }, [selectedSearchType, t]);

  // Clear search query
  const handleQueryValueRemove = useCallback(() => {
    setQueryValue("");
    submit(
      {
        page: String(initialData.currentPage),
      },
      { method: "get", replace: true },
    );
  }, [initialData.currentPage, submit]);

  // Handle pagination
  const handlePaginationChange = useCallback(
    (direction: "next" | "previous") => {
      const newPage =
        direction === "next" ? initialData.currentPage + 1 : initialData.currentPage - 1;

      const newCursor =
        direction === "next" ? initialData.pageInfo.endCursor : initialData.pageInfo.startCursor;

      submit(
        {
          query: queryValue,
          searchType: selectedSearchType,
          page: String(newPage),
          cursor: newCursor,
          direction: direction,
        },
        { method: "get", replace: true },
      );
    },
    [initialData.currentPage, initialData.pageInfo, queryValue, selectedSearchType, submit],
  );

  // Handle search submission
  const handleSearch = useCallback(() => {
    submit(
      {
        query: queryValue,
        searchType: selectedSearchType,
        page: "1",
      },
      { method: "get", replace: true },
    );
  }, [queryValue, selectedSearchType, submit]);

  return {
    queryValue,
    selectedSearchType,
    isDetailView,
    handleFiltersQueryChange,
    handleSearchTypeChange,
    getPlaceholder,
    handleQueryValueRemove,
    handlePaginationChange,
    handleSearch,
  };
}
