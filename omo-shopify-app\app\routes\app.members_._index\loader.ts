import type { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../../shopify.server";
import { MembersLoaderData } from "../../types/memberTypes";
import { fetchCustomers } from "./services";

export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);
  const url = new URL(request.url);

  // Extract search parameters from URL
  const page = parseInt(url.searchParams.get("page") ?? "1", 10);
  const searchQuery = url.searchParams.get("query") ?? "";
  const searchType = url.searchParams.get("searchType") ?? "email";
  const cursor = url.searchParams.get("cursor") ?? null;
  const direction = url.searchParams.get("direction") ?? "next";

  // Fetch customers data
  const customersData = await fetchCustomers(admin, {
    page,
    searchQuery,
    searchType,
    cursor,
    direction,
  });

  return {
    ...customersData,
    searchQuery,
    searchType,
    shopId: session.shop,
  };
}
export type { MembersLoaderData as LoaderData };
