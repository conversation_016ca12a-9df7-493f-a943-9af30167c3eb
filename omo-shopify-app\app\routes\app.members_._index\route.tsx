import { useLoaderData, useNavigation } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { Box, Card, EmptySearchResult, Page, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { MemberPagination, MemberSearchBar, MemberTable } from "./components";
import { useMemberSearch } from "./hooks";
import type { LoaderData } from "./loader";
export { loader } from "./loader";

export default function MembersPage() {
  const { t } = useTranslation();
  const loaderData = useLoaderData<LoaderData>();
  const {
    customers,
    pageInfo,
    currentPage,
    searchQuery,
    searchType,
    totalCustomers,
    errorMessage,
  } = loaderData;

  const navigation = useNavigation();
  const isLoading = navigation.state === "loading";

  const {
    queryValue,
    selectedSearchType,
    handleFiltersQueryChange,
    handleSearchTypeChange,
    getPlaceholder,
    handleQueryValueRemove,
    handlePaginationChange,
    handleSearch,
  } = useMemberSearch({
    searchQuery,
    searchType,
    currentPage,
    pageInfo,
  });

  return (
    <Page fullWidth>
      <TitleBar title={t("members.title")} />

      <Card padding="200">
        <Box padding="400">
          <MemberSearchBar
            queryValue={queryValue}
            selectedSearchType={selectedSearchType}
            placeholder={getPlaceholder()}
            isLoading={isLoading}
            onQueryChange={handleFiltersQueryChange}
            onSearchTypeChange={handleSearchTypeChange}
            onClearButtonClick={handleQueryValueRemove}
            onSearch={handleSearch}
          />
        </Box>

        {errorMessage && (
          <Box>
            <Text tone="critical" as="p">
              {t("members.error.loading")} {errorMessage ?? t("members.error.unknown")}
            </Text>
          </Box>
        )}

        <MemberTable
          customers={customers}
          isLoading={isLoading}
          emptyStateMarkup={
            <EmptySearchResult
              title={t("members.emptyState.title")}
              description={t("members.emptyState.description")}
              withIllustration
            />
          }
        />

        <MemberPagination
          customers={customers}
          pageInfo={pageInfo}
          currentPage={currentPage}
          totalCustomers={totalCustomers}
          isLoading={isLoading}
          onPageChange={handlePaginationChange}
        />
      </Card>
    </Page>
  );
}
