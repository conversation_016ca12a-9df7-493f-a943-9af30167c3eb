// File: app/routes/app.members/services.ts
import { CustomerMetafield, MetafieldType } from "@/constants";
import {
  ADD_CUSTOMER_TAGS_MUTATION,
  GET_CUSTOMERS_QUERY,
  GET_TOTAL_CUSTOMERS_QUERY,
} from "../../graphql/memberQueries";
import { Member, MemberMetafield, PageInfo } from "../../types/memberTypes";
import { getNamespaceMetafield } from "../webhooks.refunds.create/services";
import { ITEMS_PER_PAGE } from "./constants";

/**
 * Fetches customers list with pagination
 */
export async function fetchCustomers(
  admin: any,
  options: {
    page: number;
    searchQuery: string;
    searchType: string;
    cursor: string | null;
    direction: string;
  },
) {
  const { page, searchQuery, searchType, cursor, direction } = options;

  // Build search query based on search type without exclusions
  let queryFilter = "";
  if (searchQuery && searchQuery.trim() !== "") {
    const trimmedQuery = searchQuery.trim();
    if (searchType === "name") {
      queryFilter = `first_name:*${trimmedQuery}* OR last_name:*${trimmedQuery}*`;
    } else if (searchType === "email") {
      queryFilter = `email:*${trimmedQuery}*`;
    }
  }

  console.log("Search query filter:", queryFilter); // For debugging

  // Prepare GraphQL variables for pagination
  const graphQLVariables: any = {
    query: queryFilter || undefined,
  };

  if (direction === "previous") {
    graphQLVariables.last = ITEMS_PER_PAGE;
    graphQLVariables.before = cursor;
  } else {
    graphQLVariables.first = ITEMS_PER_PAGE;
    if (cursor) {
      graphQLVariables.after = cursor;
    }
  }

  try {
    // Execute GraphQL queries in parallel
    const [response, countResponse] = await Promise.all([
      admin.graphql(GET_CUSTOMERS_QUERY, { variables: graphQLVariables }),
      admin.graphql(GET_TOTAL_CUSTOMERS_QUERY, {
        variables: { query: queryFilter || undefined },
      }),
    ]);

    const responseJson = await response.json();
    const countResponseJson = await countResponse.json();

    const totalCustomers = countResponseJson.data?.customers?.edges?.length ?? 0;
    const pageInfo: PageInfo = responseJson.data?.customers?.pageInfo ?? {
      hasNextPage: false,
      hasPreviousPage: false,
      startCursor: null,
      endCursor: null,
    };
    const namespace = (await getNamespaceMetafield(admin)) as string;

    // Parse and transform customer data
    const customers =
      responseJson.data?.customers?.edges?.map(({ node, cursor }: any) =>
        parseCustomerData(node, cursor, admin, namespace),
      ) ?? [];

    return {
      customers,
      pageInfo,
      totalCustomers,
      currentPage: page,
    };
  } catch (error: any) {
    console.error("GraphQL Error:", error);
    return {
      customers: [],
      pageInfo: {
        hasNextPage: false,
        hasPreviousPage: false,
        startCursor: null,
        endCursor: null,
      },
      totalCustomers: 0,
      currentPage: page,
      errorMessage: error.message ?? "An error occurred while fetching data",
    };
  }
}

/**
 * Parse customer data from GraphQL response
 */
function parseCustomerData(node: any, cursor: string, admin: any, namespace: string): Member {
  // Extract metafields
  const metafields = node.metafields?.edges.map((m: any) => ({
    namespace: m.node.namespace,
    key: m.node.key,
    value: m.node.value,
  })) as MemberMetafield[];

  // Find points metafield
  const pointsMetafield = metafields.find(
    (m) => m.namespace === namespace && m.key === CustomerMetafield.POINTS,
  );
  const points = pointsMetafield ? pointsMetafield.value : "0";

  // Find VIP tier metafield
  const vipTierMetafield = metafields.find(
    (m) => m.namespace === namespace && m.key === CustomerMetafield.VIP_TIER,
  );
  const vipTier = vipTierMetafield ? vipTierMetafield.value : null;

  // Find full name metafield
  const fullNameMetafield = metafields.find(
    (m) => m.namespace === namespace && m.key === CustomerMetafield.FULL_NAME,
  );

  // Get fullName from metafield or construct it
  const fullName = fullNameMetafield
    ? fullNameMetafield.value
    : node.displayName ||
      `${node.firstName || ""}${node.lastName ? " " + node.lastName : ""}`.trim() ||
      node.email;

  // Add "has_points" tag if customer has points but no tag
  if (parseInt(points, 10) > 0 && !node.tags.includes("has_points")) {
    addHasPointsTag(admin, node.id).catch((err) => console.error("Error tagging customer:", err));
  }

  // Construct member object
  return {
    id: node.id,
    cursor: cursor,
    numericId: node.legacyResourceId || node.id.split("/").pop(),
    fullName: fullName,
    email: node.email || "",
    phone: node.phone || "",
    ordersCount: node.numberOfOrders || 0,
    totalSpent: {
      amount: node.amountSpent?.amount ?? 0,
      currencyCode: node.amountSpent?.currencyCode ?? "TWD",
    },
    points: points,
    vipTier: vipTier,
    tags: node.tags || [],
  };
}

/**
 * Add "has_points" tag to a customer
 */
async function addHasPointsTag(admin: any, customerId: string) {
  return admin.graphql(ADD_CUSTOMER_TAGS_MUTATION, {
    variables: {
      id: customerId,
      tags: ["has_points"],
    },
  });
}

/**
 * Update customer's full name metafield
 */
export async function updateCustomerFullNameMetafield(
  admin: any,
  customerId: string,
  firstName: string,
  lastName: string,
) {
  const fullName = `${firstName || ""} ${lastName || ""}`.trim();

  if (!fullName) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                namespace: "$app",
                key: CustomerMetafield.FULL_NAME,
                value: fullName,
                type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer full name metafield:", error);
  }
}
