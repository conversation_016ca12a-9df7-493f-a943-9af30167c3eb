import { LoaderFunctionArgs } from "@remix-run/node";
import { i18nextCookie } from "../../cookie.server";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  const response = await admin.graphql(`
    #graphql
      query GetShopDetails {
        shop {
          id
          name
        }
      }
    `);

  const responseJson = await response.json();

  const result = await db.shop.findFirst({
    where: {
      shopId: responseJson.data.shop.id,
    },
  });

  if (!result) {
    throw new Error("Shop not found");
  }

  // Handle different actions
  switch (action) {
    case "generateApiKey": {
      const newKey =
        "sk_key_" +
        Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);

      await db.shop.update({
        where: {
          id: result.id,
        },
        data: {
          apiKey: newKey,
        },
      });

      return { apiKey: newKey };
    }

    case "updateLanguage": {
      const language = formData.get("language") as string;

      if (!language) {
        throw new Error("Language is required");
      }

      // Create headers with the cookie
      const headers = new Headers();
      headers.set("Set-Cookie", await i18nextCookie.serialize(language));

      // Return a Response object with the cookie header
      return new Response(JSON.stringify({ language }), {
        headers,
        status: 200,
      });
    }

    case "updateAclasPosConfig": {
      const merchantNo = formData.get("merchantNo") as string;
      const appId = formData.get("appId") as string;
      const appSecret = formData.get("appSecret") as string;
      const isActive = formData.get("isActive") === "true";

      // Update or create the config
      const updatedConfig = await db.aclasPosConfig.upsert({
        where: { shopId: result.id },
        update: {
          merchantNo,
          appId,
          appSecret,
          isActive,
        },
        create: {
          shopId: result.id,
          merchantNo,
          appId,
          appSecret,
          isActive,
        },
      });

      return updatedConfig;
    }

    default:
      throw new Error("Invalid action");
  }
};

export default action;
