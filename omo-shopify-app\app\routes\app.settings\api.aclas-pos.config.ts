import { json } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

type AclasPosConfigData = {
  merchantNo: string;
  appId: string;
  appSecret: string;
  isActive: boolean;
};

export async function loader({ request }: { request: Request }) {
  const { session } = await authenticate.admin(request);
  const shop: string = session.shop;

  try {
    // First find the shop by myshopifyDomain
    const shopRecord = await db.shop.findFirst({
      where: { myshopifyDomain: shop },
      include: { aclasPosConfig: true },
    });

    // If shop not found, return null
    if (!shopRecord) {
      return json(null);
    }

    // Return the config if it exists, otherwise null
    const config = shopRecord.aclasPosConfig || null;
    return json(config);
  } catch (error) {
    console.error("Error fetching ACLAS POS config:", error);
    return json({ error: "Failed to fetch ACLAS POS configuration" }, { status: 500 });
  }
}

export async function action({ request }: { request: Request }) {
  const { session } = await authenticate.admin(request);
  const shop: string = session.shop;
  const data: AclasPosConfigData = await request.json();

  try {
    // First find the shop by myshopifyDomain
    const shopRecord = await db.shop.findFirst({
      where: { myshopifyDomain: shop },
    });

    if (!shopRecord) {
      return json({ error: "Shop not found" }, { status: 404 });
    }

    // Update or create the config
    const updatedConfig = await db.aclasPosConfig.upsert({
      where: { shopId: shopRecord.id },
      update: {
        merchantNo: data.merchantNo,
        appId: data.appId,
        appSecret: data.appSecret,
        isActive: data.isActive,
      },
      create: {
        shopId: shopRecord.id,
        merchantNo: data.merchantNo,
        appId: data.appId,
        appSecret: data.appSecret,
        isActive: data.isActive,
      },
    });

    return json(updatedConfig);
  } catch (error) {
    console.error("Error saving ACLAS POS config:", error);
    return json(
      { error: "Failed to save ACLAS POS configuration" },
      { status: 500 }
    );
  }
}
