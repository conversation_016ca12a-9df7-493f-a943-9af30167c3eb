import { useLoaderData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Badge, BlockStack, Box, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { ClipboardIcon } from "@shopify/polaris-icons";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { LoaderData } from "../types";

export default function APIEndpointCard() {
  const { t } = useTranslation();
  const shopify = useAppBridge();
  const loaderData = useLoaderData<LoaderData>();
  const domain = loaderData.hostname ?? "your-domain.com";

  const copyEndpoint = useCallback(
    (text: string) => {
      navigator.clipboard.writeText(text.replace("<<domain>>", domain));
      shopify.toast.show(t("settings.endpointCopied"));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [domain],
  );

  const endpoints = [
    {
      title: t("settings.endpoints.addMember") || "Add New Member",
      method: "POST",
      endpoint: `https://<<domain>>/api/members`,
      authorization: `Authorization: Bearer <token>`,
    },
    {
      title: t("settings.endpoints.queryMember") || "Query Member Basic Information",
      method: "GET",
      endpoint: `https://<<domain>>/api/members`,
      authorization: `Authorization: Bearer <token>`,
    },
    {
      title: t("settings.endpoints.updateMember") || "Modify Member Basic Information",
      method: "PATCH",
      endpoint: `https://<<domain>>/api/members/<<memberId>>`,
      authorization: `Authorization: Bearer <token>`,
    },
    {
      title: t("settings.endpoints.cancelMembership") || "Cancel Membership",
      method: "POST",
      endpoint: `https://<<domain>>/api/members/cancel`,
      authorization: `Authorization: Bearer <token>`,
    },
    {
      title: t("settings.endpoints.queryOffers") || "Query Member's Available Offers",
      method: "GET",
      endpoint: `https://<<domain>>/api/offers/availables`,
      authorization: `Authorization: Bearer <token>`,
    },
    {
      title: t("settings.endpoints.addOrder") || "Add In-Store Member Order",
      method: "POST",
      endpoint: `https://<<domain>>/api/orders`,
      authorization: `Authorization: Bearer <token>`,
    },
  ];

  const getMethodBadgeColor = (method: string) => {
    switch (method) {
      case "GET":
        return "info";
      case "POST":
        return "success";
      case "PATCH":
        return "warning";
      case "DELETE":
        return "critical";
      default:
        return "info";
    }
  };

  return (
    <Card>
      <BlockStack gap="400">
        <Box paddingInlineStart="400" paddingInlineEnd="400" paddingBlockStart="400">
          <InlineStack align="space-between">
            <BlockStack gap="200">
              <Text as="h2" variant="headingMd" fontWeight="semibold">
                {t("settings.endpointsTitle") || "API Endpoints"}
              </Text>
              <Text as="p" variant="bodyMd" tone="subdued">
                {t("settings.endpointsDescription") ||
                  "Authorization method: Using Bearer Token authentication"}
              </Text>
            </BlockStack>
          </InlineStack>
        </Box>
        <BlockStack gap="300">
          {endpoints.map((endpoint, index) => (
            <Box
              key={index}
              paddingInlineStart="400"
              paddingInlineEnd="400"
              paddingBlockEnd={index === endpoints.length - 1 ? "400" : "0"}
            >
              <Card>
                <Box
                  paddingInlineStart="400"
                  paddingInlineEnd="400"
                  paddingBlock="300"
                  borderBlockEndWidth="025"
                >
                  <InlineStack align="space-between">
                    <Text as="h3" variant="headingSm">
                      {endpoint.title}
                    </Text>
                  </InlineStack>
                </Box>
                <Box
                  paddingInlineStart="400"
                  paddingInlineEnd="400"
                  paddingBlock="300"
                  background="bg-surface-secondary"
                >
                  <InlineStack align="space-between" gap="200">
                    <InlineStack gap="200" align="center">
                      <Badge tone={getMethodBadgeColor(endpoint.method)}>{endpoint.method}</Badge>
                      <Text as="span" variant="bodyMd" fontWeight="medium">
                        <code>{endpoint.endpoint.replace("<<domain>>", domain)}</code>
                      </Text>
                    </InlineStack>
                    <Button
                      size="slim"
                      onClick={() => copyEndpoint(endpoint.endpoint)}
                      icon={ClipboardIcon}
                    >
                      {t("settings.copyButton") || "Copy"}
                    </Button>
                  </InlineStack>
                </Box>
              </Card>
            </Box>
          ))}
        </BlockStack>
      </BlockStack>
    </Card>
  );
}
