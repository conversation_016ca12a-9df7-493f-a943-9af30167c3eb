import { useF<PERSON>cher, useLoaderData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Card,
  Divider,
  Form,
  FormLayout,
  InlineStack,
  Text,
  TextField,
} from "@shopify/polaris";
import { ToggleOffIcon, ToggleOnIcon } from "@shopify/polaris-icons";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { z } from "zod";

// Types
type LoaderData = {
  aclasPosConfig?: {
    id?: number;
    merchantNo: string;
    appId: string;
    appSecret: string;
    isActive: boolean;
  } | null;
  [key: string]: any;
};

// Define Zod schema for form validation
const aclasPosConfigSchema = z.object({
  merchantNo: z.string().min(1, { message: "merchantNoRequired" }),
  appId: z.string().min(1, { message: "appIdRequired" }),
  appSecret: z.string().min(1, { message: "appSecretRequired" }),
  isActive: z.boolean().default(false),
  id: z.number().optional(),
});

type AclasPosConfig = z.infer<typeof aclasPosConfigSchema>;

type FormErrors = {
  merchantNo?: string;
  appId?: string;
  appSecret?: string;
};

export default function AclasPosConfigCard() {
  const { t } = useTranslation();
  const shopify = useAppBridge();
  const fetcher = useFetcher<LoaderData>();
  const { aclasPosConfig } = useLoaderData<LoaderData>();

  const [formData, setFormData] = useState<AclasPosConfig>({
    merchantNo: aclasPosConfig?.merchantNo || "",
    appId: aclasPosConfig?.appId || "",
    appSecret: aclasPosConfig?.appSecret || "",
    isActive: aclasPosConfig?.isActive || false,
    id: aclasPosConfig?.id,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<keyof AclasPosConfig, boolean>>({
    merchantNo: false,
    appId: false,
    appSecret: false,
    isActive: false,
    id: false,
  });

  // Validate form data with Zod
  const validateForm = useCallback(
    (data: Partial<AclasPosConfig>) => {
      const result = aclasPosConfigSchema.safeParse(data);

      if (!result.success) {
        const formattedErrors = result.error.format();
        const fieldErrors: FormErrors = {};

        if (formattedErrors.merchantNo?._errors?.[0]) {
          fieldErrors.merchantNo = t(
            `settings.aclasPosConfig.validation.${formattedErrors.merchantNo._errors[0]}`,
          );
        }
        if (formattedErrors.appId?._errors?.[0]) {
          fieldErrors.appId = t(
            `settings.aclasPosConfig.validation.${formattedErrors.appId._errors[0]}`,
          );
        }
        if (formattedErrors.appSecret?._errors?.[0]) {
          fieldErrors.appSecret = t(
            `settings.aclasPosConfig.validation.${formattedErrors.appSecret._errors[0]}`,
          );
        }

        return { isValid: false, errors: fieldErrors };
      }

      return { isValid: true, errors: {} };
    },
    [t],
  );

  // Handle form field changes
  const handleChange = useCallback(
    (field: keyof AclasPosConfig) => (value: string | boolean) => {
      const newData = { ...formData, [field]: value };
      setFormData(newData);

      // Only validate touched fields
      if (touched[field]) {
        const { errors } = validateForm(newData);
        setErrors((prev) => ({ ...prev, [field]: errors[field as keyof typeof errors] }));
      }
    },
    [formData, touched, validateForm],
  );

  // Handle field blur
  const handleBlur = useCallback(
    (field: keyof AclasPosConfig) => {
      setTouched((prev) => ({ ...prev, [field]: true }));
      const { errors } = validateForm(formData);
      setErrors((prev) => ({ ...prev, [field]: errors[field as keyof typeof errors] }));
    },
    [formData, validateForm],
  );

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    const { isValid, errors } = validateForm(formData);
    setErrors(errors);

    if (!isValid) {
      // Mark all fields as touched to show errors
      setTouched({
        merchantNo: true,
        appId: true,
        appSecret: true,
        isActive: true,
        id: !!formData.id,
      });
      return;
    }

    try {
      const formDataToSubmit = new FormData();
      formDataToSubmit.append("action", "updateAclasPosConfig");
      formDataToSubmit.append("merchantNo", formData.merchantNo.trim());
      formDataToSubmit.append("appId", formData.appId.trim());
      formDataToSubmit.append("appSecret", formData.appSecret.trim());
      formDataToSubmit.append("isActive", String(formData.isActive));

      await fetcher.submit(formDataToSubmit, {
        method: "POST",
      });

      shopify.toast.show(t("settings.aclasPosConfig.saveSuccess"));
    } catch (error) {
      console.error("Error saving configuration:", error);
      const errorMessage =
        error instanceof Error ? error.message : t("settings.aclasPosConfig.saveError");
      shopify.toast.show(errorMessage, { isError: true });
    }
  }, [formData, shopify, t, fetcher, validateForm]);

  const handleToggle = useCallback(
    (newActive: boolean) => {
      handleChange("isActive")(newActive);
    },
    [handleChange],
  );

  const loading = fetcher.state === "submitting" || fetcher.state === "loading";

  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack align="space-between">
          <Text variant="headingMd" as="h2">
            {t("settings.aclasPosConfig.title")}
          </Text>

          <Button
            variant="secondary"
            onClick={() => handleToggle(!formData.isActive)}
            icon={formData.isActive ? ToggleOnIcon : ToggleOffIcon}
          >
            {formData.isActive
              ? t("settings.aclasPosConfig.activate")
              : t("settings.aclasPosConfig.deactivate")}
          </Button>
        </InlineStack>
        <Divider />

        <Form onSubmit={handleSubmit}>
          <FormLayout>
            <TextField
              label={t("settings.aclasPosConfig.merchantNo")}
              value={formData.merchantNo}
              onChange={(value) => handleChange("merchantNo")(value)}
              onBlur={() => handleBlur("merchantNo")}
              error={errors.merchantNo}
              autoComplete="off"
              disabled={loading || !formData.isActive}
            />
            <TextField
              label={t("settings.aclasPosConfig.appId")}
              value={formData.appId}
              onChange={(value) => handleChange("appId")(value)}
              onBlur={() => handleBlur("appId")}
              error={errors.appId}
              autoComplete="off"
              disabled={loading || !formData.isActive}
            />
            <TextField
              label={t("settings.aclasPosConfig.appSecret")}
              value={formData.appSecret}
              onChange={(value) => handleChange("appSecret")(value)}
              onBlur={() => handleBlur("appSecret")}
              error={errors.appSecret}
              type="password"
              autoComplete="off"
              helpText={t("settings.aclasPosConfig.appSecretHelpText")}
              disabled={loading || !formData.isActive}
            />

            <Box paddingBlockStart="400">
              <InlineStack align="end">
                <Button submit variant="primary" loading={loading} disabled={loading}>
                  {t("settings.aclasPosConfig.saveConfig")}
                </Button>
              </InlineStack>
            </Box>
          </FormLayout>
        </Form>
      </BlockStack>
    </Card>
  );
}
