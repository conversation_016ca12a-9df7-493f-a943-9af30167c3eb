import { useFetcher } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { Layout, Page } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { action } from "./action";
import AclasPosConfigCard from "./components/AclasPosConfigCard";
import APIEndpointCard from "./components/APIEndpointCard";
import ApiKeyCard from "./components/ApiKeyCard";
import LanguageCard from "./components/LanguageCard";
import { ErrorBoundary } from "./error-boundary";
import { loader } from "./loader";

export { action, ErrorBoundary, loader };

export default function SettingsPage() {
  const fetcher = useFetcher<typeof action>();
  const { t } = useTranslation();

  return (
    <Page>
      <TitleBar title={t("settings.title")} />
      <Layout>
        <Layout.Section>
          <LanguageCard fetcher={fetcher} />
        </Layout.Section>

        <Layout.Section>
          <AclasPosConfigCard />
        </Layout.Section>

        <Layout.Section>
          <ApiKeyCard fetcher={fetcher} />
        </Layout.Section>

        <Layout.Section>
          <APIEndpointCard />
        </Layout.Section>
      </Layout>
    </Page>
  );
}
