import { CustomerMetafield, gid<PERSON>refix, MetafieldType, OrderMetafield } from "@/constants";

const getMetafields = async (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  admin: any,
  query: string,
  ownerType: "ORDER" | "CUSTOMER" | "DRAFTORDER",
) => {
  const response = await admin.graphql(
    `
    #graphql
    query metafields($query: String!, $ownerType: MetafieldOwnerType!) {
      metafieldDefinitions(first: 250, ownerType: $ownerType, query:$query) {
        nodes {
          id
          namespace
          key
          name
        }
      }
    }
  `,
    {
      variables: {
        query,
        ownerType,
      },
    },
  );

  const responseJson = await response.json();

  return responseJson.data.metafieldDefinitions.nodes;
};

const createMetafieldDefinition = async (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  admin: any,
  definition: {
    name: string;
    key: string;
    namespace: string;
    type: string;
    description: string;
    ownerType: string;
  },
) => {
  const definitionData = {
    name: definition.name,
    key: definition.key,
    // namespace: definition.namespace,
    type: definition.type,
    description: definition.description,
    ownerType: definition.ownerType,
    access: {
      admin: "MERCHANT_READ",
      storefront: "PUBLIC_READ",
      customerAccount: "READ",
    },
    pin: true,
  };

  const response = await admin.graphql(
    `
    #graphql
    mutation CreateCustomerMetafieldDefinition($definition: MetafieldDefinitionInput!) {
      metafieldDefinitionCreate(definition: $definition) {
        createdDefinition {
          id
          namespace
          key
          ownerType
          name
        }
        userErrors {
          field
          message
        }
      }
    }
  `,
    {
      variables: {
        definition: definitionData,
      },
    },
  );

  const responseJson = await response.json();

  return responseJson.data.metafieldDefinitionCreate;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const initMetafields = async (admin: any) => {
  const appResponse = await admin.graphql(
    `
    #graphql
    query getApp {
      app {
        id
      }
    }
  `,
  );

  const appResponseJson = await appResponse.json();

  const appId = appResponseJson.data?.app?.id?.replace(gidPrefix.APP, "");
  const query = `namespace:app--${appId}*`;

  const draftOrdersMetafields = await getMetafields(admin, query, "DRAFTORDER");
  const DRAFTORDER_METAFIELDS = [
    {
      name: "Location ID",
      namespace: "store",
      key: OrderMetafield.LOCATION_ID,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "Location ID of the POS machine",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Staff ID",
      namespace: "staff",
      key: OrderMetafield.STAFF_ID,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "ID of staff who processed the order",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Order Date",
      namespace: "order",
      key: OrderMetafield.ORDER_DATE,
      type: MetafieldType.DATE_TIME,
      description: "Exact date and time when the order was processed",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Order ID",
      namespace: "order",
      key: OrderMetafield.ORDER_ID,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "ID of the order processed",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Invoice",
      namespace: "order",
      key: OrderMetafield.INVOICE,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "Invoice ID or reference",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Total Order Amount",
      namespace: "order",
      key: OrderMetafield.TOTAL_ORDER_AMOUNT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "Total order amount captured by POS machine",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Actual Order Amount",
      namespace: "calc",
      key: OrderMetafield.ACTUAL_ORDER_AMOUNT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "Actual order amount",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Discount Amount",
      namespace: "calc",
      key: OrderMetafield.DISCOUNT_AMOUNT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "Discount amount calculated by POS machine",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Redeem Points",
      namespace: "calc",
      key: OrderMetafield.REDEEM_POINTS,
      type: MetafieldType.NUMBER_INTEGER,
      description: "Redeem points for order amount calculated",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Reward Points",
      namespace: "calc",
      key: OrderMetafield.REWARD_POINTS,
      type: MetafieldType.NUMBER_INTEGER,
      description: "Reward points for order amount calculated",
      ownerType: "DRAFTORDER",
    },
  ];
  const missingDraftOrdersMetafields = DRAFTORDER_METAFIELDS.filter((metafield) => {
    const existingMetafield = draftOrdersMetafields.find(
      (metafieldNode: { key: string }) => metafieldNode.key === metafield.key,
    );
    return !existingMetafield;
  });

  if (missingDraftOrdersMetafields.length > 0) {
    missingDraftOrdersMetafields.forEach(async (metafield) => {
      await createMetafieldDefinition(admin, {
        description: metafield.description,
        key: metafield.key,
        name: metafield.name,
        namespace: `$app:${metafield.namespace}`,
        ownerType: "DRAFTORDER",
        type: metafield.type,
      });
    });
  }

  const ordersMetafields = await getMetafields(admin, query, "ORDER");
  const ORDER_METAFIELDS = [
    {
      name: "Location ID",
      namespace: "store",
      key: OrderMetafield.LOCATION_ID,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "Location ID of the POS machine",
      ownerType: "ORDER",
    },
    {
      name: "Staff ID",
      namespace: "staff",
      key: OrderMetafield.STAFF_ID,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "ID of staff who processed the order",
      ownerType: "ORDER",
    },
    {
      name: "Order Date",
      namespace: "order",
      key: OrderMetafield.ORDER_DATE,
      type: MetafieldType.DATE_TIME,
      description: "Exact date and time when the order was processed",
      ownerType: "ORDER",
    },
    {
      name: "Order ID",
      namespace: "order",
      key: OrderMetafield.ORDER_ID,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "ID of the order processed",
      ownerType: "ORDER",
    },
    {
      name: "Invoice",
      namespace: "order",
      key: OrderMetafield.INVOICE,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "Invoice ID or reference",
      ownerType: "ORDER",
    },
    {
      name: "Total Order Amount",
      namespace: "order",
      key: OrderMetafield.TOTAL_ORDER_AMOUNT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "Total order amount captured by POS machine",
      ownerType: "ORDER",
    },
    {
      name: "Actual Order Amount",
      namespace: "calc",
      key: OrderMetafield.ACTUAL_ORDER_AMOUNT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "Actual order amount",
      ownerType: "ORDER",
    },
    {
      name: "Discount Amount",
      namespace: "calc",
      key: OrderMetafield.DISCOUNT_AMOUNT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "Discount amount calculated by POS machine",
      ownerType: "ORDER",
    },
    {
      name: "Redeem Points",
      namespace: "calc",
      key: OrderMetafield.REDEEM_POINTS,
      type: MetafieldType.NUMBER_INTEGER,
      description: "Redeem points for order amount calculated",
      ownerType: "ORDER",
    },
    {
      name: "Reward Points",
      namespace: "calc",
      key: OrderMetafield.REWARD_POINTS,
      type: MetafieldType.NUMBER_INTEGER,
      description: "Reward points for order amount calculated",
      ownerType: "ORDER",
    },
  ];

  const missingOrdersMetafields = ORDER_METAFIELDS.filter((metafield) => {
    const existingMetafield = ordersMetafields.find(
      (metafieldNode: { key: string }) => metafieldNode.key === metafield.key,
    );
    return !existingMetafield;
  });

  if (missingOrdersMetafields.length > 0) {
    missingOrdersMetafields.forEach(async (metafield) => {
      await createMetafieldDefinition(admin, {
        description: metafield.description,
        key: metafield.key,
        name: metafield.name,
        namespace: `$app:${metafield.namespace}`,
        ownerType: "ORDER",
        type: metafield.type,
      });
    });
  }

  const customersMetafields = await getMetafields(admin, query, "CUSTOMER");
  const CUSTOMER_METAFIELDS = [
    {
      name: "Birth Date",
      namespace: "customer",
      key: CustomerMetafield.BIRTH_DATE,
      type: MetafieldType.DATE,
      description: "The customer's date of birth",
      ownerType: "CUSTOMER",
    },
    {
      name: "Gender",
      namespace: "customer",
      key: CustomerMetafield.GENDER,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "The customer's gender",
      ownerType: "CUSTOMER",
    },
    {
      name: "Register Date",
      namespace: "customer",
      key: CustomerMetafield.REGISTER_DATE,
      type: MetafieldType.DATE_TIME,
      description: "The date the customer registered",
      ownerType: "CUSTOMER",
    },
    {
      name: "Register Employee",
      namespace: "customer",
      key: CustomerMetafield.REGISTER_EMPLOYEE,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "The employee who registered the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Register Location",
      namespace: "customer",
      key: CustomerMetafield.REGISTER_LOCATION,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "The location where the customer was registered",
      ownerType: "CUSTOMER",
    },
    {
      name: "Status",
      namespace: "customer",
      key: CustomerMetafield.STATUS,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "The status of the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Points",
      namespace: "customer",
      key: CustomerMetafield.POINTS,
      type: MetafieldType.NUMBER_INTEGER,
      description: "The points of the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Store Credit",
      namespace: "customer",
      key: CustomerMetafield.STORE_CREDIT,
      type: MetafieldType.NUMBER_DECIMAL,
      description: "The store credit of the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Vip Tier",
      namespace: "customer",
      key: CustomerMetafield.VIP_TIER,
      type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
      description: "The VIP tier of the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Is Completed Profile",
      namespace: "customer",
      key: CustomerMetafield.IS_COMPLETED_PROFILE,
      type: MetafieldType.BOOLEAN,
      description: "The flag completed profile",
      ownerType: "CUSTOMER",
    },
    {
      name: "Total points",
      namespace: "customer",
      key: CustomerMetafield.TOTAL_POINTS,
      type: MetafieldType.NUMBER_INTEGER,
      description: "The total points of customer",
      ownerType: "CUSTOMER",
    },
  ];

  const missingCustomerMetafields = CUSTOMER_METAFIELDS.filter(
    (customerMetafield) =>
      !customersMetafields.some(
        (metafield: { key: string }) => metafield.key === customerMetafield.key,
      ),
  );

  if (missingCustomerMetafields.length > 0) {
    missingCustomerMetafields.forEach(async (metafield) => {
      await createMetafieldDefinition(admin, {
        description: metafield.description,
        key: metafield.key,
        name: metafield.name,
        namespace: `$app:${metafield.namespace}`,
        ownerType: "CUSTOMER",
        type: metafield.type,
      });
    });
  }
};
