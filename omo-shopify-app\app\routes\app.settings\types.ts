import { FetcherWithComponents } from "@remix-run/react";

export interface LoaderData {
  apiKey: string | null;
  id?: number;
  shopId?: string;
  shopName?: string;
  myshopifyDomain?: string;
  shopToken?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  hostname?: string;
}

export interface ActionData {
  apiKey?: string;
  language?: string;
}

export type FetcherType = FetcherWithComponents<any>;
