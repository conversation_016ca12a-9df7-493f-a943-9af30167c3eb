import type { ActionFunctionArgs } from "@remix-run/node";
import Jo<PERSON> from "joi";
import logger from "../logger.server";
import { getQueue } from "../queue.server";

// Joi schema for ACLAS transaction validation
const aclasTransactionSchema = Joi.object({
  requestId: Joi.string().required(),
  merchantNo: Joi.string().required(),
  storeNo: Joi.string().required(),
  posNo: Joi.string().required(),
  deviceId: Joi.string().required(),
  summary: Joi.object({
    orderNo: Joi.string().required(),
    transTime: Joi.string().required(),
    storeNo: Joi.string().required(),
    posNo: Joi.string().required(),
    discountRate: Joi.number().required(),
    discountAmount: Joi.number().required(),
    totalAmountExTax: Joi.number().required(),
    totalAmountIncTax: Joi.number().required(),
    orderType: Joi.number().integer().required(),
    totalTaxAmount: Joi.number().required(),
    roundingAmount: Joi.number().required(),
    receivableAmount: Joi.number().required(),
    receivedAmount: Joi.number().required(),
    change: Joi.number().required(),
    memberNo: Joi.string().optional().allow(""),
    invoiceNo: Joi.string().optional().allow(""),
    invoiceMemo: Joi.string().optional().allow(""),
    type: Joi.number().integer().required(),
    note: Joi.string().optional().allow(""),
    orderSn: Joi.number().integer().required(),
    originalOrderNo: Joi.string().optional().allow(""),
    staffNo: Joi.string().required(),
    staffName: Joi.string().optional().allow(""),
    ceateTime: Joi.string().required(),
  }).required(),
  items: Joi.array()
    .items(
      Joi.object({
        itemNo: Joi.number().integer().required(),
        barcode: Joi.string().required(),
        productCode: Joi.string().required(),
        productName: Joi.string().required(),
        categoryNo: Joi.string().required(),
        unit: Joi.string().optional().allow(""),
        discountRate: Joi.number().required(),
        discountAmount: Joi.number().required(),
        qty: Joi.number().required(),
        price: Joi.number().required(),
        amount: Joi.number().required(),
        totalAmountExTax: Joi.number().required(),
        totalAmountIncTax: Joi.number().required(),
        taxAmount: Joi.number().required(),
        saleTaxRate: Joi.number().required(),
        saleTaxRate1: Joi.number().required(),
        saleTaxRate2: Joi.number().required(),
        saleTaxRate3: Joi.number().required(),
        saleTaxRate4: Joi.number().required(),
        saleTax1: Joi.number().required(),
        saleTax2: Joi.number().required(),
        saleTax3: Joi.number().required(),
        saleTax4: Joi.number().required(),
        saleTaxRateName1: Joi.string().optional().allow(""),
        saleTaxRateName2: Joi.string().optional().allow(""),
        saleTaxRateName3: Joi.string().optional().allow(""),
        saleTaxRateName4: Joi.string().optional().allow(""),
        note: Joi.string().optional().allow("", null),
      }),
    )
    .required(),
  payments: Joi.array()
    .items(
      Joi.object({
        itemNo: Joi.number().integer().required(),
        paymentMethodNo: Joi.string().required(),
        paymentMethodName: Joi.string().required(),
        receivableAmount: Joi.number().required(),
        receivedAmount: Joi.number().required(),
        couponNo: Joi.string().optional().allow("", null),
        memberNo: Joi.string().optional().allow("", null),
        note: Joi.string().optional().allow(""),
      }),
    )
    .required(),
});

export async function action({ request }: ActionFunctionArgs) {
  try {
    // Only handle POST requests
    if (request.method !== "POST") {
      return Response.json(
        {
          code: "ERROR",
          message: "Method not allowed",
        },
        { status: 405 },
      );
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      logger.error("Failed to parse request body as JSON", error);
      return Response.json(
        {
          code: "ERROR",
          message: "Failed to parse request body as JSON",
        },
        { status: 400 },
      );
    }

    // Validate request body against schema
    const { error, value } = aclasTransactionSchema.validate(body);
    if (error) {
      logger.error("ACLAS transaction validation failed", error.details);
      return Response.json(
        {
          code: "ERROR",
          message: `Validation error: ${error.details[0].message}`,
        },
        { status: 400 },
      );
    }

    // Send to queue
    try {
      const queue = getQueue("aclas/transactions");
      await queue.add("aclas-transaction", value, {
        jobId: value.requestId,
      });

      logger.info(`ACLAS transaction queued successfully - RequestID: ${value.requestId}`);

      return Response.json(
        {
          code: "SUCCESS",
          message: "Transaction queued successfully",
        },
        { status: 200 },
      );
    } catch (queueError) {
      logger.error("Failed to add transaction to queue", queueError);
      return Response.json(
        {
          code: "ERROR",
          message: "Internal server error",
        },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.error("Unexpected error in ACLAS transaction webhook", error);
    return Response.json(
      {
        code: "ERROR",
        message: "Internal server error",
      },
      { status: 500 },
    );
  }
}
