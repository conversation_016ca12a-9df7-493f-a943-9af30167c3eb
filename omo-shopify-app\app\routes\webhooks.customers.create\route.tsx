import logger from "@/logger.server";
import { getQueue } from "@/queue.server";
import { authenticate } from "@/shopify.server";
import { ActionFunctionArgs } from "@remix-run/node";
import type { AdminApiContext } from "@shopify/shopify-app-remix/server";

// Extend the admin context to include the graphql method
type AdminContextWithGraphQL = {
  graphql: AdminApiContext["graphql"];
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin, session } = await authenticate.webhook(request);
  logger.info(`Received ${topic} webhook for ${shop}`);

  /**
   * Edit plan:
   * - Add customer timeline queue
   * - Add to queue "customer/create"
   */

  const queue = getQueue("customers/create");
  const jobId = `${shop} - ${payload.admin_graphql_api_id}`;

  await queue.add(
    `Customer ${payload.admin_graphql_api_id} created on ${shop}`,
    {
      ...payload,
      shop,
      accessToken: session?.accessToken,
    },
    {
      jobId,
    },
  );

  return new Response();
};
