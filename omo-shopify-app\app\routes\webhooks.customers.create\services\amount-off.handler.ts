import { DiscountCodePrefix } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { generateRandomCode } from "@/utils/helper";
import { CustomerTimelineType, DiscountType, RequirementType } from "@prisma/client";
import dayjs from "dayjs";
import { RewardHandler } from "../types";

export const amountOffHandler: RewardHandler = {
  handleReward: async ({ admin, ownerId, reward, shopId }) => {
    const discountCode = DiscountCodePrefix.AMOUNT_OFF + generateRandomCode();

    const {
      discountType,
      value,
      minimumRequirement,
      minimumValue,
      title,
      productDiscounts,
      orderDiscounts,
      shippingDiscounts,
    } = reward;

    const basicCodeDiscount = {
      code: discountCode,
      title,
      customerSelection: {
        customers: {
          add: [ownerId],
        },
      },
      customerGets: {
        value:
          discountType === DiscountType.PERCENTAGE
            ? { percentage: Number((Number(value) / 100).toFixed(2)) }
            : { discountAmount: { amount: Number(value) } },
        items: { all: true },
      },
      minimumRequirement:
        minimumRequirement === RequirementType.AMOUNT
          ? { subtotal: { greaterThanOrEqualToSubtotal: Number(minimumValue) } }
          : minimumRequirement === RequirementType.QUANTITY
            ? { quantity: { greaterThanOrEqualToQuantity: Number(minimumValue) } }
            : {},
      combinesWith: {
        productDiscounts: productDiscounts ?? false,
        orderDiscounts: orderDiscounts ?? false,
        shippingDiscounts: shippingDiscounts ?? false,
      },
      usageLimit: 1,
      appliesOncePerCustomer: true,
      startsAt: dayjs().toISOString(),
    };

    await admin
      .graphql(
        `#graphql
        mutation CreateDiscountCode($basicCodeDiscount: DiscountCodeBasicInput!) {
          discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
            codeDiscountNode {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
        `,
        {
          variables: {
            basicCodeDiscount,
          },
        },
      )
      .then(async () => {
        logger.info(`Successfully created discount code for customer ${ownerId}: ${discountCode}`);
        await addCustomerTimeline({
          shopId,
          customerId: ownerId,
          message: `<p>Discount code <strong>${discountCode}</strong> has been created by signup</p>`,
          type: CustomerTimelineType.DISCOUNT,
        });
      })
      .catch((error: unknown) => {
        logger.error("Error creating discount code: ", error);
      });
  },
};
