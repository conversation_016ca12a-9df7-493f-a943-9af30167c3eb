import { DiscountCodePrefix } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { generateRandomCode } from "@/utils/helper";
import { CustomerTimelineType } from "@prisma/client";
import dayjs from "dayjs";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../types";

export const freeShippingHandler: RewardHandler = {
  handleReward: async ({ admin, ownerId, reward, shopId }) => {
    const discountCode = DiscountCodePrefix.FREE_SHIP + generateRandomCode();

    const { productDiscounts, orderDiscounts, title, minimumRequirement, minimumValue } = reward;

    const minimumRequirementInput =
      minimumRequirement === "QUANTITY"
        ? { quantity: { greaterThanOrEqualToQuantity: minimumValue?.toString() } }
        : minimumRequirement === "AMOUNT"
          ? { subtotal: { greaterThanOrEqualToSubtotal: minimumValue?.toString() } }
          : null;

    const input = {
      code: discountCode,
      title,
      appliesOncePerCustomer: true,
      usageLimit: 1,
      customerSelection: {
        customers: {
          add: [ownerId],
        },
      },
      startsAt: dayjs().toISOString(),
      combinesWith: {
        productDiscounts: productDiscounts ?? false,
        orderDiscounts: orderDiscounts ?? false,
      },
      minimumRequirement: minimumRequirementInput,
    };

    await admin
      .graphql(
        `#graphql
      mutation CreateFreeShippingCode($input: DiscountCodeFreeShippingInput!) {
        discountCodeFreeShippingCreate(freeShippingCodeDiscount: $input) {
          codeDiscountNode {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
      `,
        {
          variables: {
            input,
          },
        },
      )
      .then(async () => {
        logger.info(
          `Successfully created free shipping discount code for customer ${ownerId}: ${discountCode}`,
        );

        await addCustomerTimeline({
          shopId,
          customerId: ownerId,
          message: `<p>Discount code <strong>${discountCode}</strong> has been created by signup</p>`,
          type: CustomerTimelineType.DISCOUNT,
        }).then(() => {
          logger.info(
            `Successfully added free shipping discount code to customer timeline for customer ${ownerId}: ${discountCode}`,
          );
        });
      })
      .catch((error: unknown) => {
        logger.error("Error creating free shipping discount code: ", error);
      });
  },
};
