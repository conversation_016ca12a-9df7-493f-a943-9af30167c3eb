import { RewardHandler } from "../types";
import { amountOffHandler } from "./amount-off.handler";
import { freeShippingHandler } from "./free-shipping.handler";
import { pointsHandler } from "./points.handler";
import { storeCreditHandler } from "./store-credit.handler";

export const rewardHandlers: Record<string, RewardHandler> = {
  ["POINTS"]: pointsHandler,
  ["STORE_CREDIT"]: storeCreditHandler,
  ["AMOUNT_OFF"]: amountOffHandler,
  ["FREE_SHIPPING"]: freeShippingHandler,
};
