import { CustomerMetafield, MetafieldType } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { CustomerTimelineType } from "@prisma/client";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../types";

export const pointsHandler: RewardHandler = {
  handleReward: async ({ admin, ownerId, reward, shopId }) => {
    await admin
      .graphql(
        `#graphql
      mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            key
            namespace
            value
            createdAt
            updatedAt
          }
          userErrors {
            field
            message
            code
          }
        }
      }
      `,
        {
          variables: {
            metafields: [
              {
                key: CustomerMetafield.POINTS,
                namespace: "$app",
                ownerId,
                type: MetafieldType.NUMBER_INTEGER,
                value: reward.value.toString(),
              },
            ],
          },
        },
      )
      .then(async () => {
        logger.info(
          `Successfully created points metafield for customer ${ownerId}: ${reward.value}`,
        );

        await addCustomerTimeline({
          shopId,
          customerId: ownerId,
          message: `<p>This customer earned <strong>${reward.value}</strong> points by signup</p>`,
          type: CustomerTimelineType.POINTS,
        });
      })
      .catch((error: unknown) => {
        logger.error("Error creating points metafield: ", error);
      });
  },
};
