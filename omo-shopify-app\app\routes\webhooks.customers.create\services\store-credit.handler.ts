import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { CustomerTimelineType } from "@prisma/client";
import { <PERSON>wardHandler } from "../types";

export const storeCreditHandler: RewardHandler = {
  handleReward: async ({ admin, ownerId, reward, shopId }) => {
    await admin
      .graphql(
        `#graphql
      mutation storeCreditAccountCredit($id: ID!, $creditInput: StoreCreditAccountCreditInput!) {
        storeCreditAccountCredit(id: $id, creditInput: $creditInput) {
          storeCreditAccountTransaction {
            amount {
              amount
              currencyCode
            }
            account {
              id
              balance {
                amount
                currencyCode
              }
            }
          }
          userErrors {
            message
            field
          }
        }
      }
      `,
        {
          variables: {
            id: ownerId,
            creditInput: {
              creditAmount: {
                amount: reward.value.toString(),
                currencyCode: "TWD",
              },
            },
          },
        },
      )
      .then(async () => {
        logger.info(`Successfully created store credit for customer ${ownerId}: ${reward.value}`);
        await addCustomerTimeline({
          shopId,
          customerId: ownerId,
          message: `<p>This customer get <strong>${reward.value} TWD</strong> store credit by signup</p>`,
          type: CustomerTimelineType.OTHER,
        });
      })
      .catch((error: unknown) => {
        logger.error("Error creating store credit : ", error);
      });
  },
};
