import { SignupReward } from "@prisma/client";
import type { AdminApiContext } from "@shopify/shopify-app-remix/server";

type AdminClient = {
  graphql: AdminApiContext["graphql"];
  rest?: AdminApiContext["rest"];
};

export interface RewardHandler {
  handleReward: (params: {
    admin: AdminClient;
    ownerId: string;
    reward: SignupReward;
    shopId: number;
  }) => Promise<void>;
}

export interface WebhookPayload {
  admin: AdminClient;
  shop: string;
  session: { id: string; shop: string; accessToken: string };
  payload: {
    admin_graphql_api_id: string;
    [key: string]: unknown;
  };
}
