import { CustomerMetafield } from "@/constants";
import db from "@/db.server";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { CustomerTimelineType, RewardType } from "@prisma/client";
import type { ActionFunctionArgs } from "@remix-run/node";
import { AMOUNT_ENTRY_METHOD, POINT_ENTRY_METHOD } from "../app.loyalties.vip/constants";
import { getVipSettings } from "../app.loyalties.vip/services";
import { getMetafields, updateCustomerTierMetafield } from "../webhooks.refunds.create/services";
import { createCodeDiscount, getCustomer, updateCustomerCompleteMetafield } from "./services";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin, session } = await authenticate.webhook(request);
  logger.info(`Received ${topic} webhook for ${shop}`);

  const localShop = await findShop(admin, session);

  const completeProfileSettings = await db.completeProfileSettings.findFirst({
    where: { shopId: localShop.id },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });

  const rewardPoint = Number(
    completeProfileSettings?.rewards?.find((item) => item.type === RewardType.POINTS)?.value ?? 0,
  );
  const rewardDiscountSetting = completeProfileSettings?.rewards?.find(
    (item) => item.type === RewardType.AMOUNT_OFF,
  );

  if (admin && payload?.admin_graphql_api_id) {
    // Get metafields
    const customer = await getCustomer(admin, payload?.admin_graphql_api_id);
    const customerMetafields = await getMetafields(admin, customer?.metafields?.edges);
    const customerPoints = Number(
      customerMetafields?.find((m) => m.key === CustomerMetafield.POINTS)?.value ?? 0,
    );
    const totalCustomerPoints = Number(
      customerMetafields?.find((m) => m.key === CustomerMetafield.TOTAL_POINTS)?.value ?? 0,
    );
    let newTotalPoint = totalCustomerPoints;
    if (rewardPoint > 0 || rewardDiscountSetting) {
      const gender = customerMetafields?.find((m) => m.key === CustomerMetafield.GENDER)?.value;
      const birthday = customerMetafields?.find(
        (m) => m.key === CustomerMetafield.BIRTH_DATE,
      )?.value;
      const isCompletedProfile =
        customerMetafields?.find((m) => m.key === CustomerMetafield.IS_COMPLETED_PROFILE)?.value ??
        "false";

      //update Customer Point & isCompletedProfile
      if (gender && birthday && isCompletedProfile !== "true") {
        let newPoint = customerPoints;
        if (rewardPoint > 0) {
          newPoint += rewardPoint;
          newTotalPoint += rewardPoint;
        }
        if (rewardDiscountSetting) {
          await createCodeDiscount(
            admin,
            rewardDiscountSetting,
            payload?.admin_graphql_api_id,
            localShop,
          );

          await addCustomerTimeline({
            shopId: localShop.id,
            customerId: payload?.customer?.admin_graphql_api_id,
            message: `<p>This customer earned <strong>${rewardPoint}</strong> points by updating their profile <a href="shopify://admin/customers/${payload?.id}" target="_top">${payload?.id}</a></p>`,
            type: CustomerTimelineType.POINTS,
          });
        }
        await updateCustomerCompleteMetafield(
          admin,
          customer.id,
          String(newPoint),
          String(newTotalPoint),
        );

        await addCustomerTimeline({
          shopId: localShop.id,
          customerId: payload?.customer?.admin_graphql_api_id,
          message: `<p>This customer earned <strong>${rewardPoint}</strong> points by updating their profile <a href="shopify://admin/customers/${payload?.id}" target="_top">${payload?.id}</a></p>`,
          type: CustomerTimelineType.POINTS,
        });
      }
    }

    // Update Customer Tier
    const settings = await getVipSettings(shop);
    const vipTiers = settings?.vipTiers;
    const amountSpent = Number(customer?.amountSpent?.amount ?? 0);
    const vipTier = customerMetafields?.find((m) => m.key === CustomerMetafield.VIP_TIER)?.value;

    let newTier = null;
    if (vipTiers && vipTiers.length > 0) {
      for (let i = 0; i < vipTiers.length; i++) {
        const spendRequirement = vipTiers[i]?.spendRequirement ?? null;
        const pointsRequirement = vipTiers[i]?.pointsRequirement ?? null;
        if (
          settings?.entryMethod === AMOUNT_ENTRY_METHOD &&
          spendRequirement !== null &&
          amountSpent >= spendRequirement
        ) {
          newTier = vipTiers[i].name;
        } else if (
          settings?.entryMethod === POINT_ENTRY_METHOD &&
          pointsRequirement !== null &&
          newTotalPoint >= pointsRequirement
        ) {
          newTier = vipTiers[i].name;
        }
      }
    }

    if (newTier && vipTier !== newTier) {
      await updateCustomerTierMetafield(admin, customer.id, newTier);

      await addCustomerTimeline({
        shopId: localShop.id,
        customerId: customer.id,
        message: `<p>Update tier to <strong>${newTier}</strong></p>`,
        type: CustomerTimelineType.OTHER,
      });
    }
  }
  return new Response();
};
