import { CustomerMetafield, DiscountCodePrefix, MetafieldType } from "@/constants";
import { addCustomerTimeline } from "@/services";
import { generateRandomCode } from "@/utils/helper";
import {
  CompleteProfileReward,
  CustomerTimelineType,
  DiscountType,
  MinRequirementType,
  Shop,
} from "@prisma/client";
import { DiscountValue, MinimumRequirement } from "./types";

export async function createCodeDiscount(
  admin: any,
  discountSetting: CompleteProfileReward,
  customerId: string,
  shop?: Shop,
) {
  if (!discountSetting || !customerId) return;

  let minimumRequirement: MinimumRequirement = null;

  if (discountSetting.minRequirementType === MinRequirementType.MIN_PURCHASE_AMOUNT) {
    minimumRequirement = {
      subtotal: {
        greaterThanOrEqualToSubtotal: String(discountSetting.minRequirementValue ?? 1),
      },
    };
  } else if (discountSetting.minRequirementType === MinRequirementType.MIN_QUANTITY_ITEMS) {
    minimumRequirement = {
      quantity: {
        greaterThanOrEqualToQuantity: String(discountSetting.minRequirementValue ?? 1),
      },
    };
  }
  const value: DiscountValue =
    discountSetting.discountType === DiscountType.FIXED
      ? {
          discountAmount: {
            amount: discountSetting.value,
            appliesOnEachItem: false,
          },
        }
      : {
          percentage: Number(discountSetting.value) / 100,
        };
  const discountCode = DiscountCodePrefix.AMOUNT_OFF + generateRandomCode();

  try {
    await admin.graphql(
      `mutation discountCodeBasicCreate (
        $title: String,
        $code: String!,
        $startsAt: DateTime!,
        $minimumRequirement: DiscountMinimumRequirementInput,
        $value: DiscountCustomerGetsValueInput,
        $productDiscounts: Boolean,
        $shippingDiscounts: Boolean,
        $orderDiscounts: Boolean,
        $customerId: ID!) {
          discountCodeBasicCreate(
            basicCodeDiscount: {
              title: $title
              code: $code
              customerGets: {
                value: $value
                items: { all: true }
                appliesOnSubscription: false
                appliesOnOneTimePurchase: true
              }
              customerSelection: {
                customers: {
                  add: [
                    $customerId
                  ]
                }
              }
              startsAt: $startsAt
              minimumRequirement: $minimumRequirement
              appliesOncePerCustomer: true
              combinesWith: {
                productDiscounts: $productDiscounts,
                shippingDiscounts: $shippingDiscounts,
                orderDiscounts: $orderDiscounts
              }
            }
          ) {
            codeDiscountNode {
              id
              codeDiscount {
                __typename
                ... on DiscountCodeBasic {
                  title
                }
              }
            }
            userErrors {
              code
              field
              message
            }
          }
      }`,
      {
        variables: {
          title: discountSetting.title,
          code: discountCode,
          value: value,
          customerId: customerId,
          startsAt: new Date().toISOString(),
          minimumRequirement: minimumRequirement,
          productDiscounts: discountSetting.productDiscounts,
          shippingDiscounts: discountSetting.shippingDiscounts,
          orderDiscounts: discountSetting.orderDiscounts,
        },
      },
    );

    if (shop) {
      await addCustomerTimeline({
        shopId: shop.id,
        customerId: customerId,
        message: `<p>Discount code <strong>${discountCode}</strong> has been created for ${discountSetting.title}</p>`,
        type: CustomerTimelineType.DISCOUNT,
      });
    }
  } catch (error) {
    console.error("Error creating code discount:", error);
  }
}

export async function updateCustomerCompleteMetafield(
  admin: any,
  customerId: string,
  point: string,
  totalPoint: string,
) {
  if (!customerId || point == null || totalPoint == null) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                key: CustomerMetafield.POINTS,
                namespace: "$app",
                value: point,
                type: MetafieldType.NUMBER_INTEGER,
              },

              {
                key: CustomerMetafield.TOTAL_POINTS,
                namespace: "$app",
                value: totalPoint,
                type: MetafieldType.NUMBER_INTEGER,
              },
              {
                key: CustomerMetafield.IS_COMPLETED_PROFILE,
                value: "true",
                type: MetafieldType.BOOLEAN,
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer complete metafield:", error);
  }
}

export async function getCustomer(admin: any, customerId: string) {
  if (!customerId) return;
  const response = await admin.graphql(
    `query getCustomer($id: ID!) {
            customer(id: $id) {
                id
                metafields(first: 20, namespace: "$app") {
                    edges {
                        node {
                        namespace
                        key
                        value
                        }
                    }
                }
                amountSpent {amount currencyCode}
            }
        }`,
    { variables: { id: customerId } },
  );
  const responseJson = await response.json();
  const customer = responseJson?.data?.customer;

  return customer;
}
