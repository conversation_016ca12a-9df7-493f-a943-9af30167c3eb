import { CustomerMetafield, PointIssueType } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import findShop from "@/utils/find-shop.server";
import { CustomerTimelineType, OrderStatus } from "@prisma/client";
import type { ActionFunctionArgs } from "@remix-run/node";
import { GET_CUSTOMER_POINTS, UPSERT_CUSTOMER_POINTS } from "../graphql/memberQueries";
import { authenticate } from "../shopify.server";
import { MemberMetafield } from "../types/memberTypes";
import {
  getPointOrderSetting,
  getPointsAccoringVipSettings,
  getPointsSettings,
  saveDelayedPoints,
} from "./app.loyalties.points/services";
import { updateOrderRewardPointMetafield } from "./webhooks.refunds.create/services";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin, session } = await authenticate.webhook(request);

  logger.info(`Received ${topic} webhook for ${shop}`);

  // Get the settings for the shop
  if (!admin) {
    return new Response("Admin not found", { status: 404 });
  }

  if (!payload?.customer?.admin_graphql_api_id) {
    return new Response("Customer not found", { status: 404 });
  }

  // Fetch Settings if the program exists
  const pointSettings = await getPointsSettings(shop);
  const pointsSettings = pointSettings?.pointsSettings;

  const vipTierSetting = await getPointsAccoringVipSettings(shop);
  const vipTiersSettings = vipTierSetting?.vipTiersSettings;

  const pointOrderSettings = await getPointOrderSetting(shop);
  const issueType = pointOrderSettings?.loyaltyPoints?.pointsIssueType || PointIssueType.IMMEDIATE;
  const issueDays = pointOrderSettings?.loyaltyPoints?.issueDays || 0;

  if (admin) {
    const response = await admin.graphql(
      `query getCustomer($id: ID!) {
            customer(id: $id) {
                id
                metafields(first: 20, namespace: "$app") {
                    edges {
                        node {
                        namespace
                        key
                        value
                        }
                    }
                }
                amountSpent {amount currencyCode}
            }
        }`,
      { variables: { id: payload.customer.admin_graphql_api_id } },
    );
    const responseJson = await response.json();
    const customer = responseJson?.data?.customer;

    // Extract metafields
    const metafields = customer.metafields?.edges?.map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];

    // Find VIP tier metafield
    const vipTierMetafield = metafields.find((m) => m.key === CustomerMetafield.VIP_TIER);
    const vipTier = vipTierMetafield ? vipTierMetafield.value : null;

    const orderStatus =
      pointsSettings && pointsSettings[0]
        ? pointsSettings[0].orderStatus
        : OrderStatus.PAID_FULFILLED;
    // ────────────────────────────────────────────────────────
    // Compute how many points this order just earned
    // ────────────────────────────────────────────────────────
    let currentAmount = 1;
    let pointsPerCurrency = 1;
    if (vipTiersSettings && vipTiersSettings[0].basedOnDiffTier) {
      // If the points are based on the VIP tier, we need to find the tier of the customer
      const currentTier = vipTiersSettings.find((tier) => tier.name === vipTier);
      currentAmount = currentTier ? (currentTier.spendAmount ?? 1) : 1;
      pointsPerCurrency = currentTier ? (currentTier.pointEarn ?? 1) : 1;
    } else {
      currentAmount = pointsSettings && pointsSettings[0] ? pointsSettings[0].currencyAmount : 1;
      pointsPerCurrency =
        pointsSettings && pointsSettings[0] ? pointsSettings[0].pointsPerCurrency : 1;
    }
    const orderAmount = (Number(payload?.total_price) / currentAmount) * pointsPerCurrency;
    const earnedPoints = Math.floor(orderAmount);
    //
    const customerGid = payload.customer.admin_graphql_api_id;

    // ────────────────────────────────────────────────────────
    // Handle points issuance based on settings
    // ────────────────────────────────────────────────────────
    if (orderStatus === OrderStatus.PAID_FULFILLED) {
      if (issueType === PointIssueType.IMMEDIATE) {
        // ────────────────────────────────────────────────────────
        // Read the customer’s existing points via GraphQL
        // ────────────────────────────────────────────────────────
        const getRes = await admin.graphql(GET_CUSTOMER_POINTS, {
          variables: { customerId: customerGid },
        });
        const getJson = await getRes.json();
        const existingMf = getJson.data.customer.pointsMf;
        const currentPoints = existingMf?.value ? Number(existingMf.value) : 0;
        // Calculator Point immediate
        const newTotal = Math.floor(currentPoints + earnedPoints).toString();

        await admin.graphql(UPSERT_CUSTOMER_POINTS, {
          variables: {
            ownerId: customerGid,
            namespace: "$app",
            key: CustomerMetafield.POINTS,
            value: newTotal,
          },
        });

        const totalPoints = Number(
          metafields.find((m) => m.key === CustomerMetafield.TOTAL_POINTS)?.value ?? 0,
        );
        await admin.graphql(UPSERT_CUSTOMER_POINTS, {
          variables: {
            ownerId: customerGid,
            namespace: "$app",
            key: CustomerMetafield.TOTAL_POINTS,
            value: Math.floor(totalPoints + earnedPoints).toString(),
          },
        });

        logger.info(`Customer points updated successfully - ID: ${customerGid}`);
        const shop = await findShop(admin, session);
        await addCustomerTimeline({
          shopId: shop.id,
          customerId: customerGid,
          type: CustomerTimelineType.POINTS,
          message: `<p>Earned <strong>${earnedPoints}</strong> points for order <a href="shopify://admin/orders/${payload.id}"><span>Order ${payload.id}</span></a></p>`, // TODO: Check for order id
        });

        if (earnedPoints >= 0) {
          await updateOrderRewardPointMetafield(
            admin,
            payload.admin_graphql_api_id,
            String(earnedPoints),
          );
        }
      } else if (issueType === PointIssueType.DELAYED) {
        // Save for Cron Task
        try {
          const issueAt = new Date(Date.now() + issueDays * 24 * 60 * 60 * 1000);
          await saveDelayedPoints({
            customerGid,
            orderId: payload.id.toString(),
            points: earnedPoints,
            issueAt,
          });
        } catch (error) {
          console.error("Error saving delayed points:", error);
        }
      }
    }
  }
  return new Response();
};
