import { CustomerMetafield, DiscountCodePrefix, PointIssueType } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import findShop from "@/utils/find-shop.server";
import { CustomerTimelineType, OrderStatus } from "@prisma/client";
import type { ActionFunctionArgs } from "@remix-run/node";
import { GET_CUSTOMER_POINTS } from "../graphql/memberQueries";
import { authenticate } from "../shopify.server";
import { MemberMetafield } from "../types/memberTypes";
import {
  getPointOrderSetting,
  getPointsAccoringVipSettings,
  getPointsSettings,
  saveDelayedPoints,
} from "./app.loyalties.points/services";
import {
  getNamespaceMetafield,
  updateCustomerPointMetafield,
  updateOrderPointMetafield,
  updateOrderRewardPointMetafield,
} from "./webhooks.refunds.create/services";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin, session } = await authenticate.webhook(request);

  logger.info(`Received ${topic} webhook for ${shop}`);

  // Get the settings for the shop
  if (!admin) {
    return new Response("Admin not found", { status: 404 });
  }

  if (!payload?.customer?.admin_graphql_api_id) {
    return new Response("Customer not found", { status: 404 });
  }

  // Fetch settings if the program exists

  const pointSettings = await getPointsSettings(shop);
  const pointsSettings = pointSettings?.pointsSettings;

  const vipTierSetting = await getPointsAccoringVipSettings(shop);
  const vipTiersSettings = vipTierSetting?.vipTiersSettings;

  const pointOrderSettings = await getPointOrderSetting(shop);
  const issueType = pointOrderSettings?.loyaltyPoints?.pointsIssueType || PointIssueType.IMMEDIATE;
  const issueDays = pointOrderSettings?.loyaltyPoints?.issueDays || 0;

  const localShop = await findShop(admin, session);

  if (admin) {
    if (payload?.customer?.admin_graphql_api_id) {
      await addCustomerTimeline({
        shopId: localShop.id,
        customerId: payload?.customer?.admin_graphql_api_id,
        message: `<p>This customer placed an order <a href="${payload?.name}">${payload?.id}</a></p>`, // TODO: Add order link
        type: CustomerTimelineType.ORDER,
      });
    }

    const response = await admin.graphql(
      `query getCustomer($id: ID!) {
            customer(id: $id) {
                id
                metafields(first: 20, namespace: "$app") {
                    edges {
                        node {
                        namespace
                        key
                        value
                        }
                    }
                }
                amountSpent {amount currencyCode}
            }
        }`,
      { variables: { id: payload?.customer?.admin_graphql_api_id } },
    );
    const responseJson = await response.json();
    const customer = responseJson?.data?.customer;

    // Extract metafields
    const metafields = customer.metafields?.edges?.map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];
    const namespace = (await getNamespaceMetafield(admin)) as string;

    // Find VIP tier metafield
    const vipTierMetafield = metafields.find(
      (m) => m.namespace === namespace && m.key === CustomerMetafield.VIP_TIER,
    );
    const vipTier = vipTierMetafield ? vipTierMetafield.value : null;

    // Check Order Status for upadte Points
    const orderStatus =
      pointsSettings && pointsSettings[0]
        ? pointsSettings[0].orderStatus
        : OrderStatus.PAID_FULFILLED;
    // ────────────────────────────────────────────────────────
    // Compute how many points this order just earned
    // ────────────────────────────────────────────────────────
    let currentAmount = 1;
    let pointsPerCurrency = 1;
    if (vipTiersSettings && vipTiersSettings[0].basedOnDiffTier) {
      // If the points are based on the VIP tier, we need to find the tier of the customer
      const currentTier = vipTiersSettings.find((tier) => tier.name === vipTier);
      currentAmount = currentTier ? (currentTier.spendAmount ?? 1) : 1;
      pointsPerCurrency = currentTier ? (currentTier.pointEarn ?? 1) : 1;
    } else {
      currentAmount = pointsSettings && pointsSettings[0] ? pointsSettings[0].currencyAmount : 1;
      pointsPerCurrency =
        pointsSettings && pointsSettings[0] ? pointsSettings[0].pointsPerCurrency : 1;
    }
    const orderAmount = (Number(payload?.total_price) / currentAmount) * pointsPerCurrency;
    const earnedPoints = Math.floor(orderAmount);

    // ────────────────────────────────────────────────────────
    // Read the customer’s existing points via GraphQL
    // ────────────────────────────────────────────────────────
    const customerGid = payload.customer.admin_graphql_api_id;
    const getRes = await admin.graphql(GET_CUSTOMER_POINTS, {
      variables: { customerId: customerGid },
    });
    const getJson = await getRes.json();
    const existingMf = getJson.data.customer.pointsMf;
    const currentPoints = existingMf?.value ? Number(existingMf.value) : 0;
    let newPoint = currentPoints;
    let newTotalPoint = Number(
      metafields.find((m) => m.namespace === namespace && m.key === CustomerMetafield.TOTAL_POINTS)
        ?.value ?? 0,
    );

    if (orderStatus !== OrderStatus.PAID_FULFILLED) {
      if (issueType === PointIssueType.IMMEDIATE) {
        // Calculator Point immediate
        newPoint += earnedPoints;
        newTotalPoint += earnedPoints;
        if (earnedPoints >= 0) {
          await updateOrderRewardPointMetafield(
            admin,
            payload.admin_graphql_api_id,
            String(earnedPoints),
          );

          await addCustomerTimeline({
            shopId: localShop.id,
            customerId: payload?.customer?.admin_graphql_api_id,
            message: `<p>This customer earned <strong>${earnedPoints}</strong> points by placing an order <a href="shopify://admin/orders/${payload?.id}" target="_top">${payload?.id}</a></p>`,
            type: CustomerTimelineType.ORDER,
          });
        }
      } else if (issueType === PointIssueType.DELAYED) {
        // Save for Cron Task
        try {
          const issueAt = new Date(Date.now() + issueDays * 24 * 60 * 60 * 1000);
          await saveDelayedPoints({
            customerGid,
            orderId: payload.id.toString(),
            points: earnedPoints,
            issueAt,
          });
        } catch (error) {
          console.error("Error saving delayed points:", error);
        }
      }
    }

    //Update customer & order points & delete redeem discount
    const redeemDiscount = payload?.discount_applications?.find(function (discount: any) {
      return discount?.title?.includes(DiscountCodePrefix.REDEEM);
    });

    if (redeemDiscount && payload?.admin_graphql_api_id) {
      const match = redeemDiscount?.title?.match(new RegExp(`${DiscountCodePrefix.REDEEM}(\\d+)`));
      const redeemPoint = match ? parseInt(match[1], 10) : 0;
      newPoint = newPoint - redeemPoint;

      if (redeemPoint > 0) {
        await updateOrderPointMetafield(admin, payload.admin_graphql_api_id, String(redeemPoint));
      }
    }

    if (newPoint >= 0 && newTotalPoint >= 0) {
      await updateCustomerPointMetafield(
        admin,
        customer.id,
        String(newPoint),
        String(newTotalPoint),
      );

      await addCustomerTimeline({
        shopId: localShop.id,
        customerId: payload?.customer?.admin_graphql_api_id,
        message: `<p>This customer earned <strong>${earnedPoints}</strong> points by placing an order <a href="shopify://admin/orders/${payload?.id}" target="_top">${payload?.id}</a></p>`,
        type: CustomerTimelineType.POINTS,
      });
    }
  }
  return new Response();
};
