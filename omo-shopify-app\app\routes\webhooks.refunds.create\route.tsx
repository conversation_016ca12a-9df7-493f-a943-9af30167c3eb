import { Customer<PERSON>etafield, DelayedPointsStatus, gidPrefix, OrderMetafield } from "@/constants";
import logger from "@/logger.server";
import { addCustomerTimeline } from "@/services";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { CustomerTimelineType, RefundType } from "@prisma/client";
import type { ActionFunctionArgs } from "@remix-run/node";
import {
  deleteDelayedPoints,
  getDelayedPoints,
  getPointsSettings,
  updateDelayedPoints,
} from "../app.loyalties.points/services";
import {
  getMetafields,
  getRefund,
  updateCustomerPointMetafield,
  updateOrderRewardPointMetafield,
} from "./services";

/**
 * This webhook is used to trigger when the return is successful.
 * To return the redeem points and deduct reward points for the customer.
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin, session } = await authenticate.webhook(request);
  logger.info(`Received ${topic} webhook for ${shop}`);

  if (!admin) {
    return new Response("Admin not found", { status: 404 });
  }

  const localShop = await findShop(admin, session);

  const refundData = await getRefund(admin, payload?.admin_graphql_api_id);

  // Find customer points
  const customerMetafields = await getMetafields(
    admin,
    refundData?.order?.customer?.metafields?.edges,
  );
  const customerPoints = Number(
    customerMetafields?.find((m) => m.key === CustomerMetafield.POINTS)?.value ?? 0,
  );
  const totalCustomerPoints = Number(
    customerMetafields?.find((m) => m.key === CustomerMetafield.TOTAL_POINTS)?.value ?? 0,
  );

  // Find redeem & reward points
  const orderMetafields = await getMetafields(admin, refundData?.order?.metafields?.edges);
  const redeemPoints = Number(
    orderMetafields?.find((m) => m.key === OrderMetafield.REDEEM_POINTS)?.value ?? 0,
  );
  const orderRewardPoints = Number(
    orderMetafields?.find((m) => m.key === OrderMetafield.REWARD_POINTS)?.value ?? 0,
  );
  let rewardPoints = 0;
  const delayedPointsHistory = await getDelayedPoints(
    refundData?.order?.customer?.id,
    refundData?.order?.id?.split("/").pop(),
  );
  let delayedPoints = 0;
  if (delayedPointsHistory) {
    if (delayedPointsHistory.status === DelayedPointsStatus.ISSUED) {
      rewardPoints = delayedPointsHistory.points;
    } else {
      rewardPoints = 0;
      delayedPoints = delayedPointsHistory.points;
    }
  } else {
    rewardPoints = orderRewardPoints;
  }

  // Get refund amount
  const refundAmount = Number(refundData?.totalRefundedSet?.shopMoney?.amount ?? 0);
  const totalAmount = Number(refundData?.order?.totalPriceSet?.shopMoney?.amount ?? 0);

  // Get point setting
  const pointSettings = await getPointsSettings(shop);
  const pointsSettings = pointSettings?.pointsSettings;
  const redeemedRefundType =
    pointsSettings && pointsSettings[0]
      ? pointsSettings[0].redeemedRefundType
      : RefundType.PROPORTIONAL;
  const orderRefundType =
    pointsSettings && pointsSettings[0]
      ? pointsSettings[0].orderRefundType
      : RefundType.PROPORTIONAL;

  let redeem = 0;
  if (redeemPoints > 0 && redeemedRefundType !== RefundType.NONE) {
    if (totalAmount > 0 && refundAmount !== totalAmount) {
      redeem = (refundAmount * redeemPoints) / totalAmount;
    } else {
      redeem = redeemPoints;
    }
  }

  let reward = 0;
  let returnDelayPoints = 0;
  switch (orderRefundType) {
    case RefundType.PROPORTIONAL:
      if (delayedPoints > 0) {
        if (orderRewardPoints > 0) {
          returnDelayPoints = (refundAmount * orderRewardPoints) / totalAmount;
        } else {
          returnDelayPoints = (refundAmount * delayedPoints) / totalAmount;
          await updateOrderRewardPointMetafield(
            admin,
            `${gidPrefix.ORDER}${payload.order_id}`,
            String(delayedPoints),
          );
        }
      } else {
        reward = (refundAmount * rewardPoints) / totalAmount;
      }
      break;
    case RefundType.FULL:
      if (refundData?.order?.refunds?.length <= 1) {
        reward = rewardPoints;
        if (delayedPoints > 0) {
          returnDelayPoints = delayedPoints;
        }
      }
      break;
    default:
      reward = 0;
      if (delayedPoints > 0) {
        returnDelayPoints = 0;
      }
      break;
  }

  if (redeemedRefundType !== RefundType.NONE || orderRefundType !== RefundType.NONE) {
    if (
      delayedPointsHistory &&
      delayedPointsHistory.status !== DelayedPointsStatus.ISSUED &&
      delayedPoints > 0
    ) {
      const newDelayedPoints = delayedPoints - returnDelayPoints;
      if (newDelayedPoints === 0) {
        await deleteDelayedPoints(delayedPointsHistory.id);
      } else if (newDelayedPoints > 0 && newDelayedPoints !== delayedPoints) {
        await updateDelayedPoints(delayedPointsHistory.id, newDelayedPoints);
      }
    }

    const newPoint = Math.floor(customerPoints + redeem - reward);
    const newTotalPoint = Math.floor(totalCustomerPoints - reward);
    await updateCustomerPointMetafield(
      admin,
      refundData?.order?.customer?.id,
      String(newPoint),
      String(newTotalPoint),
    );

    await addCustomerTimeline({
      shopId: localShop.id,
      customerId: payload?.customer?.admin_graphql_api_id,
      message: `<p>This customer returned <strong>${reward}</strong> points by refunding an order <a href="shopify://admin/orders/${payload?.id}" target="_top">${payload?.id}</a></p>`,
      type: CustomerTimelineType.POINTS,
    });
  }

  return new Response();
};
