// eslint-disable-next-line import/no-unresolved
import { CustomerMetafield, gidPrefix, MetafieldType, OrderMetafield } from "@/constants";
import { MemberMetafield } from "@/types/memberTypes";

export async function updateCustomerTierMetafield(admin: any, customerId: string, newTier: string) {
  if (!newTier || !customerId) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                key: CustomerMetafield.VIP_TIER,
                value: newTier,
                type: MetafieldType.SINGLE_LINE_TEXT_FIELD,
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer tier metafield:", error);
  }
}

export async function updateCustomerPointMetafield(
  admin: any,
  customerId: string,
  point: string,
  totalPoint: string,
) {
  if (!customerId) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                key: CustomerMetafield.POINTS,
                value: point,
                type: MetafieldType.NUMBER_INTEGER,
              },
              {
                key: CustomerMetafield.TOTAL_POINTS,
                value: totalPoint,
                type: MetafieldType.NUMBER_INTEGER,
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer point metafield:", error);
  }
}

export async function updateOrderPointMetafield(admin: any, orderId: string, newPoint: string) {
  if (!newPoint) return;

  try {
    await admin.graphql(
      `
      mutation updateOrderMetafield($input: OrderInput!){
        orderUpdate(input: $input) {
          order {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          input: {
            id: orderId,
            metafields: [
              {
                key: OrderMetafield.REDEEM_POINTS,
                value: newPoint,
                type: MetafieldType.NUMBER_INTEGER,
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating order point metafield:", error);
  }
}

export async function updateOrderRewardPointMetafield(admin: any, orderId: string, point: string) {
  if (!point) return;

  try {
    await admin.graphql(
      `
      mutation updateOrderMetafield($input: OrderInput!){
        orderUpdate(input: $input) {
          order {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          input: {
            id: orderId,
            metafields: [
              {
                key: OrderMetafield.REWARD_POINTS,
                value: point,
                type: MetafieldType.NUMBER_INTEGER,
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating order reward point metafield:", error);
  }
}

export async function getNamespaceMetafield(admin: any) {
  if (!admin) return;
  const appResponse = await admin.graphql(
    `
    #graphql
    query getApp {
      app {
        id
      }
    }
  `,
  );
  const appResponseJson = await appResponse.json();
  const appId = appResponseJson.data?.app?.id?.replace(gidPrefix.APP, "");
  const namespace = `app--${appId}`;
  return namespace;
}

export async function getRefund(admin: any, refundId: string) {
  if (!refundId) return;
  const response = await admin.graphql(
    `query getReturn($id: ID!){
      refund(id: $id) {
        id
        totalRefundedSet {
          shopMoney {
            amount
          }
        }
        order {
          id
          refunds {
            id
          }
          totalPriceSet{
            shopMoney {
              amount
            }
          }
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
          customer {
            id
            metafields(first: 20, namespace: "$app") {
              edges {
                node {
                  namespace
                  key
                  value
                }
              }
            }
          }
        }
      }
    }`,
    { variables: { id: refundId } },
  );
  const responseJson = await response.json();
  const refundData = responseJson?.data?.refund;

  return refundData;
}

export async function getMetafields(admin: any, edges: any) {
  if (!edges) return;
  const namespace = (await getNamespaceMetafield(admin)) as string;
  return edges
    .filter((m: any) => m.node.namespace === namespace)
    .map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];
}
