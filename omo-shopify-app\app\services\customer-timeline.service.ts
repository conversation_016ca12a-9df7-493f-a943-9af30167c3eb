import db from "@/db.server";
import { CustomerTimelineType } from "@prisma/client";
import dayjs from "dayjs";

export function addCustomerTimeline({
  shopId,
  customerId,
  message,
  type,
}: {
  shopId: number;
  customerId: string;
  message: string;
  type: CustomerTimelineType;
}) {
  return db.customerTimeline.create({
    data: {
      shopId,
      customerId,
      message,
      type,
      date: dayjs().set("hour", 0).set("minute", 0).set("second", 0).toDate(),
    },
  });
}
