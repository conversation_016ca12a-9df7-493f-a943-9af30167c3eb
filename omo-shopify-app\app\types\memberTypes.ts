/**
 * Member data types for the Shopify OMO Integration app
 */

// Base member information
export interface MemberBasicInfo {
  id: string;
  numericId: string;
  fullName: string;
  email: string;
  phone: string;
  tags: string[];
}

// Member financial information
export interface MemberFinancialInfo {
  ordersCount: number;
  totalSpent: {
    amount: number;
    currencyCode: string;
  };
  points: string;
}

// Member loyalty information
export interface MemberLoyaltyInfo {
  vipTier: string | null;
}

// Complete member data
export interface Member extends MemberBasicInfo, MemberFinancialInfo, MemberLoyaltyInfo {
  cursor: string;
}

// API response pagination information
export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor: string | null;
  endCursor: string | null;
}

// Search parameters
export interface SearchParams {
  query: string;
  searchType: "email" | "name";
  page: number;
  cursor: string | null;
  direction: "next" | "previous";
}

// Loader data returned from the route
export interface MembersLoaderData {
  customers: Member[];
  pageInfo: PageInfo;
  totalCustomers: number;
  currentPage: number;
  searchQuery: string;
  searchType: string;
  shopId: string;
  errorMessage?: string;
}

// Member metafield data
export interface MemberMetafield {
  namespace: string;
  key: string;
  value: string;
}
