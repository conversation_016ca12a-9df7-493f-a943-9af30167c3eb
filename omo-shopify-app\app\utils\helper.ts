export function generateRandomCode(length = 10) {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const timestamp = Date.now().toString(36);
  const randomPart = Array(length - timestamp.length)
    .fill(0)
    .map(() => characters.charAt(Math.floor(Math.random() * characters.length)))
    .join("");
  return (timestamp + randomPart).toUpperCase().slice(0, length);
}

export function moneyFormat(amount: number | null | undefined | string, currency = "TWD") {
  const numericAmount = Number(amount);
  const validAmount = isNaN(numericAmount) ? 0 : numericAmount;
  return `${Intl.NumberFormat().format(validAmount)} ${currency}`;
}

export function numberFormat(amount: number | null | undefined | string) {
  const numericAmount = Number(amount);
  const validAmount = isNaN(numericAmount) ? 0 : numericAmount;
  return Intl.NumberFormat().format(validAmount);
}
