// src/utils/iconMap.ts
import { Cake, Edit2, Gift, LogIn, ShoppingCart, User } from "lucide-react";
import type { ReactNode } from "react";

export interface IconLabel {
  icon: ReactNode;
  label: string;
  subLabel?: string;
  /** URL to navigate to when clicked */
  url?: string;
}

export const ICON_MAP: Record<string, IconLabel> = {
  PURCHASE: {
    icon: <ShoppingCart className="w-6 h-6" />,
    label: "Place an order",
    subLabel: "2 rewards",
    url: "/app/loyalties/place-an-order",
  },
  SIGN_UP: {
    icon: <LogIn className="w-6 h-6" />,
    label: "Sign up",
    subLabel: "2 reward",
    url: "/app/loyalties/signup",
  },
  COMPLETE_PROFILE: {
    icon: <Edit2 className="w-6 h-6" />,
    label: "Complete your profile",
    subLabel: "1 rewards",
    url: "/app/loyalties/complete-profile",
  },
  CUSTOM_REWARD: {
    icon: <Gift className="w-6 h-6" />,
    label: "Custom reward",
    url: "/app/loyalties/custom-reward",
  },
  CELEBRATE_BIRTHDAY: {
    icon: <Cake className="w-6 h-6" />,
    label: "Celebrate a birthday",
    subLabel: "1 reward",
    url: "/app/loyalties/birthday",
  },
  REFERRAL: {
    icon: <User className="w-6 h-6" />,
    label: "Referral",
    url: "/app/loyalties/referral",
  },
};
