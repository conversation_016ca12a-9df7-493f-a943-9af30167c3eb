import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DelayedPointsStatus, gidPrefix, MetafieldType } from "@/constants";
import logger from "@/logger.server";
import { ApolloClient, gql } from "@apollo/client/core";
import db from "../db.server";
import { clientByShop } from "./auth.server";

// Improved cron implementation with better error handling and logging
class ImprovedCron {
  private intervals: NodeJS.Timeout[] = [];
  private isRunning = false;

  start() {
    if (this.isRunning) {
      console.log(" Cron jobs already running, skipping start");
      return;
    }

    console.log(" Starting improved cron jobs...");
    this.isRunning = true;

    // Test job - runs every minute for debugging
    if (process.env.NODE_ENV === "development") {
      this.scheduleInterval(60 * 1000, () => {
        logger.info(` Test cron job running at ${new Date().toISOString()}`);
      });
    }

    // Birthday points - daily at midnight (00:00)
    this.scheduleDailyAt("00:00", () => {
      logger.info("Running birthday points job...");
      return updatePointsOnBirthday()
        .then((result) => {
          logger.info(" Birthday points job completed:", result);
        })
        .catch((err) => {
          logger.error(" Birthday points job error:", err);
        });
    });

    // Delayed points - daily at 2:00 AM
    this.scheduleDailyAt("02:00", () => {
      logger.info(" Running delayed points job...");
      return processDelayedPoints()
        .then((result) => {
          logger.info(" Delayed points job completed:", result);
        })
        .catch((err) => {
          logger.error(" Delayed points job error:", err);
        });
    });

    // Cleanup - daily at 3:00 AM (instead of weekly for more frequent cleanup)
    this.scheduleDailyAt("03:00", () => {
      logger.info(" Running cleanup job...");
      return cleanupOldRecords()
        .then((count) => {
          logger.info(` Cleanup job completed, cleaned ${count} records`);
        })
        .catch((err) => {
          logger.error(" Cleanup job error:", err);
        });
    });

    logger.info(" All cron jobs scheduled successfully");
    this.logNextScheduledTimes();
  }

  stop() {
    logger.info(" Stopping cron jobs...");
    this.intervals.forEach(clearInterval);
    this.intervals = [];
    this.isRunning = false;
    logger.info(" All cron jobs stopped");
  }

  // Simple interval scheduler for testing
  private scheduleInterval(intervalMs: number, callback: () => void | Promise<void>) {
    const interval = setInterval(callback, intervalMs);
    this.intervals.push(interval);
    logger.info(` Scheduled interval job every ${intervalMs / 1000} seconds`);
  }

  // Improved daily scheduler using simple interval-based approach
  private scheduleDailyAt(timeString: string, callback: () => void | Promise<void>) {
    const [hours, minutes] = timeString.split(":").map(Number);

    const runJob = () => {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      // Check if current time matches target time
      if (currentHour === hours && currentMinute === minutes) {
        logger.info(` Executing scheduled job at ${timeString}`);
        try {
          const result = callback();
          // Handle both sync and async callbacks
          if (result && typeof result === "object" && "catch" in result) {
            (result as Promise<void>).catch((error) => {
              logger.error(` Scheduled job error at ${timeString}:`, error);
            });
          }
        } catch (error) {
          logger.error(` Scheduled job error at ${timeString}:`, error);
        }
      }
    };

    // Check every minute
    const interval = setInterval(runJob, 60 * 1000);
    this.intervals.push(interval);

    logger.info(` Scheduled daily job at ${timeString}`);
  }

  private logNextScheduledTimes() {
    logger.info(" Next scheduled jobs:");
    logger.info("  - Birthday points: Daily at 00:00");
    logger.info("  - Delayed points: Daily at 02:00");
    logger.info("  - Cleanup: Daily at 03:00");
  }
}

// Enhanced semaphore with timeout protection
class EnhancedSemaphore {
  private count: number;
  private queue: Array<{ callback: () => void | Promise<void>; timeout: NodeJS.Timeout }> = [];
  private readonly timeoutMs = 30 * 60 * 1000; // 30 minutes timeout

  constructor(count: number) {
    this.count = count;
  }

  take(callback: () => void | Promise<void>): void {
    if (this.count > 0) {
      this.count--;
      this.executeWithTimeout(callback);
    } else {
      // Add to queue with timeout
      const timeout = setTimeout(() => {
        logger.error(" Semaphore callback timed out, removing from queue");
        this.removeFromQueue(callback);
      }, this.timeoutMs);

      this.queue.push({ callback, timeout });
    }
  }

  leave(): void {
    this.count++;
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      if (next) {
        clearTimeout(next.timeout);
        this.count--;
        this.executeWithTimeout(next.callback);
      }
    }
  }

  private executeWithTimeout(callback: () => void | Promise<void>): void {
    const startTime = Date.now();
    logger.info(" Starting semaphore-protected job");

    const timeoutId = setTimeout(() => {
      logger.error(" Job execution timed out after 30 minutes");
    }, this.timeoutMs);

    try {
      const result = callback();
      // Check if result is a Promise
      if (
        result &&
        typeof result === "object" &&
        "then" in result &&
        typeof result.then === "function"
      ) {
        (result as Promise<void>)
          .then(() => {
            const duration = Date.now() - startTime;
            logger.info(` Async job completed in ${duration}ms`);
          })
          .catch((error) => {
            logger.error(" Async job failed:", error);
          })
          .finally(() => {
            clearTimeout(timeoutId);
            this.leave();
          });
      } else {
        const duration = Date.now() - startTime;
        logger.info(` Sync job completed in ${duration}ms`);
        clearTimeout(timeoutId);
        this.leave();
      }
    } catch (error) {
      logger.error(" Job execution failed:", error);
      clearTimeout(timeoutId);
      this.leave();
    }
  }

  private removeFromQueue(callback: () => void | Promise<void>): void {
    const index = this.queue.findIndex((item) => item.callback === callback);
    if (index !== -1) {
      const item = this.queue.splice(index, 1)[0];
      clearTimeout(item.timeout);
    }
  }
}

// Global instances
const sem = new EnhancedSemaphore(1);
const improvedCron = new ImprovedCron();

export default function runCronJob() {
  return {
    start: () => {
      try {
        improvedCron.start();
      } catch (error) {
        logger.error(" Failed to start cron jobs:", error);
      }
    },
    stop: () => {
      try {
        improvedCron.stop();
      } catch (error) {
        logger.error(" Failed to stop cron jobs:", error);
      }
    },
    // Add status check method
    status: () => {
      return {
        isRunning: improvedCron["isRunning"],
        activeJobs: improvedCron["intervals"].length,
        timestamp: new Date().toISOString(),
      };
    },
  };
}

// ========================================
// BIRTHDAY POINTS JOB (với logging cải thiện)
// ========================================

const today = new Date();

interface CustomerQueryResponse {
  data: {
    customers: {
      edges: Array<{
        cursor: string;
        node: {
          id: string;
          metafields: {
            nodes: {
              id: string;
              namespace: string;
              key: string;
              value: string;
            }[];
          };
        };
      }>;
      pageInfo: {
        hasNextPage: boolean;
        endCursor?: string;
      };
    };
  };
}

export async function updatePointsOnBirthday() {
  return new Promise<{ processed: number; errors: number }>((resolve, reject) => {
    sem.take(async () => {
      const startTime = Date.now();
      let totalProcessed = 0;
      let totalErrors = 0;

      try {
        logger.info(" Processing birthday points...");

        // Get all shops
        const shops = await db.shop.findMany({
          select: {
            shopId: true,
            myshopifyDomain: true,
            shopToken: true,
          },
        });

        logger.info(` Found ${shops.length} shops to process`);

        // Loop through each shop and process
        for (const shop of shops) {
          try {
            logger.info(` Processing shop: ${shop.myshopifyDomain}`);

            const client = await clientByShop(shop);
            const namespace = (await getNamespaceMetafield(client)) as string;
            const birthdayRewards = await getBirthdayRewards(shop.shopId);

            if (!birthdayRewards.length) {
              logger.info(` No birthday rewards configured for shop: ${shop.myshopifyDomain}`);
              continue;
            }

            let previousEndCursor = null;
            let hasNextPage = true;
            let shopProcessed = 0;

            while (hasNextPage) {
              // Query a customer chunk
              const customerChunk = await getCustomerChunk(client, previousEndCursor ?? null);
              const birthdayCustomers = filterBirthdayCustomers(customerChunk, namespace);

              logger.info(`👥 Found ${birthdayCustomers.length} birthday customers in this chunk`);

              // Update points for each birthday customer
              for (const customer of birthdayCustomers) {
                try {
                  const customerId = customer.node.id;
                  const customerPoint = Number(
                    customer.node.metafields.nodes.find(
                      (m) => m.namespace === namespace && m.key === CustomerMetafield.POINTS,
                    )?.value || 0,
                  );
                  const totalCustomerPoint = Number(
                    customer.node.metafields.nodes.find(
                      (m) => m.namespace === namespace && m.key === CustomerMetafield.TOTAL_POINTS,
                    )?.value ?? 0,
                  );
                  const birthdayPoint = birthdayRewards.reduce(
                    (sum, reward) => sum + (reward.rewardValue ?? 0),
                    0,
                  );

                  await updateCustomerPoint(
                    client,
                    customerId,
                    customerPoint,
                    totalCustomerPoint,
                    birthdayPoint,
                  );
                  shopProcessed++;
                  totalProcessed++;

                  logger.info(` Added ${birthdayPoint} birthday points to customer ${customerId}`);
                } catch (error) {
                  logger.error(` Error updating customer points:`, error);
                  totalErrors++;
                }
              }

              // Update cursor status
              hasNextPage = customerChunk.data.customers.pageInfo.hasNextPage;
              previousEndCursor = customerChunk.data.customers.pageInfo.endCursor;
            }

            logger.info(
              ` Shop ${shop.myshopifyDomain} completed: ${shopProcessed} customers processed`,
            );
            client.stop();
          } catch (error) {
            logger.error(` Error processing shop ${shop.myshopifyDomain}:`, error);
            totalErrors++;
          }
        }

        const duration = Date.now() - startTime;
        logger.info(` Birthday points job completed in ${duration}ms`);
        logger.info(` Summary: ${totalProcessed} processed, ${totalErrors} errors`);

        resolve({ processed: totalProcessed, errors: totalErrors });
      } catch (error) {
        logger.error(" Birthday points job failed:", error);
        reject(error);
      }
    });
  });
}

// Rest of the functions remain the same but with improved logging...
// (keeping the same logic but adding better console.log statements)

const getBirthdayRewards = async (shopId: string) =>
  await db.birthdayReward.findMany({
    include: {
      settings: {
        include: {
          shop: true,
        },
      },
    },
    where: {
      settings: {
        shop: {
          shopId: shopId,
        },
      },
    },
  });

const getCustomerChunk = async (
  client: ApolloClient<object>,
  cursor: string | null,
): Promise<CustomerQueryResponse> =>
  await client.query({
    query: gql`
      query ($cursor: String) {
        customers(first: 10, after: $cursor) {
          edges {
            cursor
            node {
              id
              metafields(first: 20, namespace: "$app") {
                nodes {
                  id
                  namespace
                  key
                  value
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    `,
    variables: {
      cursor: cursor,
    },
  });

const filterBirthdayCustomers = (customers: CustomerQueryResponse, namespace: string) => {
  // Filter customers whose birthday is today
  const todayMonth = today.getMonth() + 1;
  const todayDay = today.getDate();

  return customers.data.customers.edges.filter((edge) => {
    const customerBirthday = edge.node.metafields.nodes.find(
      (m) => m.namespace === namespace && m.key === CustomerMetafield.BIRTH_DATE,
    )?.value;

    if (!customerBirthday) return false;

    const [, month, day] = customerBirthday.split("-");
    return `${Number(todayMonth)}-${Number(todayDay)}` === `${Number(month)}-${Number(day)}`;
  });
};

const updateCustomerPoint = async (
  client: ApolloClient<object>,
  customerId: string,
  oldPoint: number,
  oldTotalPoint: number,
  newPoint: number,
) =>
  await client.mutate({
    mutation: gql`
      mutation updateCustomerMetafields($input: CustomerInput!) {
        customerUpdate(input: $input) {
          userErrors {
            message
            field
          }
        }
      }
    `,
    variables: {
      input: {
        id: customerId,
        metafields: [
          {
            key: CustomerMetafield.POINTS,
            value: (oldPoint + newPoint).toString(),
            type: MetafieldType.NUMBER_INTEGER,
          },
          {
            key: CustomerMetafield.TOTAL_POINTS,
            value: (oldTotalPoint + newPoint).toString(),
            type: MetafieldType.NUMBER_INTEGER,
          },
        ],
      },
    },
  });

// Keep all other existing functions (processDelayedPoints, cleanupOldRecords, etc.)
// but add better logging to each one...

export async function processDelayedPoints() {
  return new Promise<{ processed: number; errors: number }>((resolve, reject) => {
    sem.take(async () => {
      try {
        const now = new Date();
        console.log(` Processing delayed points at ${now.toISOString()}`);

        // Get all due points records
        const dueRecords = await db.loyaltyPointsHistory.findMany({
          where: {
            status: DelayedPointsStatus.PENDING,
            issueAt: { lte: now },
          },
          orderBy: {
            issueAt: "asc",
          },
        });

        if (dueRecords.length === 0) {
          resolve({ processed: 0, errors: 0 });
          return;
        }

        logger.info(` Found ${dueRecords.length} delayed points records to process`);

        // Group records by shop for efficient processing
        const recordsByShop = await groupRecordsByShop(dueRecords);

        let totalProcessed = 0;
        let totalErrors = 0;

        // Process each shop
        for (const [shopDomain, records] of Object.entries(recordsByShop)) {
          try {
            logger.info(` Processing ${records.length} records for shop: ${shopDomain}`);

            const shop = await db.shop.findFirst({
              where: { myshopifyDomain: shopDomain },
              select: { shopId: true, myshopifyDomain: true, shopToken: true },
            });

            if (!shop) {
              logger.error(` Shop not found: ${shopDomain}`);
              totalErrors += records.length;
              continue;
            }

            const client = await clientByShop(shop);
            const { processed, errors } = await processRecordsForShop(client, records);

            totalProcessed += processed;
            totalErrors += errors;

            client.stop();
          } catch (error) {
            logger.error(` Error processing shop ${shopDomain}:`, error);
            totalErrors += records.length;
          }
        }

        logger.info(
          ` Delayed points processing completed. Processed: ${totalProcessed}, Errors: ${totalErrors}`,
        );
        resolve({ processed: totalProcessed, errors: totalErrors });
      } catch (error) {
        logger.error(" Error in processDelayedPoints:", error);
        reject(error);
      }
    });
  });
}

async function groupRecordsByShop(records: any[]) {
  const grouped: { [shopDomain: string]: any[] } = {};

  for (const record of records) {
    // Find shop for this customer (using existing shop records)
    const shops = await db.shop.findMany({
      where: {
        loyaltyPrograms: {
          some: {
            isActive: true,
            programType: "POINTS",
          },
        },
      },
      select: { myshopifyDomain: true },
      take: 1,
    });

    const shopDomain = shops[0]?.myshopifyDomain || "unknown";

    if (!grouped[shopDomain]) {
      grouped[shopDomain] = [];
    }
    grouped[shopDomain].push(record);
  }

  return grouped;
}

async function processRecordsForShop(client: ApolloClient<object>, records: any[]) {
  let processedCount = 0;
  let errorCount = 0;

  for (const record of records) {
    try {
      logger.info(` Processing record ${record.id} for customer ${record.customerId}`);

      // Get customer's current points
      const customerData = await client.query({
        query: gql`
          query getCustomerPoints($customerId: ID!) {
            customer(id: $customerId) {
              id
              metafields(first: 20, namespace: "$app") {
                nodes {
                  id
                  namespace
                  key
                  value
                }
              }
            }
          }
        `,
        variables: { customerId: record.customerId },
      });

      const namespace = (await getNamespaceMetafield(client)) as string;
      const customer = customerData.data.customer;

      if (!customer) {
        logger.error(` Customer not found: ${record.customerId}`);
        errorCount++;
        await markRecordAsError(record.id);
        continue;
      }

      const pointsMetafield = customer.metafields.nodes.find(
        (m: any) => m.namespace === namespace && m.key === CustomerMetafield.POINTS,
      );
      const currentPoints = pointsMetafield?.value ? Number(pointsMetafield.value) : 0;

      const totalPoints = Number(
        customer.metafields.nodes.find(
          (m: any) => m.namespace === namespace && m.key === CustomerMetafield.TOTAL_POINTS,
        )?.value ?? 0,
      );
      const newTotal = currentPoints + record.points;

      // Update customer points
      await client.mutate({
        mutation: gql`
          mutation updateCustomerPoints($input: CustomerInput!) {
            customerUpdate(input: $input) {
              customer {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: {
          input: {
            id: record.customerId,
            metafields: [
              {
                key: CustomerMetafield.POINTS,
                value: newTotal.toString(),
                type: MetafieldType.NUMBER_INTEGER,
              },
              {
                key: CustomerMetafield.TOTAL_POINTS,
                value: (totalPoints + record.points).toString(),
                type: MetafieldType.NUMBER_INTEGER,
              },
            ],
          },
        },
      });

      // Mark record as issued
      await db.loyaltyPointsHistory.update({
        where: { id: record.id },
        data: {
          status: DelayedPointsStatus.ISSUED,
          issuedAt: new Date(),
        },
      });

      processedCount++;
      logger.info(
        ` Issued ${record.points} points to customer ${record.customerId}. New total: ${newTotal}`,
      );
    } catch (error) {
      logger.error(` Error processing record ${record.id}:`, error);
      errorCount++;
      await markRecordAsError(record.id);
    }
  }

  return { processed: processedCount, errors: errorCount };
}

async function markRecordAsError(recordId: number) {
  try {
    await db.loyaltyPointsHistory.update({
      where: { id: recordId },
      data: { status: DelayedPointsStatus.ERROR },
    });
  } catch (error) {
    logger.error(` Error marking record ${recordId} as ERROR:`, error);
  }
}

export async function cleanupOldRecords() {
  return new Promise<number>((resolve, reject) => {
    sem.take(async () => {
      try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90); // 90 days old

        logger.info(` Cleaning up records older than ${cutoffDate.toISOString()}`);

        const result = await db.loyaltyPointsHistory.deleteMany({
          where: {
            status: DelayedPointsStatus.ISSUED,
            issuedAt: {
              lt: cutoffDate,
            },
          },
        });

        logger.info(` Cleaned up ${result.count} old ISSUED records older than 90 days`);
        resolve(result.count);
      } catch (error) {
        logger.error(" Error in cleanup job:", error);
        reject(error);
      }
    });
  });
}

// Utility functions remain the same...
export async function retryFailedPoints() {
  const failedRecords = await db.loyaltyPointsHistory.findMany({
    where: {
      status: DelayedPointsStatus.ERROR,
    },
    take: 10,
    orderBy: {
      createdAt: "desc",
    },
  });

  logger.info(` Found ${failedRecords.length} failed records to retry`);

  for (const record of failedRecords) {
    await db.loyaltyPointsHistory.update({
      where: { id: record.id },
      data: { status: DelayedPointsStatus.PENDING },
    });
    logger.info(` Reset record ${record.id} to PENDING status`);
  }

  return { retried: failedRecords.length };
}

export async function getDelayedPointsStatistics() {
  const stats = await db.loyaltyPointsHistory.groupBy({
    by: ["status"],
    _count: {
      id: true,
    },
    _sum: {
      points: true,
    },
  });

  const totalRecords = await db.loyaltyPointsHistory.count();

  return {
    byStatus: stats,
    totalRecords,
    timestamp: new Date().toISOString(),
  };
}

export async function getNamespaceMetafield(client: any) {
  if (!client) return;
  const appResponse = await client.query({
    query: gql`
      query getApp {
        app {
          id
        }
      }
    `,
  });
  const appId = appResponse.data?.app?.id?.replace(gidPrefix.APP, "");
  const namespace = `app--${appId}`;
  return namespace;
}
