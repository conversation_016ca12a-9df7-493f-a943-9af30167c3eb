import Jo<PERSON> from "joi";

export const ERROR_TYPE_MAPPING: Record<string, string> = {
  "any.required": "missing_field",
  "string.empty": "required_field",
  "string.isoDate": "invalid_format",
  "any.only": "invalid_value",
};

export function validatePayload(schema: Joi.ObjectSchema, payload: any) {
  const { error, value } = schema.validate(payload, { abortEarly: false });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.path.join("."),
      code: ERROR_TYPE_MAPPING[detail.type] || "validation_error",
      message: detail.message,
    }));
    return { success: false, errors };
  }

  return { success: true, data: value };
}
