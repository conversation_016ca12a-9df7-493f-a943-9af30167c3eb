#!/bin/sh

# Exit on any error
set -e

echo "Starting OMO Shopify App..."
echo "Environment: $NODE_ENV"

# Wait for database to be ready
echo "Waiting for database connection..."
until nc -z mysql 3306; do
  echo "Database not ready, waiting..."
  sleep 2
done
echo "Database is ready!"

# Wait for Redis to be ready  
echo "Waiting for Redis connection..."
until nc -z redis 6379; do
  echo "Redis not ready, waiting..."
  sleep 2
done
echo "Redis is ready!"

# Run database setup
echo "Running database setup..."
npm run setup

# Start the application
echo "Starting Remix application..."
exec npm run start
