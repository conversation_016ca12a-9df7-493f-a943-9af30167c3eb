// extensions/discount-base-points/node_modules/@shopify/shopify_function/run.ts
function run_default(userfunction) {
  try {
    ShopifyFunction;
  } catch (e) {
    throw new Error(
      "ShopifyFunction is not defined. Please rebuild your function using the latest version of Shopify CLI."
    );
  }
  const input_obj = ShopifyFunction.readInput();
  const output_obj = userfunction(input_obj);
  ShopifyFunction.writeOutput(output_obj);
}

// extensions/discount-base-points/src/cart_lines_discounts_generate_run.ts
function cartLinesDiscountsGenerateRun(input) {
  const usingPoints = Number(input?.cart?.usingPoints?.value);
  const points = Number(input?.cart?.buyerIdentity?.customer?.metafield?.value);
  const discountValue = Number(input?.cart?.discountValue?.value);
  if (input?.cart?.buyerIdentity?.customer && usingPoints > 0 && usingPoints <= points)
    return {
      operations: [
        {
          orderDiscountsAdd: {
            candidates: [
              {
                message: `OMO_POINTS_${usingPoints} ${discountValue} ${input.cart.cost.totalAmount.currencyCode} off on your order`,
                targets: [
                  {
                    orderSubtotal: {
                      excludedCartLineIds: []
                    }
                  }
                ],
                value: {
                  fixedAmount: {
                    amount: discountValue
                  }
                }
              }
            ],
            selectionStrategy: "FIRST" /* First */
          }
        }
      ]
    };
  return {
    operations: []
  };
}

// <stdin>
function cartLinesDiscountsGenerateRun2() {
  return run_default(cartLinesDiscountsGenerateRun);
}
export {
  cartLinesDiscountsGenerateRun2 as cartLinesDiscountsGenerateRun
};
