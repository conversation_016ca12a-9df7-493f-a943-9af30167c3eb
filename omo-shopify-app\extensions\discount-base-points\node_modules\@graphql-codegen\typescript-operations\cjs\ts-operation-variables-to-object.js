"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeScriptOperationVariablesToObject = void 0;
const typescript_1 = require("@graphql-codegen/typescript");
class TypeScriptOperationVariablesToObject extends typescript_1.TypeScriptOperationVariablesToObject {
    formatTypeString(fieldType, _isNonNullType, _hasDefaultValue) {
        return fieldType;
    }
}
exports.TypeScriptOperationVariablesToObject = TypeScriptOperationVariablesToObject;
