import { PluginFunction, Types } from '@graphql-codegen/plugin-helpers';
import { TypeScriptDocumentsPluginConfig } from './config.cjs';
import { TypeScriptDocumentsVisitor } from './visitor.cjs';
export { TypeScriptDocumentsPluginConfig } from './config.cjs';
export declare const plugin: PluginFunction<TypeScriptDocumentsPluginConfig, Types.ComplexPluginOutput>;
export { TypeScriptDocumentsVisitor };
