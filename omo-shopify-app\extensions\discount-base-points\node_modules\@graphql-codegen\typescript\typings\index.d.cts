import { PluginFunction, Types } from '@graphql-codegen/plugin-helpers';
import { GraphQLSchema } from 'graphql';
import { TypeScriptPluginConfig } from './config.cjs';
export * from './config.cjs';
export * from './introspection-visitor.cjs';
export * from './typescript-variables-to-object.cjs';
export * from './visitor.cjs';
export declare const plugin: PluginFunction<TypeScriptPluginConfig, Types.ComplexPluginOutput>;
export declare function includeIntrospectionTypesDefinitions(schema: GraphQLSchema, documents: Types.DocumentFile[], config: TypeScriptPluginConfig): string[];
