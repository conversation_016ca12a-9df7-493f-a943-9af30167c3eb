import { PluginFunction, Types } from '@graphql-codegen/plugin-helpers';
import { GraphQLSchema } from 'graphql';
import { TypeScriptPluginConfig } from './config.js';
export * from './config.js';
export * from './introspection-visitor.js';
export * from './typescript-variables-to-object.js';
export * from './visitor.js';
export declare const plugin: PluginFunction<TypeScriptPluginConfig, Types.ComplexPluginOutput>;
export declare function includeIntrospectionTypesDefinitions(schema: GraphQLSchema, documents: Types.DocumentFile[], config: TypeScriptPluginConfig): string[];
