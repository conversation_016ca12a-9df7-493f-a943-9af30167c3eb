"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_DECLARATION_KINDS = void 0;
exports.normalizeDeclarationKind = normalizeDeclarationKind;
exports.DEFAULT_DECLARATION_KINDS = {
    directive: 'type',
    scalar: 'type',
    input: 'type',
    type: 'type',
    interface: 'type',
    arguments: 'type',
};
function normalizeDeclarationKind(declarationKind) {
    if (typeof declarationKind === 'string') {
        return {
            directive: declarationKind,
            scalar: declarationKind,
            input: declarationKind,
            type: declarationKind,
            interface: declarationKind,
            arguments: declarationKind,
        };
    }
    return {
        ...exports.DEFAULT_DECLARATION_KINDS,
        ...declarationKind,
    };
}
