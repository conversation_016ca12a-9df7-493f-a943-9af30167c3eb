{"name": "@graphql-codegen/visitor-plugin-common", "version": "5.8.0", "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}, "dependencies": {"@graphql-tools/optimize": "^2.0.0", "@graphql-codegen/plugin-helpers": "^5.1.0", "@graphql-tools/relay-operation-optimizer": "^7.0.0", "@graphql-tools/utils": "^10.0.0", "auto-bind": "~4.0.0", "dependency-graph": "^0.11.0", "graphql-tag": "^2.11.0", "parse-filepath": "^1.0.2", "change-case-all": "1.0.15", "tslib": "~2.6.0"}, "repository": {"type": "git", "url": "https://github.com/dotansimha/graphql-code-generator.git", "directory": "packages/plugins/other/visitor-plugin-common"}, "license": "MIT", "engines": {"node": ">=16"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}