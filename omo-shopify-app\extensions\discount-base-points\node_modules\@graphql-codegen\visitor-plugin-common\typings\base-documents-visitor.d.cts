import { FragmentDefinitionNode, GraphQLSchema, OperationDefinitionNode } from 'graphql';
import { ParsedTypesConfig, RawTypesConfig } from './base-types-visitor.cjs';
import { BaseVisitor } from './base-visitor.cjs';
import { SelectionSetToObject } from './selection-set-to-object.cjs';
import { NormalizedScalarsMap, CustomDirectivesConfig } from './types.cjs';
import { DeclarationBlockConfig } from './utils.cjs';
import { OperationVariablesToObject } from './variables-to-object.cjs';
export interface ParsedDocumentsConfig extends ParsedTypesConfig {
    addTypename: boolean;
    preResolveTypes: boolean;
    extractAllFieldsToTypes: boolean;
    globalNamespace: boolean;
    operationResultSuffix: string;
    dedupeOperationSuffix: boolean;
    omitOperationSuffix: boolean;
    namespacedImportName: string | null;
    exportFragmentSpreadSubTypes: boolean;
    skipTypeNameForRoot: boolean;
    experimentalFragmentVariables: boolean;
    mergeFragmentTypes: boolean;
    customDirectives: CustomDirectivesConfig;
}
export interface RawDocumentsConfig extends RawTypesConfig {
    /**
     * @default true
     * @description Uses primitive types where possible.
     * Set to `false` in order to use `Pick` and take use the types generated by `typescript` plugin.
     *
     * @exampleMarkdown
     * ```ts filename="codegen.ts"
     *  import type { CodegenConfig } from '@graphql-codegen/cli';
     *
     *  const config: CodegenConfig = {
     *    // ...
     *    generates: {
     *      'path/to/file': {
     *        // plugins...
     *        config: {
     *          preResolveTypes: false
     *        },
     *      },
     *    },
     *  };
     *  export default config;
     * ```
     */
    preResolveTypes?: boolean;
    /**
     * @default false
     * @description Avoid adding `__typename` for root types. This is ignored when a selection explicitly specifies `__typename`.
     *
     * @exampleMarkdown
     * ```ts filename="codegen.ts"
     *  import type { CodegenConfig } from '@graphql-codegen/cli';
     *
     *  const config: CodegenConfig = {
     *    // ...
     *    generates: {
     *      'path/to/file': {
     *        // plugins...
     *        config: {
     *          skipTypeNameForRoot: true
     *        },
     *      },
     *    },
     *  };
     *  export default config;
     * ```
     */
    skipTypeNameForRoot?: boolean;
    /**
     * @default false
     * @description Puts all generated code under `global` namespace. Useful for Stencil integration.
     *
     * @exampleMarkdown
     * ```ts filename="codegen.ts"
     *  import type { CodegenConfig } from '@graphql-codegen/cli';
     *
     *  const config: CodegenConfig = {
     *    // ...
     *    generates: {
     *      'path/to/file': {
     *        // plugins...
     *        config: {
     *          globalNamespace: true
     *        },
     *      },
     *    },
     *  };
     *  export default config;
     * ```
     */
    globalNamespace?: boolean;
    /**
     * @default ""
     * @description Adds a suffix to generated operation result type names
     */
    operationResultSuffix?: string;
    /**
     * @default false
     * @description Set this configuration to `true` if you wish to make sure to remove duplicate operation name suffix.
     */
    dedupeOperationSuffix?: boolean;
    /**
     * @default false
     * @description Set this configuration to `true` if you wish to disable auto add suffix of operation name, like `Query`, `Mutation`, `Subscription`, `Fragment`.
     */
    omitOperationSuffix?: boolean;
    /**
     * @default false
     * @description If set to true, it will export the sub-types created in order to make it easier to access fields declared under fragment spread.
     */
    exportFragmentSpreadSubTypes?: boolean;
    /**
     * @default false
     * @description If set to true, it will enable support for parsing variables on fragments.
     */
    experimentalFragmentVariables?: boolean;
    /**
     * @default false
     * @description If set to true, merge equal fragment interfaces.
     */
    mergeFragmentTypes?: boolean;
    /**
     * @ignore
     */
    namespacedImportName?: string;
    /**
     * @description Configures behavior for use with custom directives from
     * various GraphQL libraries.
     * @exampleMarkdown
     * ```ts filename="codegen.ts"
     *  import type { CodegenConfig } from '@graphql-codegen/cli';
     *
     *  const config: CodegenConfig = {
     *    // ...
     *    generates: {
     *      'path/to/file.ts': {
     *        plugins: ['typescript'],
     *        config: {
     *          customDirectives: {
     *            apolloUnmask: true
     *          }
     *        },
     *      },
     *    },
     *  };
     *  export default config;
     * ```
     */
    customDirectives?: CustomDirectivesConfig;
}
export declare class BaseDocumentsVisitor<TRawConfig extends RawDocumentsConfig = RawDocumentsConfig, TPluginConfig extends ParsedDocumentsConfig = ParsedDocumentsConfig> extends BaseVisitor<TRawConfig, TPluginConfig> {
    protected _schema: GraphQLSchema;
    protected _unnamedCounter: number;
    protected _variablesTransfomer: OperationVariablesToObject;
    protected _selectionSetToObject: SelectionSetToObject;
    protected _globalDeclarations: Set<string>;
    constructor(rawConfig: TRawConfig, additionalConfig: TPluginConfig, _schema: GraphQLSchema, defaultScalars?: NormalizedScalarsMap);
    getGlobalDeclarations(noExport?: boolean): string[];
    setSelectionSetHandler(handler: SelectionSetToObject): void;
    setDeclarationBlockConfig(config: DeclarationBlockConfig): void;
    setVariablesTransformer(variablesTransfomer: OperationVariablesToObject): void;
    get schema(): GraphQLSchema;
    get addTypename(): boolean;
    private handleAnonymousOperation;
    FragmentDefinition(node: FragmentDefinitionNode): string;
    protected applyVariablesWrapper(variablesBlock: string, _operationType?: string): string;
    OperationDefinition(node: OperationDefinitionNode): string;
}
