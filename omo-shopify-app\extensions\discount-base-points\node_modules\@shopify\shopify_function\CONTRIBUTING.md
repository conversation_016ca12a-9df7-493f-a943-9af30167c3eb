# Contributing to @shopify/shopify-function-javascript

We love receiving pull requests!

## Standards

* PR should explain what the feature does, and why the change exists.
* PR should include any carrier specific documentation explaining how it works.
* Code should be generic and reusable.

## How to contribute

1. Fork it ( https://github.com/Shopify/shopify-function-javascript/fork )
2. Create your feature branch (`git checkout -b my-new-feature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin my-new-feature`)
5. Create a new Pull Request