/*!
 * Chai - expectTypes utility
 * Copyright(c) 2012-2014 <PERSON> <<EMAIL>>
 * MIT Licensed
 */

import {AssertionError} from 'assertion-error';
import {flag} from './flag.js';
import {type} from './type-detect.js';

/**
 * ### .expectTypes(obj, types)
 *
 * Ensures that the object being tested against is of a valid type.
 *
 *     utils.expectTypes(this, ['array', 'object', 'string']);
 *
 * @param {unknown} obj constructed Assertion
 * @param {Array} types A list of allowed types for this assertion
 * @namespace Utils
 * @name expectTypes
 * @public
 */
export function expectTypes(obj, types) {
  var flagMsg = flag(obj, 'message');
  var ssfi = flag(obj, 'ssfi');

  flagMsg = flagMsg ? flagMsg + ': ' : '';

  obj = flag(obj, 'object');
  types = types.map(function (t) {
    return t.toLowerCase();
  });
  types.sort();

  // Transforms ['lorem', 'ipsum'] into 'a lorem, or an ipsum'
  var str = types
    .map(function (t, index) {
      var art = ~['a', 'e', 'i', 'o', 'u'].indexOf(t.charAt(0)) ? 'an' : 'a';
      var or = types.length > 1 && index === types.length - 1 ? 'or ' : '';
      return or + art + ' ' + t;
    })
    .join(', ');

  var objType = type(obj).toLowerCase();

  if (
    !types.some(function (expected) {
      return objType === expected;
    })
  ) {
    throw new AssertionError(
      flagMsg + 'object tested must be ' + str + ', but ' + objType + ' given',
      undefined,
      ssfi
    );
  }
}
