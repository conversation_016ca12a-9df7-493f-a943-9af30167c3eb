{"name": "deep-eql", "version": "5.0.2", "description": "Improved deep equality testing for Node.js and the browser.", "keywords": ["chai util", "deep equal", "object equal", "testing"], "repository": {"type": "git", "url": "**************:chaijs/deep-eql.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> (https://github.com/keithamus)", "dougluce (https://github.com/dougluce)", "<PERSON><PERSON><PERSON> (https://github.com/flowlo)"], "type": "module", "main": "./index.js", "files": ["index.js", "deep-eql.js"], "scripts": {"bench": "node bench", "lint": "eslint --ignore-path .gitignore .", "semantic-release": "semantic-release pre && semantic-release post", "pretest": "npm run lint", "test": "npm run test:node && npm run test:browser", "test:browser": "web-test-runner", "test:node": "istanbul cover _mocha", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "watch": "web-test-runner --watch"}, "eslintConfig": {"extends": ["strict/es5"], "rules": {"complexity": 0, "no-underscore-dangle": 0, "no-use-before-define": 0, "spaced-comment": 0}, "parserOptions": {"sourceType": "module", "ecmaVersion": 2015}}, "devDependencies": {"@js-temporal/polyfill": "^0.4.3", "@rollup/plugin-commonjs": "^24.1.0", "@web/test-runner": "^0.16.1", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "eslint": "^7.32.0", "eslint-config-strict": "^14.0.1", "eslint-plugin-filenames": "^1.3.2", "istanbul": "^0.4.2", "kewlr": "^0.4.1", "lcov-result-merger": "^1.0.2", "lodash.isequal": "^4.4.0", "mocha": "^9.1.1", "simple-assert": "^2.0.0"}, "engines": {"node": ">=6"}}