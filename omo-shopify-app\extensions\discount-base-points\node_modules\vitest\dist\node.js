import { createServer as createServer$1 } from 'vite';
export { isFileServingAllowed, parseAst, parseAstAsync } from 'vite';
import { f as TestModule } from './chunks/index.DsZFoqi9.js';
export { e as TestCase, i as TestProject, g as TestSuite } from './chunks/index.DsZFoqi9.js';
export { G as GitNotFoundError, T as TestSpecification, F as TestsNotFoundError, V as VitestPackageInstaller, a as VitestPlugin, d as createViteLogger, c as createVitest, i as isValidApiRequest, b as registerConsoleShortcuts, r as resolveFsAllow, s as startVitest } from './chunks/cli-api.DqsSTaIi.js';
export { p as parseCLI } from './chunks/cac.CB_9Zo9Q.js';
export { d as createMethodsRPC, g as getFilePoolName, a as resolveApiServerConfig, b as resolveConfig } from './chunks/resolveConfig.rBxzbVsl.js';
export { B as BaseSequencer } from './chunks/RandomSequencer.CMRlh2v4.js';
export { distDir, rootDir } from './path.js';
import createDebug from 'debug';
import 'node:fs';
import '@vitest/runner/utils';
import 'pathe';
import 'tinyrainbow';
import './chunks/utils.DNoFbBUZ.js';
import 'node:util';
import '@vitest/utils';
import 'node:perf_hooks';
import '@vitest/utils/source-map';
import 'std-env';
import 'node:fs/promises';
import 'node:stream';
import 'node:console';
import 'node:process';
import './chunks/_commonjsHelpers.BFTU3MAI.js';
import 'assert';
import 'events';
import 'node:module';
import 'node:os';
import './chunks/coverage.BoMDb1ip.js';
import 'node:path';
import './chunks/index.BJDntFik.js';
import 'node:url';
import 'readline';
import './chunks/constants.fzPh7AOq.js';
import '@vitest/snapshot/manager';
import 'vite-node/client';
import 'vite-node/server';
import './chunks/index.68735LiX.js';
import 'stream';
import 'zlib';
import 'buffer';
import 'crypto';
import 'https';
import 'http';
import 'net';
import 'tls';
import 'url';
import 'node:crypto';
import 'os';
import 'path';
import 'fs';
import 'vite-node/utils';
import '@vitest/mocker/node';
import 'magic-string';
import 'node:readline';
import 'node:assert';
import 'node:v8';
import 'util';
import 'node:events';
import 'tinypool';
import 'node:worker_threads';
import 'tinyexec';

function createDebugger(namespace) {
  const debug = createDebug(namespace);
  if (debug.enabled) {
    return debug;
  }
}

const createServer = createServer$1;
const createViteServer = createServer$1;
const TestFile = TestModule;

export { TestFile, TestModule, createDebugger, createServer, createViteServer };
