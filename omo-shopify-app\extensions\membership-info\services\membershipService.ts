// File: extensions/membership-info/services/membershipService.ts
import { MembershipData } from "../types/membershipTypes";

// Get customer data from Customer Account API
export async function fetchCustomerData(): Promise<MembershipData> {
  try {
    console.log("Fetching customer data from API...");

    // GraphQL query following documentation structure
    const query = {
      query: `query {
        customer {
          id
          firstName
          lastName
          displayName
          emailAddress {
            emailAddress
          }
        }
      }`,
    };

    const response = await fetch("shopify://customer-account/api/unstable/graphql.json", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(query),
    });

    if (!response.ok) {
      throw new Error(`API response error: ${response.status}`);
    }

    // Parse JSON response
    const result = await response.json();
    console.log("API Response:", result);

    // Check for errors in the response
    if (result.errors) {
      console.error("GraphQL errors:", result.errors);
      throw new Error(result.errors[0]?.message || "GraphQL error");
    }

    // Check data structure
    if (!result.data || !result.data.customer) {
      console.error("Invalid API response format:", result);
      throw new Error("Invalid API response format");
    }

    const customerData = result.data.customer;

    // Create displayName from firstName and lastName if displayName is not available
    const displayName =
      customerData.displayName ||
      `${customerData.firstName || ""} ${customerData.lastName || ""}`.trim() ||
      "Customer";

    // Get email from emailAddress.emailAddress structure
    const email = customerData.emailAddress?.emailAddress || "";

    // Create dummy data for other information
    const now = new Date();

    return {
      customer: {
        id: customerData.id || "gid://shopify/Customer/unknown",
        displayName: displayName,
        email: email,
      },
      membership: {
        tier: "VIP",
        points: 1540,
        storeCredit: 50000,
        memberSince: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        totalSpent: 7500000,
        nextTierThreshold: 10000,
        currentTierThreshold: 5000,
      },
      activities: [
        {
          id: "act-1",
          type: "points_earned",
          description: "Points from order #1001",
          date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "+150",
          metadata: {
            orderId: "1001",
            pointsBalance: 1540,
          },
        },
        {
          id: "act-2",
          type: "store_credit_earned",
          description: "Bonus credit for loyal customer",
          date: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "+$50.00",
          metadata: {
            storeCreditBalance: 50000,
          },
        },
        {
          id: "act-3",
          type: "points_redeemed",
          description: "Points redeemed for discount",
          date: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "-500",
          metadata: {
            orderId: "987",
            pointsBalance: 1390,
          },
        },
        {
          id: "act-4",
          type: "tier_upgrade",
          description: "Upgraded to VIP tier",
          date: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "🎉",
          metadata: {
            tierName: "VIP",
          },
        },
        {
          id: "act-5",
          type: "points_earned",
          description: "Points from first purchase",
          date: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "+1000",
          metadata: {
            orderId: "834",
            pointsBalance: 1000,
          },
        },
        {
          id: "act-6",
          type: "store_credit_used",
          description: "Store credit used for order",
          date: new Date(now.getTime() - 45 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "-$15.00",
          metadata: {
            orderId: "912",
            storeCreditBalance: 25000,
          },
        },
      ],
    };
  } catch (error) {
    console.error("Error fetching customer data:", error);

    // Return default data if error occurs
    const now = new Date();
    return {
      customer: {
        id: "gid://shopify/Customer/default",
        displayName: "Customer",
        email: "<EMAIL>",
      },
      membership: {
        tier: "VIP",
        points: 1540,
        storeCredit: 50000,
        memberSince: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        totalSpent: 7500000,
        nextTierThreshold: 10000,
        currentTierThreshold: 5000,
      },
      activities: [
        {
          id: "act-1",
          type: "points_earned",
          description: "Points from order #1001",
          date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "+150",
          metadata: {
            orderId: "1001",
            pointsBalance: 1540,
          },
        },
        {
          id: "act-2",
          type: "store_credit_earned",
          description: "Bonus credit for loyal customer",
          date: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          amount: "+$50.00",
          metadata: {
            storeCreditBalance: 50000,
          },
        },
      ],
    };
  }
}
