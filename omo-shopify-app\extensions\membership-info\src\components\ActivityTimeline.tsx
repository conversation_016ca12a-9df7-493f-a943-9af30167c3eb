import {
  BlockStack,
  Card,
  Text,
  InlineStack,
  Divider,
  View,
  Grid,
  Icon,
} from "@shopify/ui-extensions-react/customer-account";
import { Activity } from "../../types/membershipTypes";

interface ActivityTimelineProps {
  activities: Activity[];
}

export function ActivityTimeline({ activities }: ActivityTimelineProps) {
  if (!activities || activities.length === 0) {
    return (
      <Card padding>
        <BlockStack spacing="base">
          <Text size="large" emphasis="bold">
            Activity History
          </Text>
          <Text size="medium">No activity yet</Text>
        </BlockStack>
      </Card>
    );
  }

  // Format date to display in a more readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  // Get appropriate icon for each activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "points_earned":
        return "📊";
      case "points_redeemed":
        return "🔄";
      case "store_credit_earned":
        return "💰";
      case "store_credit_used":
        return "💸";
      case "tier_upgrade":
        return "🏆";
      default:
        return "📝";
    }
  };

  // Get appropriate text appearance based on activity type
  const getActivityAppearance = (type: string, amount: string) => {
    if (amount.startsWith("+") || type.includes("earned") || type.includes("upgrade")) {
      return "success";
    } else if (amount.startsWith("-") || type.includes("redeemed") || type.includes("used")) {
      return "critical";
    }
    return undefined;
  };

  return (
    <Card padding>
      <BlockStack spacing="loose">
        <Text size="large" emphasis="bold">
          Activity History
        </Text>

        <BlockStack spacing="base">
          {activities.map((activity, index) => (
            <BlockStack key={activity.id} spacing="none">
              <Grid columns={["auto", "fill", "auto"]} spacing="base">
                {/* Left column - Activity Icon */}
                <View
                  cornerRadius="base"
                  minInlineSize={64} // Smaller size to fit icon tightly
                  minBlockSize={64} // Smaller size to fit icon tightly
                  inlineAlignment="center"
                  blockAlignment="center"
                  padding="none"
                >
                  {/* Icon centered both horizontally and vertically */}
                  <Text size="extraLarge" emphasis="bold">
                    {getActivityIcon(activity.type)}
                  </Text>
                </View>

                {/* Middle column - Activity info including date */}
                <BlockStack spacing="tight">
                  {/* Activity description */}
                  <Text size="medium" emphasis="bold">
                    {activity.description}
                  </Text>

                  {/* Activity metadata */}
                  {activity.metadata && (
                    <BlockStack spacing="extraTight">
                      {activity.metadata.orderId && (
                        <Text size="small" appearance="subdued">
                          Order #{activity.metadata.orderId}
                        </Text>
                      )}

                      {activity.metadata.pointsBalance !== undefined && (
                        <Text size="small" appearance="subdued">
                          Points balance: {activity.metadata.pointsBalance}
                        </Text>
                      )}

                      {activity.metadata.storeCreditBalance !== undefined && (
                        <Text size="small" appearance="subdued">
                          Store credit balance: $
                          {(activity.metadata.storeCreditBalance / 1000).toFixed(2)}
                        </Text>
                      )}

                      {activity.metadata.tierName && (
                        <Text size="small" appearance="subdued">
                          New tier: {activity.metadata.tierName}
                        </Text>
                      )}
                    </BlockStack>
                  )}
                </BlockStack>

                {/* Right column - Amount */}
                <BlockStack spacing="tight" inlineAlignment="end">
                  {/* Display date above amount, styled with emphasis */}
                  <Text size="medium" emphasis="bold" appearance="subdued">
                    {formatDate(activity.date)}
                  </Text>

                  {/* Amount value with appropriate styling */}
                  <Text
                    size="large"
                    appearance={getActivityAppearance(activity.type, activity.amount)}
                    emphasis="bold"
                  >
                    {activity.amount}
                  </Text>
                </BlockStack>
              </Grid>

              {/* Add divider between activities except for the last one */}
              {index < activities.length - 1 && (
                <View padding={["base", "none", "none", "none"]}>
                  <Divider />
                </View>
              )}
            </BlockStack>
          ))}
        </BlockStack>
      </BlockStack>
    </Card>
  );
}
