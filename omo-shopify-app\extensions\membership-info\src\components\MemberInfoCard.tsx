import {
  BlockStack,
  Card,
  Text,
  TextBlock,
  InlineStack,
  Divider,
  View,
  Grid,
} from "@shopify/ui-extensions-react/customer-account";
import { CustomerInfo, MembershipInfo } from "../../types/membershipTypes";

interface MemberInfoCardProps {
  customer: CustomerInfo;
  membership: MembershipInfo;
}

export function MemberInfoCard({ customer, membership }: MemberInfoCardProps) {
  // Format date to display in a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  // Format currency with proper symbols and thousands separators
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount / 1000); // Divide by 1000 to convert from cents if needed
  };

  // Check if the customer has a name and format it properly
  const getCustomerName = () => {
    if (!customer.firstName && !customer.lastName) {
      return (
        <Text size="medium" appearance="subdued">
          Name information not provided
        </Text>
      );
    }

    return (
      <Text size="medium">{`${customer.firstName || ""} ${customer.lastName || ""}`.trim()}</Text>
    );
  };

  // Calculate progress percentage
  const progressPercentage = Math.min(
    100,
    (membership.points / membership.nextTierThreshold) * 100,
  );

  return (
    <Card padding>
      <BlockStack spacing="loose">
        <Text size="large" emphasis="bold">
          Member Information
        </Text>

        {/* Member Info layout with title and field labels on same line as values */}
        <Grid columns={["fill", "fill"]} spacing="base">
          {/* Left column with field labels and values */}
          <BlockStack spacing="loose">
            <InlineStack spacing="tight" blockAlignment="center">
              <Text size="medium" appearance="accent" emphasis="bold">
                Name:
              </Text>
              {getCustomerName()}
            </InlineStack>

            <InlineStack spacing="tight" blockAlignment="center">
              <Text size="medium" appearance="accent" emphasis="bold">
                Email:
              </Text>
              <Text size="medium">{customer.email}</Text>
            </InlineStack>

            <InlineStack spacing="tight" blockAlignment="center">
              <Text size="medium" appearance="accent" emphasis="bold">
                Member since:
              </Text>
              <Text size="medium">{formatDate(membership.memberSince)}</Text>
            </InlineStack>
          </BlockStack>

          {/* Right column with values */}
          <BlockStack spacing="loose" inlineAlignment="end">
            <InlineStack spacing="tight" blockAlignment="center">
              <Text size="medium" appearance="accent" emphasis="bold">
                Tier:
              </Text>
              <Text size="medium" emphasis="bold">
                {membership.tier}
              </Text>
            </InlineStack>

            <InlineStack spacing="tight" blockAlignment="center">
              <Text size="medium" appearance="accent" emphasis="bold">
                Points:
              </Text>
              <Text size="medium" emphasis="bold">
                {membership.points}
              </Text>
            </InlineStack>

            <InlineStack spacing="tight" blockAlignment="center">
              <Text size="medium" appearance="accent" emphasis="bold">
                Store Credit:
              </Text>
              <Text size="medium" emphasis="bold">
                {formatCurrency(membership.storeCredit)}
              </Text>
            </InlineStack>

            {membership.totalSpent && (
              <InlineStack spacing="tight" blockAlignment="center">
                <Text size="medium" appearance="accent" emphasis="bold">
                  Total Spent:
                </Text>
                <Text size="medium" emphasis="bold">
                  {formatCurrency(membership.totalSpent)}
                </Text>
              </InlineStack>
            )}
          </BlockStack>
        </Grid>

        <Divider />

        {/* Tier progress section with improved visibility */}
        <BlockStack spacing="base">
          <Text size="medium" appearance="accent" emphasis="bold">
            Tier Progress:
          </Text>

          {/* Points to next tier left-aligned */}
          <View>
            <Grid columns={["fill", "fill"]} spacing="base">
              <BlockStack spacing={"loose"} inlineAlignment="start">
                <InlineStack spacing="tight" blockAlignment="center" inlineAlignment={"start"}>
                  <TextBlock size="medium" appearance="warning" emphasis="bold">
                    {membership.nextTierThreshold > membership.points
                      ? `${membership.nextTierThreshold - membership.points}`
                      : "Maximum tier reached"}{" "}
                  </TextBlock>
                  <TextBlock size="medium" emphasis="bold">
                    points to reach next tier
                  </TextBlock>
                </InlineStack>
              </BlockStack>
              <BlockStack spacing="loose" inlineAlignment="end">
                <Text size="medium" appearance="accent" emphasis="bold">
                  {membership.points} / {membership.nextTierThreshold} points
                </Text>
              </BlockStack>
            </Grid>
          </View>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}
