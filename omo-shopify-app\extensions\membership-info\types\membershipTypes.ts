// File: extensions/membership-info/types/membershipTypes.ts
export interface CustomerInfo {
  id: string;
  displayName: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface MembershipInfo {
  tier: string;
  points: number;
  storeCredit: number;
  memberSince: string;
  totalSpent: number;
  nextTierThreshold: number;
  currentTierThreshold: number;
}

export type ActivityType =
  | "points_earned"
  | "points_redeemed"
  | "tier_upgrade"
  | "store_credit_earned"
  | "store_credit_used";

export interface Activity {
  id: string;
  type: ActivityType;
  description: string;
  date: string;
  amount: string;
  metadata?: {
    orderId?: string;
    tierName?: string;
    pointsBalance?: number;
    storeCreditBalance?: number;
  };
}

export interface MembershipData {
  customer: CustomerInfo;
  membership: MembershipInfo;
  activities: Activity[];
}
