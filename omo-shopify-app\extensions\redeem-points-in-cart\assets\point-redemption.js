class PointRedemptionElement extends HTMLElement {
  static get observedAttributes() {
    return [];
  }

  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.template = document.getElementById("point-redemption-template");
    if (!this.template) {
      console.error("point-redemption template not found");
      return;
    }

    this.onToggle = this.onToggle.bind(this);

    // Loyalty-program parameters (populated in fetchLoyaltyData)
    this.pointsRedemptionAmount = 0; // e.g., 100 points
    this.pointsRedemptionValue = 0; // e.g., $1
    this.maxRedeemPercentage = 0; // e.g., 20%
    this.balance = 0; // Customer’s point balance
    this.cart = {}; // Will hold /cart.js data
    this.currencySymbol = window.Shopify?.currency?.active || "";
  }

  connectedCallback() {
    // 1) Clone & mount the <template> into shadow DOM
    const content = this.template.content.cloneNode(true);
    this.shadowRoot.appendChild(content);

    // 2) Cache references to key UI elements (they might be hidden/replaced if no customer)
    this.balanceEl = this.shadowRoot.getElementById("pr-balance");
    this.rateEl = this.shadowRoot.getElementById("pr-rate");
    this.toUseEl = this.shadowRoot.getElementById("pr-to-use");
    this.checkbox = this.shadowRoot.getElementById("pr-use-points");

    // 3) Read attributes
    this.allowFull = this.hasAttribute("allow-full-redemption");
    try {
      this.customer = JSON.parse(this.getAttribute("customer") || "{}");
    } catch {
      this.customer = {};
    }
    this.customerId = this.getAttribute("customerId") || null;
    this.balance = Number(this.getAttribute("balance") || 0);

    // If no logged-in customer, immediately show the login button instead of loading data
    if (!this.customerId) {
      this.showLoginButton();
      return;
    }

    // 4) Initial “loading” state (when a customer is present)
    this.rateEl.textContent = "Calculating…";
    this.toUseEl.textContent = "…";
    this.checkbox.disabled = true;

    // 5) Fetch cart + loyalty data, then render
    this.fetchCartData()
      .then(() => this.fetchLoyaltyData())
      .then(() => this.render())
      .catch((err) => {
        console.error("Error in data fetch:", err);
        this.rateEl.textContent = "Error loading points";
        this.toUseEl.textContent = "—";
      });

    // 6) Listen for checkbox toggles
    this.checkbox.addEventListener("change", this.onToggle);
  }

  disconnectedCallback() {
    if (this.checkbox) {
      this.checkbox.removeEventListener("change", this.onToggle);
    }
  }

  attributeChangedCallback(name, oldVal, newVal) {
    if (name === "customer") {
      try {
        this.customer = JSON.parse(newVal || "{}");
      } catch {
        this.customer = {};
      }
    }
    // If customerId changes from “null” to something valid, re-render
    this.customerId = this.getAttribute("customerId") || null;
    this.render();
  }

  /**
   * If no customer is logged in, clear out the template-based UI
   * and show a single “Login to Redeem Points” button.
   */
  showLoginButton() {
    // First, clear out anything that might have been attached from the template
    this.shadowRoot.innerHTML = "";

    // Create a simple button and style it (optional)
    const loginTag = document.createElement("a");
    loginTag.textContent = "Login to Redeem Points";
    loginTag.href = "/account/login";
    loginTag.id = "pr-login-button";
    loginTag.classList.add("pr-login-button");

    this.shadowRoot.appendChild(loginTag);
  }

  async fetchCartData() {
    try {
      const res = await fetch("/cart.js");
      this.cart = await res.json();
    } catch (e) {
      console.error("Error fetching cart:", e);
      this.cart = {};
    }
  }

  async fetchLoyaltyData() {
    try {
      const data = await fetch("/apps/proxy/points").then((r) => r.json());
      const lp = data.loyaltyProgram?.points || {};
      this.pointsRedemptionAmount = lp.pointsRedemptionAmount || 0;
      this.pointsRedemptionValue = lp.pointsRedemptionValue || 0;
      this.maxRedeemPercentage = lp.maxRedeemPercentage || 0;
      if (lp.balance != null) {
        this.balance = lp.balance;
      }
    } catch (e) {
      console.error("Error fetching loyalty:", e);
      this.pointsRedemptionAmount = 0;
      this.pointsRedemptionValue = 0;
      this.maxRedeemPercentage = 0;
    }
  }

  render() {
    // If there’s no shadowRoot or no customerId, skip normal rendering
    if (!this.shadowRoot || !this.customerId) return;

    // If loyalty data is missing, show an error state
    if (this.pointsRedemptionAmount === 0 && this.pointsRedemptionValue === 0) {
      this.rateEl.textContent = "Points data unavailable";
      this.toUseEl.textContent = "—";
      this.checkbox.disabled = true;
      this.balanceEl.textContent = this.balance.toLocaleString();
      return;
    }

    // 1) $ off per point:
    const dollarOffPerPoint = this.pointsRedemptionValue / this.pointsRedemptionAmount;

    // 2) Cart total in store currency (divide cents by 100)
    const cartTotal = (this.cart.total_price || 0) / 100;

    // 4) Points needed to hit that max discount
    const redeemableAmount = this.balance * dollarOffPerPoint;

    const maxRedeemAmount = (this.maxRedeemPercentage * cartTotal) / 100;

    if (redeemableAmount > maxRedeemAmount) {
      this.discountAmount = maxRedeemAmount;
      this.pointsUsed = Math.floor(maxRedeemAmount / dollarOffPerPoint);
    } else {
      this.discountAmount = redeemableAmount;
      this.pointsUsed = this.balance;
    }

    // 7) Update UI text
    this.balanceEl.textContent = this.balance.toLocaleString();
    this.rateEl.textContent = `${this.pointsUsed.toLocaleString()} points = ${this.discountAmount} ${this.currencySymbol} discount`;

    // 8) “To Use” display
    this.toUseEl.textContent = this.allowFull
      ? this.pointsUsed.toLocaleString()
      : Math.min(this.balance, this.pointsUsed).toLocaleString();

    // 9) Sync checkbox state with cart's usingPoints attribute
    if (this.cart && this.cart.attributes) {
      const usingPointsValue = this.cart.attributes.usingPoints;
      // Set checkbox to checked if usingPoints exists and has a truthy value
      this.checkbox.checked = !!(
        usingPointsValue &&
        usingPointsValue !== "0" &&
        usingPointsValue !== "false"
      );
    } else {
      // If no cart attributes, default to unchecked
      this.checkbox.checked = false;
    }

    this.checkbox.disabled = false;
  }

  async onToggle() {
    const use = this.checkbox.checked;

    const pointsUsed = use ? this.pointsUsed : 0;
    const discountValue = use ? this.discountAmount : 0;

    const formData = new FormData();
    formData.append("attributes[usingPoints]", String(pointsUsed));
    formData.append("attributes[discountValue]", String(discountValue));

    try {
      await fetch(window.Shopify.routes.root + "cart/update.js", {
        method: "POST",
        body: formData,
      });

      // Refresh data + re-render
      await this.fetchCartData();
      await this.fetchLoyaltyData();
      this.render();

      // Notify any other listeners
      document.dispatchEvent(new CustomEvent("cart:updated"));
    } catch (e) {
      console.error("Error updating cart:", e);
    }
  }
}

customElements.define("point-redemption", PointRedemptionElement);

/**
 * mountPointRedemption:
 * - Finds the JSON config and the cart container.
 * - Removes any existing <point-redemption> to avoid duplicates.
 * - Creates a new <point-redemption> with required attributes.
 * - Appends it into the container.
 */
function mountPointRedemption() {
  // 1) Locate the JSON config injected via Liquid
  const configEl = document.getElementById("point-redemption-config");
  if (!configEl) {
    // If the config script is missing, nothing to do.
    return;
  }

  let config;
  try {
    config = JSON.parse(configEl.textContent);
  } catch (e) {
    console.error("Invalid JSON in #point-redemption-config:", e);
    return;
  }

  // 2) Locate the cart container where we should mount the component
  const container = document.querySelector(".cart__blocks .js-contents");
  if (!container) {
    // If the cart container isn’t present, bail out.
    return;
  }

  // 3) Remove any existing <point-redemption> to prevent stacking
  const existing = container.querySelector("point-redemption");
  if (existing) {
    existing.remove();
  }

  const el = document.createElement("point-redemption");
  el.setAttribute("allow-full-redemption", "");
  if (config.customer) el.setAttribute("customer", JSON.stringify(config.customer));
  el.setAttribute("customerId", String(config.customerId || ""));
  el.setAttribute("balance", String(config.balance || 0));

  // 5) Append the element into the container
  container.appendChild(el);
}

/*
 * 1) On initial page load, mount the <point-redemption> component.
 * 2) Whenever "cart:updated" fires, remount it to pick up fresh data.
 */
document.addEventListener("DOMContentLoaded", mountPointRedemption);
document.addEventListener("cart:updated", mountPointRedemption);
