{%- comment -%}
  File: extensions/point-redemption/snippets/cart-point-discount.liquid
  Display point discount line in cart
{%- endcomment -%}
{% if cart.attributes.points_used and cart.attributes.points_used != '0' %}
  {% assign discount_amount = cart.attributes.point_discount_amount
    | default: cart.attributes.points_used
    | times: 0.01
  %}
  {% assign points_used = cart.attributes.points_used %}
  <div class="point-discount-line" id="point-discount-line">
    <div class="discount-label">
      Points redemption: {{ points_used }} ({{ discount_amount | money_with_currency }})
    </div>
    <div class="discount-value">-{{ discount_amount | money }}</div>
  </div>
{% endif %}
