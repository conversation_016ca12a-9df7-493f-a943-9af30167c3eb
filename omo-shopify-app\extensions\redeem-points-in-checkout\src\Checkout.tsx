import {
  Banner,
  BlockStack,
  Checkbox,
  reactExtension,
  useApi,
  useApplyAttributeChange,
  useAppMetafields,
  useInstructions,
  useTranslate,
} from "@shopify/ui-extensions-react/checkout";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.reductions.render-after", () => <Extension />);

function Extension() {
  const t = useTranslate();
  const instructions = useInstructions();
  const applyAttributeChange = useApplyAttributeChange();
  const customerMetafields = useAppMetafields({
    type: "customer",
  });
  const { sessionToken, shop, extension } = useApi();

  fetch(`https://educated-deeply-candy-warm.trycloudflare.com/apps/api`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${sessionToken}`,
      "Access-Control-Allow-Origin": "*",
      Accept: "application/json",
    },
  })
    .then((res) => res.json())
    .then((data) => console.log(data))
    .catch((err) => console.error(err));

  const points =
    customerMetafields.find((metafield) => metafield.metafield.key === "points")?.metafield.value ??
    0;

  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="checkout-ui" status="warning">
        {t("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  // 3. Render a UI
  return (
    <BlockStack>
      <Checkbox disabled={Number(points) <= 0} onChange={onCheckboxChange}>
        {t("isUsePoints", { points })}
      </Checkbox>
    </BlockStack>
  );

  async function onCheckboxChange(isChecked) {
    // 4. Call the API to modify checkout
    const result = await applyAttributeChange({
      key: "usingPoints",
      type: "updateAttribute",
      value: isChecked ? points.toString() : "0",
    });
    console.log("applyAttributeChange result", result);
  }
}
