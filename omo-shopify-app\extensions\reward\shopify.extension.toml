# Learn more about configuring your Customer account UI extension:
# https://shopify.dev/api/customer-account-ui-extensions/unstable/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-04"

[[extensions]]
name = "reward"
handle = "reward"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/customer-account-ui-extensions/unstable/extension-targets-overview

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/customer-account-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/customer-account-ui-extensions/unstable/configuration#network-access
network_access = true
customer_privacy = true

[[extensions.targeting]]
module = "./src/ProfilePage.tsx"
target = "customer-account.page.render"

# Access scopes - this is for documentation but won't work until approved
[access_scopes]
scopes = "customer_read_customers"
