import {
  <PERSON><PERSON><PERSON>ck,
  <PERSON><PERSON>,
  <PERSON>,
  DateField,
  Grid,
  Icon,
  InlineStack,
  Modal,
  Page,
  Pressable,
  Select,
  Text,
  View,
  reactExtension,
  useApi,
} from "@shopify/ui-extensions-react/customer-account";

import { useEffect, useState } from "react";

export default reactExtension("customer-account.page.render", () => <PromotionBanner />);

const SHOPIFY_APP_URL = process.env.SHOPIFY_APP_URL;

function PromotionBanner() {
  const { i18n, sessionToken, ui } = useApi();
  const [userName, setUserName] = useState("");
  const [coupons, setCoupons] = useState([]);
  const [initialBirthday, setInitialBirthday] = useState<string>("");
  const [initialGender, setInitialGender] = useState("");
  const [birthday, setBirthday] = useState<string>(null);
  const [gender, setGender] = useState("Prefer not to say");
  const [tier, setTier] = useState("Starter");
  const [point, setPoint] = useState("0");
  const [genderOptions, setGenderOptions] = useState([]);
  const [dateError, setDateError] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const today = new Date();
  const disableDate = new Date(today);
  disableDate.setDate(today.getDate() + 1);

  const handleSave = () => {
    setIsSaving(true);
    updateProfile();
    fetchData();
  };

  const handleDateChange = (newDate: string) => {
    setBirthday(newDate);
    setDateError("");

    // Validate the date
    if (newDate) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      const selectedDate = new Date(newDate);
      const minDate = new Date("1900-01-01");

      if (
        !dateRegex.test(newDate) ||
        isNaN(selectedDate.getTime()) ||
        selectedDate > disableDate ||
        selectedDate < minDate
      ) {
        setDateError("Please select a valid date of your birthday");
        return;
      }
    }
  };

  const getCustomerNameQuery = {
    query: `query {
      customer {
        firstName
        displayName
      }
    }`,
  };

  const fetchData = async () => {
    const token = await sessionToken.get();
    console.log("sessionToken", token);

    fetch(`${SHOPIFY_APP_URL}/api/extensions/discounts`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        setCoupons(data?.discounts);
      })
      .catch(console.error);
  };

  const fetchProfileData = async () => {
    const token = await sessionToken.get();
    console.log("sessionToken", token);

    fetch(`${SHOPIFY_APP_URL}/api/extensions/profile`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        setInitialGender(data?.gender);
        setInitialBirthday(data?.birthday);
        setGender(data?.gender);
        setBirthday(data?.birthday);
        setTier(data?.tier);
        setPoint(data?.currentPoint);
        const genders = data?.genderOptions?.map((item) => ({
          value: item.name,
          label: item.name,
        }));
        setGenderOptions(genders);
      })
      .catch(console.error);
  };

  const updateProfile = async () => {
    const token = await sessionToken.get();
    console.log("sessionToken", token);

    fetch(`${SHOPIFY_APP_URL}/api/extensions/profile`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      },
      body: JSON.stringify({
        gender: gender,
        birthday: birthday,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        setInitialGender(data?.gender);
        setInitialBirthday(data?.birthday);
        setIsSaving(false);
        ui.overlay.close("edit-profile-modal");
        ui.toast.show(i18n.translate("user.toast.profileUpdated"));
      })
      .catch(console.error);
  };

  useEffect(() => {
    fetch("shopify://customer-account/api/2025-04/graphql.json", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(getCustomerNameQuery),
    })
      .then((response) => response.json())
      .then(({ data }) => {
        setUserName(data?.customer?.displayName);
      })
      .catch(console.error);
    fetchProfileData();
    fetchData();
  }, []);

  return (
    <Page title="Rewards">
      <Card>
        <BlockStack spacing="loose" padding="loose">
          <InlineStack spacing="tight" blockAlignment="center">
            <Text size="medium" appearance="accent" emphasis="bold">
              {i18n.translate("user.name")}
            </Text>
            {userName}
          </InlineStack>
          <InlineStack spacing="extraLoose">
            {/* Point Balance Card */}
            <View>
              <Grid
                columns={500}
                rows={100}
                spacing="base"
                background="subdued"
                borderRadius="base"
              >
                <BlockStack spacing="base" padding="base">
                  <Text size="medium" appearance="subdued">
                    {i18n.translate("user.point")}
                  </Text>
                  {point === null ? (
                    <Text size="extraLarge" emphasis="bold">
                      {i18n.translate("user.noPoint")}
                    </Text>
                  ) : (
                    <Text size="extraLarge" emphasis="bold">
                      {i18n.translate("user.customerPoint", { point: point })}
                    </Text>
                  )}
                </BlockStack>
              </Grid>
            </View>

            {/* Current Tier Card */}
            <View inlineSize="fill" borderRadius="small">
              <Grid
                columns={500}
                rows={100}
                spacing="base"
                background="subdued"
                borderRadius="base"
              >
                <BlockStack spacing="base" padding="base">
                  <Text size="medium" appearance="subdued">
                    {i18n.translate("user.tier")}
                  </Text>
                  {tier && (
                    <InlineStack spacing="base" blockAlignment="center">
                      <View padding="tight" background="subdued">
                        <Icon source="star" appearance="accent" />
                      </View>
                      <Text size="large" emphasis="bold">
                        {tier}
                      </Text>
                    </InlineStack>
                  )}
                </BlockStack>
              </Grid>
            </View>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack spacing="base" padding="loose">
          <InlineStack spacing="base">
            <Text size="medium" appearance="accent" emphasis="bold">
              {i18n.translate("user.detail")}
            </Text>
            <Pressable
              overlay={
                <Modal
                  id="edit-profile-modal"
                  title={i18n.translate("user.edit.title")}
                  primaryAction={
                    <Button
                      kind="primary"
                      disabled={!birthday || !!dateError}
                      onPress={handleSave}
                      loading={isSaving}
                    >
                      {i18n.translate("user.edit.save")}
                    </Button>
                  }
                  secondaryActions={
                    <Button kind="secondary" onPress={() => ui.overlay.close("edit-profile-modal")}>
                      {i18n.translate("user.edit.cancel")}
                    </Button>
                  }
                >
                  <BlockStack spacing="base" padding="loose">
                    <DateField
                      label={i18n.translate("user.edit.chooseDate")}
                      value={birthday}
                      disabled={[
                        {
                          start: disableDate.toISOString().split("T")[0],
                        },
                      ]}
                      onChange={handleDateChange}
                      error={dateError}
                    />
                    <Select
                      label={i18n.translate("user.edit.gender")}
                      options={genderOptions}
                      value={gender}
                      onChange={setGender}
                    />
                  </BlockStack>
                </Modal>
              }
            >
              <Icon source="pen" appearance="subdued" />
            </Pressable>
          </InlineStack>

          <InlineStack spacing="extraLoose">
            <BlockStack spacing="none">
              <Text size="base" appearance="subdued">
                {i18n.translate("user.edit.birthday")}
              </Text>
              <Text>{initialBirthday}</Text>
            </BlockStack>
            <View inlineSize="fill" />
            <BlockStack spacing="none">
              <Text size="base" appearance="subdued">
                {i18n.translate("user.edit.gender")}
              </Text>
              <Text>{initialGender}</Text>
            </BlockStack>
          </InlineStack>
        </BlockStack>
      </Card>
      <BlockStack spacing="base">
        <Text size="medium" appearance="accent" emphasis="bold">
          {i18n.translate("user.coupons.title")}
        </Text>

        <InlineStack spacing="loose">
          {coupons.map(({ title, codes, minimumRequirement, endsAt, combinesWith }, index) => (
            <Grid columns={350} rows={250} key={index} spacing="base">
              <Card>
                <View padding="base">
                  <BlockStack spacing="base">
                    <Text emphasis="bold">{title}</Text>
                    <InlineStack inlineAlignment="start">
                      <View padding="tight">
                        {codes.map((code, i) => (
                          <Text size="base" key={i}>
                            {i18n.translate("user.coupons.code")}
                            <Text emphasis="bold">{code}</Text>
                          </Text>
                        ))}
                      </View>
                    </InlineStack>

                    <Text size="small" appearance="subdued">
                      {i18n.translate("user.coupons.expiration")}
                      {endsAt ? endsAt : "LifeTime"}
                    </Text>
                    {minimumRequirement && (
                      <Text size="small" appearance="subdued">
                        {minimumRequirement.greaterThanOrEqualToQuantity &&
                          i18n.translate("user.coupons.minimumQuantity", {
                            number: minimumRequirement.greaterThanOrEqualToQuantity,
                          })}
                        {minimumRequirement.greaterThanOrEqualToSubtotal &&
                          i18n.translate("user.coupons.minimumSubtotal", {
                            amount: minimumRequirement.greaterThanOrEqualToSubtotal.amount,
                          })}
                      </Text>
                    )}
                    {combinesWith &&
                      (combinesWith.shippingDiscounts ||
                        combinesWith.productDiscounts ||
                        combinesWith.orderDiscounts) && (
                        <Text size="small" appearance="subdued">
                          • Combines with {combinesWith.orderDiscounts ? "Order Discount" : ""}
                          {combinesWith.orderDiscounts &&
                          (combinesWith.productDiscounts || combinesWith.shippingDiscounts)
                            ? ", "
                            : ""}
                          {combinesWith.productDiscounts ? "Product Discount" : ""}
                          {combinesWith.productDiscounts && combinesWith.shippingDiscounts
                            ? ", "
                            : ""}
                          {combinesWith.shippingDiscounts ? "Shipping Discount" : ""}
                        </Text>
                      )}
                  </BlockStack>
                </View>
              </Card>
            </Grid>
          ))}
        </InlineStack>
      </BlockStack>
    </Page>
  );
}
