query RunInput {
  cart {
    buyerIdentity {
      customer {
        id
        metafield(namespace: "$app", key: "vip_tier") {
          value
        }
      }
    }
    cost {
      totalAmount {
        amount
        currencyCode
      }
    }
    lines {
      id
      quantity
      cost {
        subtotalAmount {
          amount
        }
      }
    }
  }
  discountNode {
    metafield(namespace: "$app", key: "birthday_discount_config") {
      value
    }
  }
}
