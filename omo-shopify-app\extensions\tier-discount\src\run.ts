import { DiscountApplicationStrategy, FunctionRunResult, Input } from "../generated/api";

interface TierDiscountConfig {
  tierName: string;
  discountCode: string;
  rewardValue: number;
  discountType: "PERCENTAGE" | "FIXED_AMOUNT";
  minimumRequirement?: "AMOUNT" | "QUANTITY";
  minimumValue?: number;
  title: string;
  enabled: boolean;
}

interface BirthdayDiscountConfiguration {
  enabled?: boolean;
  birthday_discount_prefix?: string;
  auto_apply?: boolean;
  tierDiscounts?: TierDiscountConfig[];
}

export function tierBirthdayDiscountRun(input: Input): FunctionRunResult {
  console.log("🎂 Starting tier birthday discount function");

  // Parse configuration from metafield
  let configuration: BirthdayDiscountConfiguration = {};
  try {
    const configValue = input.discountNode.metafield?.value;
    if (configValue) {
      configuration = JSON.parse(configValue);
    }
  } catch (error) {
    console.error("❌ Failed to parse configuration:", error);
    return {
      discountApplicationStrategy: DiscountApplicationStrategy.First,
      discounts: [],
    };
  }

  // Check if extension is enabled
  if (!configuration.enabled) {
    console.log("⚠️ Birthday discount extension is disabled");
    return {
      discountApplicationStrategy: DiscountApplicationStrategy.First,
      discounts: [],
    };
  }

  // Check if auto apply is enabled
  if (!configuration.auto_apply) {
    console.log("⚠️ Auto apply is disabled");
    return {
      discountApplicationStrategy: DiscountApplicationStrategy.First,
      discounts: [],
    };
  }

  // Get customer tier from metafield
  const customerTier = input.cart.buyerIdentity?.customer?.metafield?.value;
  if (!customerTier) {
    console.log("⚠️ No customer tier found or customer not logged in");
    return {
      discountApplicationStrategy: DiscountApplicationStrategy.First,
      discounts: [],
    };
  }

  console.log(`👤 Customer tier detected: ${customerTier}`);

  // Find matching tier discount
  const tierDiscounts = configuration.tierDiscounts || [];
  const matchingDiscount = tierDiscounts.find(
    (discount) => discount.tierName === customerTier && discount.enabled,
  );

  if (!matchingDiscount) {
    console.log(`⚠️ No discount found for tier: ${customerTier}`);
    return {
      discountApplicationStrategy: DiscountApplicationStrategy.First,
      discounts: [],
    };
  }

  console.log(`🎯 Found matching discount for tier ${customerTier}: ${matchingDiscount.title}`);

  // Check minimum requirements if any
  if (matchingDiscount.minimumRequirement && matchingDiscount.minimumValue) {
    const cartTotal = parseFloat(input.cart.cost?.totalAmount?.amount || "0");
    const cartQuantity = input.cart.lines.reduce((total, line) => total + line.quantity, 0);

    if (matchingDiscount.minimumRequirement === "AMOUNT") {
      if (cartTotal < matchingDiscount.minimumValue) {
        console.log(`⚠️ Cart total ${cartTotal} below minimum ${matchingDiscount.minimumValue}`);
        return {
          discountApplicationStrategy: DiscountApplicationStrategy.First,
          discounts: [],
        };
      }
    } else if (matchingDiscount.minimumRequirement === "QUANTITY") {
      if (cartQuantity < matchingDiscount.minimumValue) {
        console.log(
          `⚠️ Cart quantity ${cartQuantity} below minimum ${matchingDiscount.minimumValue}`,
        );
        return {
          discountApplicationStrategy: DiscountApplicationStrategy.First,
          discounts: [],
        };
      }
    }
  }

  const discountMessage = `${matchingDiscount.title} (${customerTier} Tier)`;

  // Create discount value based on type
  const discountValue =
    matchingDiscount.discountType === "PERCENTAGE"
      ? { percentage: { value: matchingDiscount.rewardValue } }
      : { fixedAmount: { amount: matchingDiscount.rewardValue.toString() } };

  console.log(
    `✅ Applying order discount: ${matchingDiscount.rewardValue}${matchingDiscount.discountType === "PERCENTAGE" ? "%" : " fixed"}`,
  );

  // Apply discount to order subtotal
  const discount = {
    message: discountMessage,
    targets: [
      {
        orderSubtotal: {
          excludedVariantIds: [],
        },
      },
    ],
    value: discountValue,
  };

  console.log(`🎉 Applied tier birthday discount for ${customerTier} tier`);

  return {
    discountApplicationStrategy: DiscountApplicationStrategy.First,
    discounts: [discount],
  };
}
