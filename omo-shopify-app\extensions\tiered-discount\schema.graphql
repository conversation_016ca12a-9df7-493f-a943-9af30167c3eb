schema {
  query: Input
  mutation: MutationRoot
}

"""
Scale the Functions resource limits based on the field's length.
"""
directive @scaleLimits(rate: Float!) on FIELD_DEFINITION

"""
Requires that exactly one field must be supplied and that field must not be `null`.
"""
directive @oneOf on INPUT_OBJECT

"""
A custom property. Attributes are used to store additional information about a Shopify resource, such as
products, customers, or orders. Attributes are stored as key-value pairs.

For example, a list of attributes might include whether a customer is a first-time buyer (`"customer_first_order": "true"`),
whether an order is gift-wrapped (`"gift_wrapped": "true"`), a preferred delivery date
(`"preferred_delivery_date": "2025-10-01"`), the discount applied (`"loyalty_discount_applied": "10%"`), and any
notes provided by the customer (`"customer_notes": "Please leave at the front door"`).
"""
type Attribute {
  """
  The key or name of the attribute. For example, `"customer_first_order"`.
  """
  key: String!

  """
  The value of the attribute. For example, `"true"`.
  """
  value: String
}

"""
Information about the customer that's interacting with the cart. It includes details such as the
customer's email and phone number, and the total amount of money the customer has spent in the store.
This information helps personalize the checkout experience and ensures that accurate pricing and delivery options
are displayed to customers.
"""
type BuyerIdentity {
  """
  The customer that's interacting with the cart. A customer is a buyer who has an
  [account](https://help.shopify.com/manual/customers/customer-accounts) with the store.
  """
  customer: Customer

  """
  The email address of the customer that's interacting with the cart.
  """
  email: String

  """
  Whether the customer is authenticated through their
  [customer account](https://help.shopify.com/manual/customers/customer-accounts).
  If the customer is authenticated, then the `customer` field returns the customer's information.
  If the customer isn't authenticated, then the `customer` field returns `null`.
  """
  isAuthenticated: Boolean!

  """
  The phone number of the customer that's interacting with the cart.
  """
  phone: String

  """
  The company of a B2B customer that's interacting with the cart.
  Used to manage and track purchases made by businesses rather than individual customers.
  """
  purchasingCompany: PurchasingCompany
}

"""
The cart where the Function is running. A cart contains the merchandise that a customer intends to purchase
and information about the customer, such as the customer's email address and phone number.
"""
type Cart {
  """
  The custom attributes associated with a cart to store additional information. Cart attributes
  allow you to collect specific information from customers on the **Cart** page, such as order notes,
  gift wrapping requests, or custom product details. Attributes are stored as key-value pairs.
  """
  attribute(
    """
    The key of the cart attribute to retrieve. For example, `"gift_wrapping"`.
    """
    key: String
  ): Attribute

  """
  Information about the customer that's interacting with the cart. It includes details such as the
  customer's email and phone number, and the total amount of money the customer has spent in the store.
  This information helps personalize the checkout experience and ensures that accurate pricing and delivery options
  are displayed to customers.
  """
  buyerIdentity: BuyerIdentity

  """
  A breakdown of the costs that the customer will pay at checkout. It includes the total amount,
  the subtotal before taxes and duties, the tax amount, and duty charges.
  """
  cost: CartCost!

  """
  The items in a cart that are eligible for fulfillment and can be delivered to the customer.
  """
  deliverableLines: [DeliverableCartLine!]!

  """
  A collection of items that are grouped by shared delivery characteristics. Delivery groups streamline
  fulfillment by organizing items that can be shipped together, based on the customer's
  shipping address. For example, if a customer orders a t-shirt and a pair of shoes that can be shipped
  together, then the items are included in the same delivery group.
  """
  deliveryGroups: [CartDeliveryGroup!]!

  """
  The items in a cart that the customer intends to purchase. A cart line is an entry in the
  customer's cart that represents a single unit of a product variant. For example, if a customer adds two
  different sizes of the same t-shirt to their cart, then each size is represented as a separate cart line.
  """
  lines: [CartLine!]! @scaleLimits(rate: 0.005)

  """
  The additional fields on the **Cart** page that are required for international orders in specific countries,
  such as customs information or tax identification numbers.
  """
  localizedFields(
    """
    The keys of the localized fields to retrieve.
    """
    keys: [LocalizedFieldKey!]! = []
  ): [LocalizedField!]!
}

"""
A breakdown of the costs that the customer will pay at checkout. It includes the total amount,
the subtotal before taxes and duties, the tax amount, and duty charges.
"""
type CartCost {
  """
  The amount for the customer to pay at checkout, excluding taxes and discounts.
  """
  subtotalAmount: MoneyV2!

  """
  The total amount for the customer to pay at checkout.
  """
  totalAmount: MoneyV2!

  """
  The duty charges for a customer to pay at checkout.
  """
  totalDutyAmount: MoneyV2

  """
  The total tax amount for the customer to pay at checkout.
  """
  totalTaxAmount: MoneyV2
}

"""
Information about items in a cart that are grouped by shared delivery characteristics.
Delivery groups streamline fulfillment by organizing items that can be shipped together, based on the customer's
shipping address. For example, if a customer orders a t-shirt and a pair of shoes that can be shipped
together, then the items are included in the same delivery group.
"""
type CartDeliveryGroup {
  """
  Information about items in a cart that a customer intends to purchase. A cart line is an entry in the
  customer's cart that represents a single unit of a product variant. For example, if a customer adds two
  different sizes of the same t-shirt to their cart, then each size is represented as a separate cart line.
  """
  cartLines: [CartLine!]! @scaleLimits(rate: 0.005)

  """
  The shipping or destination address associated with the delivery group.
  """
  deliveryAddress: MailingAddress

  """
  The delivery options available for the delivery group. Delivery options are the different ways that customers
  can choose to have their orders shipped. Examples include express shipping or standard shipping.
  """
  deliveryOptions: [CartDeliveryOption!]!

  """
  A [globally-unique ID](https://shopify.dev/docs/api/usage/gids)
  for the delivery group.
  """
  id: ID!

  """
  Information about the delivery option that the customer has selected.
  """
  selectedDeliveryOption: CartDeliveryOption
}

"""
Information about a delivery option that's available for an item in a cart. Delivery options are the different
ways that customers can choose to have their orders shipped. Examples include express shipping or standard
shipping.
"""
type CartDeliveryOption {
  """
  A unique identifier that represents the delivery option offered to customers.
  For example, `Canada Post Expedited`.
  """
  code: String

  """
  The amount that the customer pays if they select the delivery option.
  """
  cost: MoneyV2!

  """
  The delivery method associated with the delivery option. A delivery method is a way that merchants can
  fulfill orders from their online stores. Delivery methods include shipping to an address,
  [local pickup](https://help.shopify.com/manual/fulfillment/setup/delivery-methods/pickup-in-store),
  and shipping to a [pickup point](https://help.shopify.com/manual/fulfillment/shopify-shipping/pickup-points),
  all of which are natively supported by Shopify checkout.
  """
  deliveryMethodType: DeliveryMethod!

  """
  A single-line description of the delivery option, with HTML tags removed.
  """
  description: String

  """
  A unique, human-readable identifier of the delivery option's title.
  A handle can contain letters, hyphens (`-`), and numbers, but not spaces.
  For example, `standard-shipping`.
  """
  handle: Handle!

  """
  The name of the delivery option that displays to customers. The title is used to construct the delivery
  option's handle. For example, if a delivery option is titled "Standard Shipping", then the handle is
  `standard-shipping`.
  """
  title: String
}

"""
Information about an item in a cart that a customer intends to purchase. A cart line is an entry in the
customer's cart that represents a single unit of a product variant. For example, if a customer adds two
different sizes of the same t-shirt to their cart, then each size is represented as a separate cart line.
"""
type CartLine {
  """
  The custom attributes associated with a cart to store additional information. Cart attributes
  allow you to collect specific information from customers on the **Cart** page, such as order notes,
  gift wrapping requests, or custom product details. Attributes are stored as key-value pairs.

  Cart line attributes are equivalent to the
  [`line_item`](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/selling-plans)
  object in Liquid.
  """
  attribute(
    """
    The key of the cart attribute to retrieve. For example, `"gift_wrapping"`.
    """
    key: String
  ): Attribute

  """
  The cost of an item in a cart that the customer intends to purchase. Cart lines are entries in the customer's
  cart that represent a single unit of a product variant. For example, if a customer adds two different sizes of
  the same t-shirt to their cart, then each size is represented as a separate cart line.
  """
  cost: CartLineCost!

  """
  The ID of the cart line.
  """
  id: ID!

  """
  The item that the customer intends to purchase.
  """
  merchandise: Merchandise!

  """
  The quantity of the item that the customer intends to purchase.
  """
  quantity: Int!

  """
  The [selling plan](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/selling-plans)
  associated with the cart line, including information about how a product variant can be sold and purchased.
  """
  sellingPlanAllocation: SellingPlanAllocation
}

"""
The cost of an item in a cart that the customer intends to purchase. Cart lines are entries in the customer's
cart that represent a single unit of a product variant. For example, if a customer adds two different sizes of
the same t-shirt to their cart, then each size is represented as a separate cart line.
"""
type CartLineCost {
  """
  The cost of a single unit. For example, if a customer purchases three units of a product
  that are priced at $10 each, then the `amountPerQuantity` is $10.
  """
  amountPerQuantity: MoneyV2!

  """
  The cost of a single unit before any discounts are applied. This field is used to calculate and display
  savings for customers. For example, if a product's `compareAtAmountPerQuantity` is $25 and its current price
  is $20, then the customer sees a $5 discount. This value can change based on the buyer's identity and is
  `null` when the value is hidden from buyers.
  """
  compareAtAmountPerQuantity: MoneyV2

  """
  The cost of items in the cart before applying any discounts to certain items.
  This amount serves as the starting point for calculating any potential savings customers
  might receive through promotions or discounts.
  """
  subtotalAmount: MoneyV2!

  """
  The total cost of items in a cart.
  """
  totalAmount: MoneyV2!
}

"""
Whether the product is in the specified collection.

A collection is a group of products that can be displayed in online stores and other sales channels in
categories, which makes it easy for customers to find them. For example, an athletics store might create
different collections for running attire and accessories.
"""
type CollectionMembership {
  """
  A [globally-unique ID](https://shopify.dev/docs/api/usage/gids)
  for the collection.
  """
  collectionId: ID!

  """
  Whether the product is in the specified collection.
  """
  isMember: Boolean!
}

"""
Represents information about a company which is also a customer of the shop.
"""
type Company implements HasMetafields {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company was created in Shopify.
  """
  createdAt: DateTime!

  """
  A unique externally-supplied ID for the company.
  """
  externalId: String

  """
  The ID of the company.
  """
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  The name of the company.
  """
  name: String!

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company was last modified.
  """
  updatedAt: DateTime!
}

"""
A company's main point of contact.
"""
type CompanyContact {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company contact was created in Shopify.
  """
  createdAt: DateTime!

  """
  The ID of the company.
  """
  id: ID!

  """
  The company contact's locale (language).
  """
  locale: String

  """
  The company contact's job title.
  """
  title: String

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company contact was last modified.
  """
  updatedAt: DateTime!
}

"""
A company's location.
"""
type CompanyLocation implements HasMetafields {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company location was created in Shopify.
  """
  createdAt: DateTime!

  """
  A unique externally-supplied ID for the company.
  """
  externalId: String

  """
  The ID of the company.
  """
  id: ID!

  """
  The preferred locale of the company location.
  """
  locale: String

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  The name of the company location.
  """
  name: String!

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company location was last modified.
  """
  updatedAt: DateTime!
}

"""
The criteria or rules that must be met for a discount to be applied to an order.
For example, conditions can include minimum purchase amounts, specific product selections, or
customer eligibility.
"""
input Condition @oneOf {
  """
  The condition for checking the minimum subtotal amount of the order.
  """
  orderMinimumSubtotal: OrderMinimumSubtotal

  """
  The condition for checking the minimum quantity of a product.
  """
  productMinimumQuantity: ProductMinimumQuantity

  """
  The condition for checking the minimum subtotal amount of the product.
  """
  productMinimumSubtotal: ProductMinimumSubtotal
}

"""
The country for which the store is customized, reflecting local preferences and regulations.
Localization might influence the language, currency, and product offerings available in a store to enhance
the shopping experience for customers in that region.
"""
type Country {
  """
  The ISO code of the country.
  """
  isoCode: CountryCode!
}

"""
The code designating a country/region, which generally follows ISO 3166-1 alpha-2 guidelines.
If a territory doesn't have a country code value in the `CountryCode` enum, then it might be considered a subdivision
of another country. For example, the territories associated with Spain are represented by the country code `ES`,
and the territories associated with the United States of America are represented by the country code `US`.
"""
enum CountryCode {
  """
  Ascension Island.
  """
  AC

  """
  Andorra.
  """
  AD

  """
  United Arab Emirates.
  """
  AE

  """
  Afghanistan.
  """
  AF

  """
  Antigua & Barbuda.
  """
  AG

  """
  Anguilla.
  """
  AI

  """
  Albania.
  """
  AL

  """
  Armenia.
  """
  AM

  """
  Netherlands Antilles.
  """
  AN

  """
  Angola.
  """
  AO

  """
  Argentina.
  """
  AR

  """
  Austria.
  """
  AT

  """
  Australia.
  """
  AU

  """
  Aruba.
  """
  AW

  """
  Åland Islands.
  """
  AX

  """
  Azerbaijan.
  """
  AZ

  """
  Bosnia & Herzegovina.
  """
  BA

  """
  Barbados.
  """
  BB

  """
  Bangladesh.
  """
  BD

  """
  Belgium.
  """
  BE

  """
  Burkina Faso.
  """
  BF

  """
  Bulgaria.
  """
  BG

  """
  Bahrain.
  """
  BH

  """
  Burundi.
  """
  BI

  """
  Benin.
  """
  BJ

  """
  St. Barthélemy.
  """
  BL

  """
  Bermuda.
  """
  BM

  """
  Brunei.
  """
  BN

  """
  Bolivia.
  """
  BO

  """
  Caribbean Netherlands.
  """
  BQ

  """
  Brazil.
  """
  BR

  """
  Bahamas.
  """
  BS

  """
  Bhutan.
  """
  BT

  """
  Bouvet Island.
  """
  BV

  """
  Botswana.
  """
  BW

  """
  Belarus.
  """
  BY

  """
  Belize.
  """
  BZ

  """
  Canada.
  """
  CA

  """
  Cocos (Keeling) Islands.
  """
  CC

  """
  Congo - Kinshasa.
  """
  CD

  """
  Central African Republic.
  """
  CF

  """
  Congo - Brazzaville.
  """
  CG

  """
  Switzerland.
  """
  CH

  """
  Côte d’Ivoire.
  """
  CI

  """
  Cook Islands.
  """
  CK

  """
  Chile.
  """
  CL

  """
  Cameroon.
  """
  CM

  """
  China.
  """
  CN

  """
  Colombia.
  """
  CO

  """
  Costa Rica.
  """
  CR

  """
  Cuba.
  """
  CU

  """
  Cape Verde.
  """
  CV

  """
  Curaçao.
  """
  CW

  """
  Christmas Island.
  """
  CX

  """
  Cyprus.
  """
  CY

  """
  Czechia.
  """
  CZ

  """
  Germany.
  """
  DE

  """
  Djibouti.
  """
  DJ

  """
  Denmark.
  """
  DK

  """
  Dominica.
  """
  DM

  """
  Dominican Republic.
  """
  DO

  """
  Algeria.
  """
  DZ

  """
  Ecuador.
  """
  EC

  """
  Estonia.
  """
  EE

  """
  Egypt.
  """
  EG

  """
  Western Sahara.
  """
  EH

  """
  Eritrea.
  """
  ER

  """
  Spain.
  """
  ES

  """
  Ethiopia.
  """
  ET

  """
  Finland.
  """
  FI

  """
  Fiji.
  """
  FJ

  """
  Falkland Islands.
  """
  FK

  """
  Faroe Islands.
  """
  FO

  """
  France.
  """
  FR

  """
  Gabon.
  """
  GA

  """
  United Kingdom.
  """
  GB

  """
  Grenada.
  """
  GD

  """
  Georgia.
  """
  GE

  """
  French Guiana.
  """
  GF

  """
  Guernsey.
  """
  GG

  """
  Ghana.
  """
  GH

  """
  Gibraltar.
  """
  GI

  """
  Greenland.
  """
  GL

  """
  Gambia.
  """
  GM

  """
  Guinea.
  """
  GN

  """
  Guadeloupe.
  """
  GP

  """
  Equatorial Guinea.
  """
  GQ

  """
  Greece.
  """
  GR

  """
  South Georgia & South Sandwich Islands.
  """
  GS

  """
  Guatemala.
  """
  GT

  """
  Guinea-Bissau.
  """
  GW

  """
  Guyana.
  """
  GY

  """
  Hong Kong SAR.
  """
  HK

  """
  Heard & McDonald Islands.
  """
  HM

  """
  Honduras.
  """
  HN

  """
  Croatia.
  """
  HR

  """
  Haiti.
  """
  HT

  """
  Hungary.
  """
  HU

  """
  Indonesia.
  """
  ID

  """
  Ireland.
  """
  IE

  """
  Israel.
  """
  IL

  """
  Isle of Man.
  """
  IM

  """
  India.
  """
  IN

  """
  British Indian Ocean Territory.
  """
  IO

  """
  Iraq.
  """
  IQ

  """
  Iran.
  """
  IR

  """
  Iceland.
  """
  IS

  """
  Italy.
  """
  IT

  """
  Jersey.
  """
  JE

  """
  Jamaica.
  """
  JM

  """
  Jordan.
  """
  JO

  """
  Japan.
  """
  JP

  """
  Kenya.
  """
  KE

  """
  Kyrgyzstan.
  """
  KG

  """
  Cambodia.
  """
  KH

  """
  Kiribati.
  """
  KI

  """
  Comoros.
  """
  KM

  """
  St. Kitts & Nevis.
  """
  KN

  """
  North Korea.
  """
  KP

  """
  South Korea.
  """
  KR

  """
  Kuwait.
  """
  KW

  """
  Cayman Islands.
  """
  KY

  """
  Kazakhstan.
  """
  KZ

  """
  Laos.
  """
  LA

  """
  Lebanon.
  """
  LB

  """
  St. Lucia.
  """
  LC

  """
  Liechtenstein.
  """
  LI

  """
  Sri Lanka.
  """
  LK

  """
  Liberia.
  """
  LR

  """
  Lesotho.
  """
  LS

  """
  Lithuania.
  """
  LT

  """
  Luxembourg.
  """
  LU

  """
  Latvia.
  """
  LV

  """
  Libya.
  """
  LY

  """
  Morocco.
  """
  MA

  """
  Monaco.
  """
  MC

  """
  Moldova.
  """
  MD

  """
  Montenegro.
  """
  ME

  """
  St. Martin.
  """
  MF

  """
  Madagascar.
  """
  MG

  """
  North Macedonia.
  """
  MK

  """
  Mali.
  """
  ML

  """
  Myanmar (Burma).
  """
  MM

  """
  Mongolia.
  """
  MN

  """
  Macao SAR.
  """
  MO

  """
  Martinique.
  """
  MQ

  """
  Mauritania.
  """
  MR

  """
  Montserrat.
  """
  MS

  """
  Malta.
  """
  MT

  """
  Mauritius.
  """
  MU

  """
  Maldives.
  """
  MV

  """
  Malawi.
  """
  MW

  """
  Mexico.
  """
  MX

  """
  Malaysia.
  """
  MY

  """
  Mozambique.
  """
  MZ

  """
  Namibia.
  """
  NA

  """
  New Caledonia.
  """
  NC

  """
  Niger.
  """
  NE

  """
  Norfolk Island.
  """
  NF

  """
  Nigeria.
  """
  NG

  """
  Nicaragua.
  """
  NI

  """
  Netherlands.
  """
  NL

  """
  Norway.
  """
  NO

  """
  Nepal.
  """
  NP

  """
  Nauru.
  """
  NR

  """
  Niue.
  """
  NU

  """
  New Zealand.
  """
  NZ

  """
  Oman.
  """
  OM

  """
  Panama.
  """
  PA

  """
  Peru.
  """
  PE

  """
  French Polynesia.
  """
  PF

  """
  Papua New Guinea.
  """
  PG

  """
  Philippines.
  """
  PH

  """
  Pakistan.
  """
  PK

  """
  Poland.
  """
  PL

  """
  St. Pierre & Miquelon.
  """
  PM

  """
  Pitcairn Islands.
  """
  PN

  """
  Palestinian Territories.
  """
  PS

  """
  Portugal.
  """
  PT

  """
  Paraguay.
  """
  PY

  """
  Qatar.
  """
  QA

  """
  Réunion.
  """
  RE

  """
  Romania.
  """
  RO

  """
  Serbia.
  """
  RS

  """
  Russia.
  """
  RU

  """
  Rwanda.
  """
  RW

  """
  Saudi Arabia.
  """
  SA

  """
  Solomon Islands.
  """
  SB

  """
  Seychelles.
  """
  SC

  """
  Sudan.
  """
  SD

  """
  Sweden.
  """
  SE

  """
  Singapore.
  """
  SG

  """
  St. Helena.
  """
  SH

  """
  Slovenia.
  """
  SI

  """
  Svalbard & Jan Mayen.
  """
  SJ

  """
  Slovakia.
  """
  SK

  """
  Sierra Leone.
  """
  SL

  """
  San Marino.
  """
  SM

  """
  Senegal.
  """
  SN

  """
  Somalia.
  """
  SO

  """
  Suriname.
  """
  SR

  """
  South Sudan.
  """
  SS

  """
  São Tomé & Príncipe.
  """
  ST

  """
  El Salvador.
  """
  SV

  """
  Sint Maarten.
  """
  SX

  """
  Syria.
  """
  SY

  """
  Eswatini.
  """
  SZ

  """
  Tristan da Cunha.
  """
  TA

  """
  Turks & Caicos Islands.
  """
  TC

  """
  Chad.
  """
  TD

  """
  French Southern Territories.
  """
  TF

  """
  Togo.
  """
  TG

  """
  Thailand.
  """
  TH

  """
  Tajikistan.
  """
  TJ

  """
  Tokelau.
  """
  TK

  """
  Timor-Leste.
  """
  TL

  """
  Turkmenistan.
  """
  TM

  """
  Tunisia.
  """
  TN

  """
  Tonga.
  """
  TO

  """
  Türkiye.
  """
  TR

  """
  Trinidad & Tobago.
  """
  TT

  """
  Tuvalu.
  """
  TV

  """
  Taiwan.
  """
  TW

  """
  Tanzania.
  """
  TZ

  """
  Ukraine.
  """
  UA

  """
  Uganda.
  """
  UG

  """
  U.S. Outlying Islands.
  """
  UM

  """
  United States.
  """
  US

  """
  Uruguay.
  """
  UY

  """
  Uzbekistan.
  """
  UZ

  """
  Vatican City.
  """
  VA

  """
  St. Vincent & Grenadines.
  """
  VC

  """
  Venezuela.
  """
  VE

  """
  British Virgin Islands.
  """
  VG

  """
  Vietnam.
  """
  VN

  """
  Vanuatu.
  """
  VU

  """
  Wallis & Futuna.
  """
  WF

  """
  Samoa.
  """
  WS

  """
  Kosovo.
  """
  XK

  """
  Yemen.
  """
  YE

  """
  Mayotte.
  """
  YT

  """
  South Africa.
  """
  ZA

  """
  Zambia.
  """
  ZM

  """
  Zimbabwe.
  """
  ZW

  """
  Unknown Region.
  """
  ZZ
}

"""
The three-letter currency codes that represent the world currencies used in stores. Currency codes include
[standard ISO 4217 codes](https://en.wikipedia.org/wiki/ISO_4217), legacy codes, and non-standard codes.
"""
enum CurrencyCode {
  """
  United Arab Emirates Dirham (AED).
  """
  AED

  """
  Afghan Afghani (AFN).
  """
  AFN

  """
  Albanian Lek (ALL).
  """
  ALL

  """
  Armenian Dram (AMD).
  """
  AMD

  """
  Netherlands Antillean Guilder.
  """
  ANG

  """
  Angolan Kwanza (AOA).
  """
  AOA

  """
  Argentine Pesos (ARS).
  """
  ARS

  """
  Australian Dollars (AUD).
  """
  AUD

  """
  Aruban Florin (AWG).
  """
  AWG

  """
  Azerbaijani Manat (AZN).
  """
  AZN

  """
  Bosnia and Herzegovina Convertible Mark (BAM).
  """
  BAM

  """
  Barbadian Dollar (BBD).
  """
  BBD

  """
  Bangladesh Taka (BDT).
  """
  BDT

  """
  Bulgarian Lev (BGN).
  """
  BGN

  """
  Bahraini Dinar (BHD).
  """
  BHD

  """
  Burundian Franc (BIF).
  """
  BIF

  """
  Bermudian Dollar (BMD).
  """
  BMD

  """
  Brunei Dollar (BND).
  """
  BND

  """
  Bolivian Boliviano (BOB).
  """
  BOB

  """
  Brazilian Real (BRL).
  """
  BRL

  """
  Bahamian Dollar (BSD).
  """
  BSD

  """
  Bhutanese Ngultrum (BTN).
  """
  BTN

  """
  Botswana Pula (BWP).
  """
  BWP

  """
  Belarusian Ruble (BYN).
  """
  BYN

  """
  Belarusian Ruble (BYR).
  """
  BYR @deprecated(reason: "`BYR` is deprecated. Use `BYN` available from version `2021-01` onwards instead.")

  """
  Belize Dollar (BZD).
  """
  BZD

  """
  Canadian Dollars (CAD).
  """
  CAD

  """
  Congolese franc (CDF).
  """
  CDF

  """
  Swiss Francs (CHF).
  """
  CHF

  """
  Chilean Peso (CLP).
  """
  CLP

  """
  Chinese Yuan Renminbi (CNY).
  """
  CNY

  """
  Colombian Peso (COP).
  """
  COP

  """
  Costa Rican Colones (CRC).
  """
  CRC

  """
  Cape Verdean escudo (CVE).
  """
  CVE

  """
  Czech Koruny (CZK).
  """
  CZK

  """
  Djiboutian Franc (DJF).
  """
  DJF

  """
  Danish Kroner (DKK).
  """
  DKK

  """
  Dominican Peso (DOP).
  """
  DOP

  """
  Algerian Dinar (DZD).
  """
  DZD

  """
  Egyptian Pound (EGP).
  """
  EGP

  """
  Eritrean Nakfa (ERN).
  """
  ERN

  """
  Ethiopian Birr (ETB).
  """
  ETB

  """
  Euro (EUR).
  """
  EUR

  """
  Fijian Dollars (FJD).
  """
  FJD

  """
  Falkland Islands Pounds (FKP).
  """
  FKP

  """
  United Kingdom Pounds (GBP).
  """
  GBP

  """
  Georgian Lari (GEL).
  """
  GEL

  """
  Ghanaian Cedi (GHS).
  """
  GHS

  """
  Gibraltar Pounds (GIP).
  """
  GIP

  """
  Gambian Dalasi (GMD).
  """
  GMD

  """
  Guinean Franc (GNF).
  """
  GNF

  """
  Guatemalan Quetzal (GTQ).
  """
  GTQ

  """
  Guyanese Dollar (GYD).
  """
  GYD

  """
  Hong Kong Dollars (HKD).
  """
  HKD

  """
  Honduran Lempira (HNL).
  """
  HNL

  """
  Croatian Kuna (HRK).
  """
  HRK

  """
  Haitian Gourde (HTG).
  """
  HTG

  """
  Hungarian Forint (HUF).
  """
  HUF

  """
  Indonesian Rupiah (IDR).
  """
  IDR

  """
  Israeli New Shekel (NIS).
  """
  ILS

  """
  Indian Rupees (INR).
  """
  INR

  """
  Iraqi Dinar (IQD).
  """
  IQD

  """
  Iranian Rial (IRR).
  """
  IRR

  """
  Icelandic Kronur (ISK).
  """
  ISK

  """
  Jersey Pound.
  """
  JEP

  """
  Jamaican Dollars (JMD).
  """
  JMD

  """
  Jordanian Dinar (JOD).
  """
  JOD

  """
  Japanese Yen (JPY).
  """
  JPY

  """
  Kenyan Shilling (KES).
  """
  KES

  """
  Kyrgyzstani Som (KGS).
  """
  KGS

  """
  Cambodian Riel.
  """
  KHR

  """
  Kiribati Dollar (KID).
  """
  KID

  """
  Comorian Franc (KMF).
  """
  KMF

  """
  South Korean Won (KRW).
  """
  KRW

  """
  Kuwaiti Dinar (KWD).
  """
  KWD

  """
  Cayman Dollars (KYD).
  """
  KYD

  """
  Kazakhstani Tenge (KZT).
  """
  KZT

  """
  Laotian Kip (LAK).
  """
  LAK

  """
  Lebanese Pounds (LBP).
  """
  LBP

  """
  Sri Lankan Rupees (LKR).
  """
  LKR

  """
  Liberian Dollar (LRD).
  """
  LRD

  """
  Lesotho Loti (LSL).
  """
  LSL

  """
  Lithuanian Litai (LTL).
  """
  LTL

  """
  Latvian Lati (LVL).
  """
  LVL

  """
  Libyan Dinar (LYD).
  """
  LYD

  """
  Moroccan Dirham.
  """
  MAD

  """
  Moldovan Leu (MDL).
  """
  MDL

  """
  Malagasy Ariary (MGA).
  """
  MGA

  """
  Macedonia Denar (MKD).
  """
  MKD

  """
  Burmese Kyat (MMK).
  """
  MMK

  """
  Mongolian Tugrik.
  """
  MNT

  """
  Macanese Pataca (MOP).
  """
  MOP

  """
  Mauritanian Ouguiya (MRU).
  """
  MRU

  """
  Mauritian Rupee (MUR).
  """
  MUR

  """
  Maldivian Rufiyaa (MVR).
  """
  MVR

  """
  Malawian Kwacha (MWK).
  """
  MWK

  """
  Mexican Pesos (MXN).
  """
  MXN

  """
  Malaysian Ringgits (MYR).
  """
  MYR

  """
  Mozambican Metical.
  """
  MZN

  """
  Namibian Dollar.
  """
  NAD

  """
  Nigerian Naira (NGN).
  """
  NGN

  """
  Nicaraguan Córdoba (NIO).
  """
  NIO

  """
  Norwegian Kroner (NOK).
  """
  NOK

  """
  Nepalese Rupee (NPR).
  """
  NPR

  """
  New Zealand Dollars (NZD).
  """
  NZD

  """
  Omani Rial (OMR).
  """
  OMR

  """
  Panamian Balboa (PAB).
  """
  PAB

  """
  Peruvian Nuevo Sol (PEN).
  """
  PEN

  """
  Papua New Guinean Kina (PGK).
  """
  PGK

  """
  Philippine Peso (PHP).
  """
  PHP

  """
  Pakistani Rupee (PKR).
  """
  PKR

  """
  Polish Zlotych (PLN).
  """
  PLN

  """
  Paraguayan Guarani (PYG).
  """
  PYG

  """
  Qatari Rial (QAR).
  """
  QAR

  """
  Romanian Lei (RON).
  """
  RON

  """
  Serbian dinar (RSD).
  """
  RSD

  """
  Russian Rubles (RUB).
  """
  RUB

  """
  Rwandan Franc (RWF).
  """
  RWF

  """
  Saudi Riyal (SAR).
  """
  SAR

  """
  Solomon Islands Dollar (SBD).
  """
  SBD

  """
  Seychellois Rupee (SCR).
  """
  SCR

  """
  Sudanese Pound (SDG).
  """
  SDG

  """
  Swedish Kronor (SEK).
  """
  SEK

  """
  Singapore Dollars (SGD).
  """
  SGD

  """
  Saint Helena Pounds (SHP).
  """
  SHP

  """
  Sierra Leonean Leone (SLL).
  """
  SLL

  """
  Somali Shilling (SOS).
  """
  SOS

  """
  Surinamese Dollar (SRD).
  """
  SRD

  """
  South Sudanese Pound (SSP).
  """
  SSP

  """
  Sao Tome And Principe Dobra (STD).
  """
  STD @deprecated(reason: "`STD` is deprecated. Use `STN` available from version `2022-07` onwards instead.")

  """
  Sao Tome And Principe Dobra (STN).
  """
  STN

  """
  Syrian Pound (SYP).
  """
  SYP

  """
  Swazi Lilangeni (SZL).
  """
  SZL

  """
  Thai baht (THB).
  """
  THB

  """
  Tajikistani Somoni (TJS).
  """
  TJS

  """
  Turkmenistani Manat (TMT).
  """
  TMT

  """
  Tunisian Dinar (TND).
  """
  TND

  """
  Tongan Pa'anga (TOP).
  """
  TOP

  """
  Turkish Lira (TRY).
  """
  TRY

  """
  Trinidad and Tobago Dollars (TTD).
  """
  TTD

  """
  Taiwan Dollars (TWD).
  """
  TWD

  """
  Tanzanian Shilling (TZS).
  """
  TZS

  """
  Ukrainian Hryvnia (UAH).
  """
  UAH

  """
  Ugandan Shilling (UGX).
  """
  UGX

  """
  United States Dollars (USD).
  """
  USD

  """
  Uruguayan Pesos (UYU).
  """
  UYU

  """
  Uzbekistan som (UZS).
  """
  UZS

  """
  Venezuelan Bolivares (VED).
  """
  VED

  """
  Venezuelan Bolivares (VEF).
  """
  VEF @deprecated(reason: "`VEF` is deprecated. Use `VES` available from version `2020-10` onwards instead.")

  """
  Venezuelan Bolivares Soberanos (VES).
  """
  VES

  """
  Vietnamese đồng (VND).
  """
  VND

  """
  Vanuatu Vatu (VUV).
  """
  VUV

  """
  Samoan Tala (WST).
  """
  WST

  """
  Central African CFA Franc (XAF).
  """
  XAF

  """
  East Caribbean Dollar (XCD).
  """
  XCD

  """
  West African CFA franc (XOF).
  """
  XOF

  """
  CFP Franc (XPF).
  """
  XPF

  """
  Unrecognized currency.
  """
  XXX

  """
  Yemeni Rial (YER).
  """
  YER

  """
  South African Rand (ZAR).
  """
  ZAR

  """
  Zambian Kwacha (ZMW).
  """
  ZMW
}

"""
A custom product represents a product that doesn't map to Shopify's
[standard product categories](https://help.shopify.com/manual/products/details/product-type).
For example, you can use a custom product to manage gift cards, shipping requirements, localized product
information, or weight measurements and conversions.
"""
type CustomProduct {
  """
  Whether the merchandise is a gift card.
  """
  isGiftCard: Boolean!

  """
  Whether the item needs to be shipped to the customer. For example, a
  digital gift card doesn't need to be shipped, but a t-shirt does
  need to be shipped.
  """
  requiresShipping: Boolean!

  """
  The localized name for the product that displays to customers. The title is used to construct the product's
  handle, which is a unique, human-readable string of the product's title. For example, if a product is titled
  "Black Sunglasses", then the handle is `black-sunglasses`.
  """
  title: String!

  """
  The product variant's weight, in the system of measurement set in the `weightUnit` field.
  """
  weight: Float

  """
  The unit of measurement for weight.
  """
  weightUnit: WeightUnit!
}

"""
Represents a [customer](https://help.shopify.com/manual/customers/manage-customers)
who has an [account](https://help.shopify.com/manual/customers/customer-accounts) with the store.
`Customer` returns data including the customer's contact information and order history.
"""
type Customer implements HasMetafields {
  """
  The total amount that the customer has spent on orders.
  The amount is converted from the shop's currency to the currency of the cart using a market rate.
  """
  amountSpent: MoneyV2!

  """
  The full name of the customer, based on the values for `firstName` and `lastName`.
  If `firstName` and `lastName` aren't specified, then the value is the customer's email address.
  If the email address isn't specified, then the value is the customer's phone number.
  """
  displayName: String!

  """
  The customer's email address.
  """
  email: String

  """
  The customer's first name.
  """
  firstName: String

  """
  Whether the customer is associated with any of the specified tags. The customer must have at least one tag
  from the list to return `true`.
  """
  hasAnyTag(
    """
    A comma-separated list of searchable keywords that are associated with the customer. For example,
    `"VIP, Gold"` returns customers with either the `VIP` or `Gold` tag.
    """
    tags: [String!]! = []
  ): Boolean!

  """
  Whether the customer is associated with the specified tags. The customer must have all of the tags in the list
  to return `true`.
  """
  hasTags(
    """
    A comma-separated list of searchable keywords that are associated with the customer. For example,
    `"VIP, Gold"` returns customers with both the `VIP` and `Gold` tags.
    """
    tags: [String!]! = []
  ): [HasTagResponse!]!

  """
  A [globally-unique ID](https://shopify.dev/docs/api/usage/gids)
  for the customer.
  """
  id: ID!

  """
  The customer's last name.
  """
  lastName: String

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  The total number of orders that the customer has made at the store.
  """
  numberOfOrders: Int!
}

"""
Represents an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601)-encoded date string.
For example, September 7, 2019 is represented as `"2019-07-16"`.
"""
scalar Date

"""
Represents an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601)-encoded date and time string.
For example, 3:50 pm on September 7, 2019 in the time zone of UTC (Coordinated Universal Time) is
represented as `"2019-09-07T15:50:00Z`".
"""
scalar DateTime

"""
A subset of the [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format that
includes the date and time but not the timezone which is determined from context.

For example, "2018-01-01T00:00:00".
"""
scalar DateTimeWithoutTimezone

"""
A signed decimal number, which supports arbitrary precision and is serialized as a string.

Example values: `"29.99"`, `"29.999"`.
"""
scalar Decimal

"""
Represents information about the merchandise in the cart.
"""
type DeliverableCartLine {
  """
  The custom attributes associated with a cart to store additional information. Cart attributes
  allow you to collect specific information from customers on the **Cart** page, such as order notes,
  gift wrapping requests, or custom product details. Attributes are stored as key-value pairs.

  Cart line attributes are equivalent to the
  [`line_item`](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/selling-plans)
  object in Liquid.
  """
  attribute(
    """
    The key of the cart attribute to retrieve. For example, `"gift_wrapping"`.
    """
    key: String
  ): Attribute

  """
  The ID of the cart line.
  """
  id: ID!

  """
  The item that the customer intends to purchase.
  """
  merchandise: Merchandise!

  """
  The quantity of the item that the customer intends to purchase.
  """
  quantity: Int!
}

"""
List of different delivery method types.
"""
enum DeliveryMethod {
  """
  Local Delivery.
  """
  LOCAL

  """
  None.
  """
  NONE

  """
  Shipping to a Pickup Point.
  """
  PICKUP_POINT

  """
  Local Pickup.
  """
  PICK_UP

  """
  Retail.
  """
  RETAIL

  """
  Shipping.
  """
  SHIPPING
}

"""
A price reduction applied to an order.

Discounts can be offered in various forms, such as a percentage off, a fixed amount off, or free shipping.
They can be applied automatically at checkout if certain conditions are met, or through a discount code that
customers enter during checkout. Discounts are often used during promotional events or to attract first-time
customers.
"""
input Discount {
  """
  The criteria or rules that must be met for a discount to be applied to an order.
  For example, conditions can include minimum purchase amounts, specific product selections, or
  customer eligibility.
  """
  conditions: [Condition!]

  """
  A notification that informs customers about available discounts.
  The message is displayed on the **Cart** page. For example, "Save 20% on all t-shirts."
  """
  message: String

  """
  The method for applying the discount to an order. This argument accepts either a single
  `OrderSubtotalTarget` or one or more `ProductVariantTarget`s, but not both.

  The `OrderSubtotalTarget` is used to target the subtotal of the order. The subtotal is the total amount
  of the order before any taxes, shipping fees, or discounts are applied. For example, if a customer
  places an order for a t-shirt and a pair of shoes, then the subtotal is the sum of the prices of those items.

  The `ProductVariantTarget` is used to target specific product variants within the order. Product variants
  are the different versions of a product that can be purchased. For example, if a customer orders a t-shirt
  in a specific size and color, the t-shirt is a product variant. The discount can be applied to all
  product variants in the order, or to specific product variants that meet certain criteria.
  """
  targets: [Target!]!

  """
  The value of the discount. The value can be a fixed amount, like $5 (5.0), or a percentage, such as 10% (0.1).
  """
  value: Value!
}

"""
The approach that determines how multiple discounts are evaluated and
applied to a cart. You can apply a discount to the first line item in a cart that meets conditions,
or apply the discount that offers the maximum price reduction.
"""
enum DiscountApplicationStrategy {
  """
  Only apply the discount to the first line item in a cart that meets conditions.
  For example, you can use the `FIRST` strategy to apply a 20% discount to the first line item in a cart
  and ensure no other discounts are applied.
  """
  FIRST

  """
  Only apply the discount that provides the greatest savings. For example, you can use the `MAXIMUM` strategy
  to apply the higher of two discounts. If you have a 20% discount on a $30 t-shirt and a $5 discount on $40
  shoes, then the 20% discount saves you $6 (20% of $30), while the $5 discount stays the same.
  Since $6 is more than $5, the 20% discount on the t-shirt is applied.
  """
  MAXIMUM
}

"""
A discount wrapper node.
"""
type DiscountNode implements HasMetafields {
  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield
}

"""
A fixed amount value.
"""
input FixedAmount {
  """
  The fixed amount value of the discount, in the currency of the cart.

  The amount must be greater than or equal to 0.
  """
  amount: Decimal!
}

"""
The output of the Function run target.
The object contains the list of discounts and strategies to apply to each item
in a cart. In API versions 2023-10 and beyond, this type is deprecated in favor of `FunctionRunResult`.
"""
input FunctionResult {
  """
  The approach that determines how multiple discounts are evaluated and
  applied to a cart. You can apply a discount to the first line item in a cart that meets conditions,
  or apply the discount that offers the maximum price reduction.
  """
  discountApplicationStrategy: DiscountApplicationStrategy!

  """
  The list of discounts that are applied to product variants or line items in a cart.
  It includes data such as the discount value and the message associated with the discount.
  """
  discounts: [Discount!]!
}

"""
The output of the Function run target.
The object contains the list of discounts and strategies to apply to each item
in a cart.
"""
input FunctionRunResult {
  """
  The approach that determines how multiple discounts are evaluated and
  applied to a cart. You can apply a discount to the first line item in a cart that meets conditions,
  or apply the discount that offers the maximum price reduction.
  """
  discountApplicationStrategy: DiscountApplicationStrategy!

  """
  The list of discounts that are applied to product variants or line items in a cart.
  It includes data such as the discount value and the message associated with the discount.
  """
  discounts: [Discount!]!
}

"""
A function-scoped handle to a refer a resource.
The Handle type appears in a JSON response as a String, but it is not intended to be human-readable.
Example value: `"10079785100"`
"""
scalar Handle

"""
Represents information about the metafields associated to the specified resource.
"""
interface HasMetafields {
  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield
}

"""
Whether a Shopify resource, such as a product or customer, has a specified tag.
"""
type HasTagResponse {
  """
  Whether the Shopify resource has the tag.
  """
  hasTag: Boolean!

  """
  A searchable keyword that's associated with a Shopify resource, such as a product or customer. For example,
  a merchant might apply the `sports` and `summer` tags to products that are associated with sportswear for
  summer.
  """
  tag: String!
}

"""
Represents a unique identifier, often used to refetch an object.
The ID type appears in a JSON response as a String, but it is not intended to be human-readable.

Example value: `"gid://shopify/Product/10079785100"`
"""
scalar ID

"""
The input object for the function.
"""
type Input {
  """
  The cart where the Function is running. A cart contains the merchandise that a customer intends to purchase
  and information about the customer, such as the customer's email address and phone number.
  """
  cart: Cart!

  """
  A wrapper around the discount that executes the Function. The `discountNode` field
  enables you to manage [discounts](https://help.shopify.com/manual/discounts),
  which are applied at checkout or on a cart.
  """
  discountNode: DiscountNode!

  """
  The regional and language settings that determine how the Function
  handles currency, numbers, dates, and other locale-specific values
  during discount calculations. These settings are based on the store's configured
  [localization practices](https://shopify.dev/docs/apps/build/functions/localization-practices-shopify-functions).
  """
  localization: Localization!

  """
  The exchange rate used to convert discounts between the shop's default
  currency and the currency that displays to the customer during checkout.
  For example, if a store operates in USD but a customer is viewing discounts in EUR,
  then the presentment currency rate handles this conversion for accurate pricing.
  """
  presentmentCurrencyRate: Decimal!

  """
  Information about the shop where the Function is running, including the shop's timezone
  setting and associated [metafields](https://shopify.dev/docs/apps/build/custom-data).
  """
  shop: Shop!
}

"""
A [JSON](https://www.json.org/json-en.html) object.

Example value:
`{
  "product": {
    "id": "gid://shopify/Product/1346443542550",
    "title": "White T-shirt",
    "options": [{
      "name": "Size",
      "values": ["M", "L"]
    }]
  }
}`
"""
scalar JSON

"""
The language for which the store is customized, ensuring content is tailored to local customers.
This includes product descriptions and customer communications that resonate with the target audience.
"""
type Language {
  """
  The ISO code.
  """
  isoCode: LanguageCode!
}

"""
Language codes supported by Shopify.
"""
enum LanguageCode {
  """
  Afrikaans.
  """
  AF

  """
  Akan.
  """
  AK

  """
  Amharic.
  """
  AM

  """
  Arabic.
  """
  AR

  """
  Assamese.
  """
  AS

  """
  Azerbaijani.
  """
  AZ

  """
  Belarusian.
  """
  BE

  """
  Bulgarian.
  """
  BG

  """
  Bambara.
  """
  BM

  """
  Bangla.
  """
  BN

  """
  Tibetan.
  """
  BO

  """
  Breton.
  """
  BR

  """
  Bosnian.
  """
  BS

  """
  Catalan.
  """
  CA

  """
  Chechen.
  """
  CE

  """
  Central Kurdish.
  """
  CKB

  """
  Czech.
  """
  CS

  """
  Church Slavic.
  """
  CU

  """
  Welsh.
  """
  CY

  """
  Danish.
  """
  DA

  """
  German.
  """
  DE

  """
  Dzongkha.
  """
  DZ

  """
  Ewe.
  """
  EE

  """
  Greek.
  """
  EL

  """
  English.
  """
  EN

  """
  Esperanto.
  """
  EO

  """
  Spanish.
  """
  ES

  """
  Estonian.
  """
  ET

  """
  Basque.
  """
  EU

  """
  Persian.
  """
  FA

  """
  Fulah.
  """
  FF

  """
  Finnish.
  """
  FI

  """
  Filipino.
  """
  FIL

  """
  Faroese.
  """
  FO

  """
  French.
  """
  FR

  """
  Western Frisian.
  """
  FY

  """
  Irish.
  """
  GA

  """
  Scottish Gaelic.
  """
  GD

  """
  Galician.
  """
  GL

  """
  Gujarati.
  """
  GU

  """
  Manx.
  """
  GV

  """
  Hausa.
  """
  HA

  """
  Hebrew.
  """
  HE

  """
  Hindi.
  """
  HI

  """
  Croatian.
  """
  HR

  """
  Hungarian.
  """
  HU

  """
  Armenian.
  """
  HY

  """
  Interlingua.
  """
  IA

  """
  Indonesian.
  """
  ID

  """
  Igbo.
  """
  IG

  """
  Sichuan Yi.
  """
  II

  """
  Icelandic.
  """
  IS

  """
  Italian.
  """
  IT

  """
  Japanese.
  """
  JA

  """
  Javanese.
  """
  JV

  """
  Georgian.
  """
  KA

  """
  Kikuyu.
  """
  KI

  """
  Kazakh.
  """
  KK

  """
  Kalaallisut.
  """
  KL

  """
  Khmer.
  """
  KM

  """
  Kannada.
  """
  KN

  """
  Korean.
  """
  KO

  """
  Kashmiri.
  """
  KS

  """
  Kurdish.
  """
  KU

  """
  Cornish.
  """
  KW

  """
  Kyrgyz.
  """
  KY

  """
  Luxembourgish.
  """
  LB

  """
  Ganda.
  """
  LG

  """
  Lingala.
  """
  LN

  """
  Lao.
  """
  LO

  """
  Lithuanian.
  """
  LT

  """
  Luba-Katanga.
  """
  LU

  """
  Latvian.
  """
  LV

  """
  Malagasy.
  """
  MG

  """
  Māori.
  """
  MI

  """
  Macedonian.
  """
  MK

  """
  Malayalam.
  """
  ML

  """
  Mongolian.
  """
  MN

  """
  Marathi.
  """
  MR

  """
  Malay.
  """
  MS

  """
  Maltese.
  """
  MT

  """
  Burmese.
  """
  MY

  """
  Norwegian (Bokmål).
  """
  NB

  """
  North Ndebele.
  """
  ND

  """
  Nepali.
  """
  NE

  """
  Dutch.
  """
  NL

  """
  Norwegian Nynorsk.
  """
  NN

  """
  Norwegian.
  """
  NO

  """
  Oromo.
  """
  OM

  """
  Odia.
  """
  OR

  """
  Ossetic.
  """
  OS

  """
  Punjabi.
  """
  PA

  """
  Polish.
  """
  PL

  """
  Pashto.
  """
  PS

  """
  Portuguese.
  """
  PT

  """
  Portuguese (Brazil).
  """
  PT_BR

  """
  Portuguese (Portugal).
  """
  PT_PT

  """
  Quechua.
  """
  QU

  """
  Romansh.
  """
  RM

  """
  Rundi.
  """
  RN

  """
  Romanian.
  """
  RO

  """
  Russian.
  """
  RU

  """
  Kinyarwanda.
  """
  RW

  """
  Sanskrit.
  """
  SA

  """
  Sardinian.
  """
  SC

  """
  Sindhi.
  """
  SD

  """
  Northern Sami.
  """
  SE

  """
  Sango.
  """
  SG

  """
  Sinhala.
  """
  SI

  """
  Slovak.
  """
  SK

  """
  Slovenian.
  """
  SL

  """
  Shona.
  """
  SN

  """
  Somali.
  """
  SO

  """
  Albanian.
  """
  SQ

  """
  Serbian.
  """
  SR

  """
  Sundanese.
  """
  SU

  """
  Swedish.
  """
  SV

  """
  Swahili.
  """
  SW

  """
  Tamil.
  """
  TA

  """
  Telugu.
  """
  TE

  """
  Tajik.
  """
  TG

  """
  Thai.
  """
  TH

  """
  Tigrinya.
  """
  TI

  """
  Turkmen.
  """
  TK

  """
  Tongan.
  """
  TO

  """
  Turkish.
  """
  TR

  """
  Tatar.
  """
  TT

  """
  Uyghur.
  """
  UG

  """
  Ukrainian.
  """
  UK

  """
  Urdu.
  """
  UR

  """
  Uzbek.
  """
  UZ

  """
  Vietnamese.
  """
  VI

  """
  Volapük.
  """
  VO

  """
  Wolof.
  """
  WO

  """
  Xhosa.
  """
  XH

  """
  Yiddish.
  """
  YI

  """
  Yoruba.
  """
  YO

  """
  Chinese.
  """
  ZH

  """
  Chinese (Simplified).
  """
  ZH_CN

  """
  Chinese (Traditional).
  """
  ZH_TW

  """
  Zulu.
  """
  ZU
}

"""
The current time based on the
[store's timezone setting](https://help.shopify.com/manual/intro-to-shopify/initial-setup/setup-business-settings).
"""
type LocalTime {
  """
  The current date relative to the parent object.
  """
  date: Date!

  """
  Returns true if the current date and time is at or past the given date and time, and false otherwise.
  """
  dateTimeAfter(
    """
    The date and time to compare against, assumed to be in the timezone of the parent object.
    """
    dateTime: DateTimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current date and time is before the given date and time, and false otherwise.
  """
  dateTimeBefore(
    """
    The date and time to compare against, assumed to be in the timezone of the parent timezone.
    """
    dateTime: DateTimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current date and time is between the two given date and times, and false otherwise.
  """
  dateTimeBetween(
    """
    The upper bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    endDateTime: DateTimeWithoutTimezone!

    """
    The lower bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    startDateTime: DateTimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current time is at or past the given time, and false otherwise.
  """
  timeAfter(
    """
    The time to compare against, assumed to be in the timezone of the parent timezone.
    """
    time: TimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current time is at or past the given time, and false otherwise.
  """
  timeBefore(
    """
    The time to compare against, assumed to be in the timezone of the parent timezone.
    """
    time: TimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current time is between the two given times, and false otherwise.
  """
  timeBetween(
    """
    The upper bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    endTime: TimeWithoutTimezone!

    """
    The lower bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    startTime: TimeWithoutTimezone!
  ): Boolean!
}

"""
Details about the localized experience for the store in a specific region, including country and language
settings. The localized experience is determined by the store's settings and the customer's location.
Localization ensures that customers can access relevant content and options while browsing or purchasing
products in a store.
"""
type Localization {
  """
  The country for which the store is customized, reflecting local preferences and regulations.
  Localization might influence the language, currency, and product offerings available in a store to enhance
  the shopping experience for customers in that region.
  """
  country: Country!

  """
  The language for which the store is customized, ensuring content is tailored to local customers.
  This includes product descriptions and customer communications that resonate with the target audience.
  """
  language: Language!

  """
  The market of the active localized experience.
  """
  market: Market!
}

"""
Represents the value captured by a localized field. Localized fields are
additional fields required by certain countries on international orders. For
example, some countries require additional fields for customs information or tax
identification numbers.
"""
type LocalizedField {
  """
  The key of the localized field.
  """
  key: LocalizedFieldKey!

  """
  The title of the localized field.
  """
  title: String!

  """
  The value of the localized field.
  """
  value: String
}

"""
Unique key identifying localized fields.
"""
enum LocalizedFieldKey {
  """
  Localized field key 'shipping_credential_br' for country Brazil.
  """
  SHIPPING_CREDENTIAL_BR

  """
  Localized field key 'shipping_credential_cl' for country Chile.
  """
  SHIPPING_CREDENTIAL_CL

  """
  Localized field key 'shipping_credential_cn' for country China.
  """
  SHIPPING_CREDENTIAL_CN

  """
  Localized field key 'shipping_credential_co' for country Colombia.
  """
  SHIPPING_CREDENTIAL_CO

  """
  Localized field key 'shipping_credential_cr' for country Costa Rica.
  """
  SHIPPING_CREDENTIAL_CR

  """
  Localized field key 'shipping_credential_ec' for country Ecuador.
  """
  SHIPPING_CREDENTIAL_EC

  """
  Localized field key 'shipping_credential_es' for country Spain.
  """
  SHIPPING_CREDENTIAL_ES

  """
  Localized field key 'shipping_credential_gt' for country Guatemala.
  """
  SHIPPING_CREDENTIAL_GT

  """
  Localized field key 'shipping_credential_id' for country Indonesia.
  """
  SHIPPING_CREDENTIAL_ID

  """
  Localized field key 'shipping_credential_kr' for country South Korea.
  """
  SHIPPING_CREDENTIAL_KR

  """
  Localized field key 'shipping_credential_mx' for country Mexico.
  """
  SHIPPING_CREDENTIAL_MX

  """
  Localized field key 'shipping_credential_my' for country Malaysia.
  """
  SHIPPING_CREDENTIAL_MY

  """
  Localized field key 'shipping_credential_pe' for country Peru.
  """
  SHIPPING_CREDENTIAL_PE

  """
  Localized field key 'shipping_credential_pt' for country Portugal.
  """
  SHIPPING_CREDENTIAL_PT

  """
  Localized field key 'shipping_credential_py' for country Paraguay.
  """
  SHIPPING_CREDENTIAL_PY

  """
  Localized field key 'shipping_credential_tr' for country Turkey.
  """
  SHIPPING_CREDENTIAL_TR

  """
  Localized field key 'shipping_credential_tw' for country Taiwan.
  """
  SHIPPING_CREDENTIAL_TW

  """
  Localized field key 'shipping_credential_type_co' for country Colombia.
  """
  SHIPPING_CREDENTIAL_TYPE_CO

  """
  Localized field key 'tax_credential_br' for country Brazil.
  """
  TAX_CREDENTIAL_BR

  """
  Localized field key 'tax_credential_cl' for country Chile.
  """
  TAX_CREDENTIAL_CL

  """
  Localized field key 'tax_credential_co' for country Colombia.
  """
  TAX_CREDENTIAL_CO

  """
  Localized field key 'tax_credential_cr' for country Costa Rica.
  """
  TAX_CREDENTIAL_CR

  """
  Localized field key 'tax_credential_ec' for country Ecuador.
  """
  TAX_CREDENTIAL_EC

  """
  Localized field key 'tax_credential_es' for country Spain.
  """
  TAX_CREDENTIAL_ES

  """
  Localized field key 'tax_credential_gt' for country Guatemala.
  """
  TAX_CREDENTIAL_GT

  """
  Localized field key 'tax_credential_id' for country Indonesia.
  """
  TAX_CREDENTIAL_ID

  """
  Localized field key 'tax_credential_it' for country Italy.
  """
  TAX_CREDENTIAL_IT

  """
  Localized field key 'tax_credential_mx' for country Mexico.
  """
  TAX_CREDENTIAL_MX

  """
  Localized field key 'tax_credential_my' for country Malaysia.
  """
  TAX_CREDENTIAL_MY

  """
  Localized field key 'tax_credential_pe' for country Peru.
  """
  TAX_CREDENTIAL_PE

  """
  Localized field key 'tax_credential_pt' for country Portugal.
  """
  TAX_CREDENTIAL_PT

  """
  Localized field key 'tax_credential_py' for country Paraguay.
  """
  TAX_CREDENTIAL_PY

  """
  Localized field key 'tax_credential_tr' for country Turkey.
  """
  TAX_CREDENTIAL_TR

  """
  Localized field key 'tax_credential_type_co' for country Colombia.
  """
  TAX_CREDENTIAL_TYPE_CO

  """
  Localized field key 'tax_credential_type_mx' for country Mexico.
  """
  TAX_CREDENTIAL_TYPE_MX

  """
  Localized field key 'tax_credential_use_mx' for country Mexico.
  """
  TAX_CREDENTIAL_USE_MX

  """
  Localized field key 'tax_email_it' for country Italy.
  """
  TAX_EMAIL_IT
}

"""
Represents a mailing address.
"""
type MailingAddress {
  """
  The first line of the address. Typically the street address or PO Box number.
  """
  address1: String

  """
  The second line of the address. Typically the number of the apartment, suite, or unit.
  """
  address2: String

  """
  The name of the city, district, village, or town.
  """
  city: String

  """
  The name of the customer's company or organization.
  """
  company: String

  """
  The two-letter code for the country of the address. For example, US.
  """
  countryCode: CountryCode

  """
  The first name of the customer.
  """
  firstName: String

  """
  The last name of the customer.
  """
  lastName: String

  """
  The approximate latitude of the address.
  """
  latitude: Float

  """
  The approximate longitude of the address.
  """
  longitude: Float

  """
  The market of the address.
  """
  market: Market

  """
  The full name of the customer, based on firstName and lastName.
  """
  name: String

  """
  A unique phone number for the customer. Formatted using E.164 standard. For example, +16135551111.
  """
  phone: String

  """
  The alphanumeric code for the region. For example, ON.
  """
  provinceCode: String

  """
  The zip or postal code of the address.
  """
  zip: String
}

"""
A market is a group of one or more regions that you want to target for international sales.
By creating a market, you can configure a distinct, localized shopping experience for
customers from a specific area of the world. For example, you can
[change currency](https://shopify.dev/api/admin-graphql/current/mutations/marketCurrencySettingsUpdate),
[configure international pricing](https://shopify.dev/api/examples/product-price-lists),
or [add market-specific domains or subfolders](https://shopify.dev/api/admin-graphql/current/objects/MarketWebPresence).
"""
type Market implements HasMetafields {
  """
  A human-readable unique string for the market automatically generated from its title.
  """
  handle: Handle!

  """
  A globally-unique identifier.
  """
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  A geographic region which comprises a market.
  """
  regions: [MarketRegion!]!
}

"""
Represents a region.
"""
interface MarketRegion {
  """
  The name of the region in the language of the current localization.
  """
  name: String
}

"""
A country which comprises a market.
"""
type MarketRegionCountry implements MarketRegion {
  """
  The two-letter code for the country.
  """
  code: CountryCode!

  """
  The country name in the language of the current localization.
  """
  name: String!
}

"""
The item that a customer intends to purchase. Merchandise can be a product variant or a custom
product.

A product variant is a specific version of a product that comes in more than one option, such as size or color.
For example, if a merchant sells t-shirts with options for size and color, then a small, blue t-shirt would be
one product variant and a large, blue t-shirt would be another.

A custom product represents a product that doesn't map to Shopify's
[standard product categories](https://help.shopify.com/manual/products/details/product-type).
For example, you can use a custom product to manage gift cards, shipping requirements, localized product
information, or weight measurements and conversions.
"""
union Merchandise = CustomProduct | ProductVariant

"""
[Custom fields](https://shopify.dev/docs/apps/build/custom-data) that store additional information
about a Shopify resource, such as products, orders, and
[many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
enables you to customize the checkout experience.
"""
type Metafield {
  """
  The data that's stored in the metafield, using JSON format.
  """
  jsonValue: JSON!

  """
  The [type of data](https://shopify.dev/apps/metafields/types) that the metafield stores in
  the `value` field.
  """
  type: String!

  """
  The data that's stored in the metafield. The data is always stored as a string,
  regardless of the [metafield's type](https://shopify.dev/apps/metafields/types).
  """
  value: String!
}

"""
A precise monetary value and its associated currency. For example, 12.99 USD.
"""
type MoneyV2 {
  """
  A monetary value in decimal format, allowing for precise representation of cents or fractional
  currency. For example, 12.99.
  """
  amount: Decimal!

  """
  The three-letter currency code that represents a world currency used in a store. Currency codes
  include standard [standard ISO 4217 codes](https://en.wikipedia.org/wiki/ISO_4217), legacy codes,
  and non-standard codes. For example, USD.
  """
  currencyCode: CurrencyCode!
}

"""
The root mutation for the API.
"""
type MutationRoot {
  """
  Handles the Function result.
  """
  handleResult(
    """
    The result of the Function.
    """
    result: FunctionResult!
  ): Void! @deprecated(reason: "Use the target-specific field instead.")

  """
  Handles the Function result for the purchase.order-discount.run target.
  """
  run(
    """
    The result of the Function.
    """
    result: FunctionRunResult!
  ): Void!
}

"""
The condition for checking the minimum subtotal amount of the order.
"""
input OrderMinimumSubtotal {
  """
  Variant IDs with a merchandise line price that's excluded to calculate the minimum subtotal amount of the order.
  """
  excludedVariantIds: [ID!]!

  """
  The minimum subtotal amount of the order.
  """
  minimumAmount: Decimal!

  """
  The target type of the condition.

   The value is validated against: = "ORDER_SUBTOTAL".
  """
  targetType: TargetType!
}

"""
A method for applying a discount to the entire order subtotal. The subtotal is the total amount of the
order before any taxes, shipping fees, or discounts are applied. For example, if a customer places an order
for a t-shirt and a pair of shoes, then the subtotal is the sum of the prices of those items.
"""
input OrderSubtotalTarget {
  """
  The list of excluded product variant IDs. Cart lines for these product variants are excluded from the order
  subtotal calculation when calculating the maximum value of the discount.
  """
  excludedVariantIds: [ID!]!
}

"""
A percentage value.
"""
input Percentage {
  """
  The percentage value.

  The value is validated against: >= 0 and <= 100.
  """
  value: Decimal!
}

"""
The goods and services that merchants offer to customers. Products can include details such as
title, vendor, and custom data stored in [metafields](https://shopify.dev/docs/apps/build/custom-data).
Products can be organized by grouping them into a collection.

Learn more about [managing products in a merchant's store](https://help.shopify.com/manual/products).
"""
type Product implements HasMetafields {
  """
  A unique, human-readable string of the product's title. A handle can contain letters, hyphens (`-`), and
  numbers, but not spaces. The handle is used in the online store URL for the product. For example, if a product
  is titled "Black Sunglasses", then the handle is `black-sunglasses`.
  """
  handle: Handle!

  """
  Whether the product is associated with any of the specified tags. The product must have at least one tag
  from the list to return `true`.
  """
  hasAnyTag(
    """
    A comma-separated list of searchable keywords that are associated with the product. For example,
    `"sports, summer"` returns products with either the `sports` or `summer` tag.
    """
    tags: [String!]! = []
  ): Boolean!

  """
  Whether the product is associated with the specified tags. The product must have all of the tags in the list
  to return `true`.
  """
  hasTags(
    """
    A comma-separated list of searchable keywords that are associated with the product. For example,
    `"sports, summer"` returns products with both the `sports` and `summer` tags.
    """
    tags: [String!]! = []
  ): [HasTagResponse!]!

  """
  A [globally-unique ID](https://shopify.dev/docs/api/usage/gids)
  for the product.
  """
  id: ID!

  """
  Whether the product is in any of the specified collections. The product must be in at least one collection
  from the list to return `true`.

  A collection is a group of products that can be displayed in online stores and other sales channels in
  categories, which makes it easy for customers to find them. For example, an athletics store might create
  different collections for running attire and accessories.
  """
  inAnyCollection(
    """
    A comma-separated list of [globally-unique collection IDs](https://shopify.dev/docs/api/usage/gids)
    that are associated with the product. For example, `gid://shopify/Collection/123`, `gid://shopify/Collection/456`.
    """
    ids: [ID!]! = []
  ): Boolean!

  """
  Whether the product is in the specified collections. The product must be in all of the collections in the
  list to return `true`.

  A collection is a group of products that can be displayed in online stores and other sales channels in
  categories, which makes it easy for customers to find them. For example, an athletics store might create
  different collections for running attire and accessories.
  """
  inCollections(
    """
    A comma-separated list of [globally-unique collection IDs](https://shopify.dev/docs/api/usage/gids)
    that are associated with the product. For example, `gid://shopify/Collection/123`, `gid://shopify/Collection/456`.
    """
    ids: [ID!]! = []
  ): [CollectionMembership!]!

  """
  Whether the product is a gift card.
  """
  isGiftCard: Boolean!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  A custom category for a product. Product types allow merchants to define categories other than the
  ones available in Shopify's
  [standard product categories](https://help.shopify.com/manual/products/details/product-type).
  """
  productType: String

  """
  The localized name for the product that displays to customers. The title is used to construct the product's
  handle, which is a unique, human-readable string of the product's title. For example, if a product is titled
  "Black Sunglasses", then the handle is `black-sunglasses`.
  """
  title: String!

  """
  The name of the product's vendor.
  """
  vendor: String
}

"""
The condition for checking the minimum quantity of a product.
"""
input ProductMinimumQuantity {
  """
  Variant IDs with a merchandise line price that's included to calculate the minimum quantity of the product.
  """
  ids: [ID!]!

  """
  The minimum quantity of a product.
  """
  minimumQuantity: Int!

  """
  The target type of the condition.

  The value is validated against: = "PRODUCT_VARIANT".
  """
  targetType: TargetType!
}

"""
The condition for checking the minimum subtotal amount of the product.
"""
input ProductMinimumSubtotal {
  """
  Variant IDs with a merchandise line price that's included to calculate the minimum subtotal amount of a product.
  """
  ids: [ID!]!

  """
  The minimum subtotal amount of the product.
  """
  minimumAmount: Decimal!

  """
  The target type of the condition.

  The value is validated against: = "PRODUCT_VARIANT".
  """
  targetType: TargetType!
}

"""
A specific version of a product that comes in more than one option, such as size or color. For example,
if a merchant sells t-shirts with options for size and color, then a small, blue t-shirt would be one
product variant and a large, blue t-shirt would be another.
"""
type ProductVariant implements HasMetafields {
  """
  A [globally-unique ID](https://shopify.dev/docs/api/usage/gids)
  for the product variant.
  """
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  The product associated with the product variant. For example, if a
  merchant sells t-shirts with options for size and color, then a small,
  blue t-shirt would be one product variant and a large, blue t-shirt would be another.
  The product associated with the product variant would be the t-shirt itself.
  """
  product: Product!

  """
  Whether the item needs to be shipped to the customer. For example, a
  digital gift card doesn't need to be shipped, but a t-shirt does
  need to be shipped.
  """
  requiresShipping: Boolean!

  """
  A case-sensitive identifier for the product variant in the merchant's store. For example, `"BBC-1"`.
  A product variant must have a SKU to be connected to a
  [fulfillment service](https://shopify.dev/docs/apps/build/orders-fulfillment/fulfillment-service-apps/build-for-fulfillment-services).
  """
  sku: String

  """
  The localized name for the product variant that displays to customers.
  """
  title: String

  """
  The product variant's weight, in the system of measurement set in the `weightUnit` field.
  """
  weight: Float

  """
  The unit of measurement for weight.
  """
  weightUnit: WeightUnit!
}

"""
A method for applying a discount to a
[product variant](https://help.shopify.com/manual/products/variants). Product variants
are the different versions of a product that can be purchased. For example, if a customer orders a t-shirt
in a specific size and color, the t-shirt is a product variant. The discount can be applied to all
product variants in the order, or to specific product variants that meet certain criteria.
"""
input ProductVariantTarget {
  """
  The ID of the targeted product variant.
  """
  id: ID!

  """
  The maximum number of line item units to be discounted.
  The default value is `null`, which represents the total quantity of the matching line items.

  The value is validated against: > 0.
  """
  quantity: Int
}

"""
The company of a B2B customer that's interacting with the cart.
Used to manage and track purchases made by businesses rather than individual customers.
"""
type PurchasingCompany {
  """
  The company associated to the order or draft order.
  """
  company: Company!

  """
  The company contact associated to the order or draft order.
  """
  contact: CompanyContact

  """
  The company location associated to the order or draft order.
  """
  location: CompanyLocation!
}

"""
Represents how products and variants can be sold and purchased.
"""
type SellingPlan implements HasMetafields {
  """
  The description of the selling plan.
  """
  description: String

  """
  A globally-unique identifier.
  """
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield

  """
  The name of the selling plan. For example, '6 weeks of prepaid granola, delivered weekly'.
  """
  name: String!

  """
  Whether purchasing the selling plan will result in multiple deliveries.
  """
  recurringDeliveries: Boolean!
}

"""
Represents an association between a variant and a selling plan. Selling plan
allocations describe the options offered for each variant, and the price of the
variant when purchased with a selling plan.
"""
type SellingPlanAllocation {
  """
  A list of price adjustments, with a maximum of two. When there are two, the
  first price adjustment goes into effect at the time of purchase, while the
  second one starts after a certain number of orders. A price adjustment
  represents how a selling plan affects pricing when a variant is purchased with
  a selling plan. Prices display in the customer's currency if the shop is
  configured for it.
  """
  priceAdjustments: [SellingPlanAllocationPriceAdjustment!]!

  """
  A representation of how products and variants can be sold and purchased. For
  example, an individual selling plan could be '6 weeks of prepaid granola,
  delivered weekly'.
  """
  sellingPlan: SellingPlan!
}

"""
The resulting prices for variants when they're purchased with a specific selling plan.
"""
type SellingPlanAllocationPriceAdjustment {
  """
  The effective price for a single delivery. For example, for a prepaid
  subscription plan that includes 6 deliveries at the price of $48.00, the per
  delivery price is $8.00.
  """
  perDeliveryPrice: MoneyV2!

  """
  The price of the variant when it's purchased with a selling plan For example,
  for a prepaid subscription plan that includes 6 deliveries of $10.00 granola,
  where the customer gets 20% off, the price is 6 x $10.00 x 0.80 = $48.00.
  """
  price: MoneyV2!
}

"""
Information about the store, including the store's timezone setting
and custom data stored in [metafields](https://shopify.dev/docs/apps/build/custom-data).
"""
type Shop implements HasMetafields {
  """
  The current time based on the
  [store's timezone setting](https://help.shopify.com/manual/intro-to-shopify/initial-setup/setup-business-settings).
  """
  localTime: LocalTime!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data) that stores additional information
  about a Shopify resource, such as products, orders, and
  [many more](https://shopify.dev/docs/api/admin-graphql/latest/enums/MetafieldOwnerType).
  Using [metafields with Shopify Functions](https://shopify.dev/docs/apps/build/functions/input-output/metafields-for-input-queries)
  enables you to customize the checkout experience.
  """
  metafield(
    """
    The unique identifier for the metafield within its namespace. A metafield is composed of a
    namespace and a key, in the format `namespace.key`.
    """
    key: String!

    """
    A category that organizes a group of metafields. Namespaces are used to prevent naming conflicts
    between different apps or different parts of the same app. If omitted, then the
    [app-reserved namespace](https://shopify.dev/docs/apps/build/custom-data/ownership)
    is used.
    """
    namespace: String
  ): Metafield
}

"""
The method for applying the discount to an order. This argument accepts either a single
`OrderSubtotalTarget` or one or more `ProductVariantTarget`s, but not both.
"""
input Target @oneOf {
  """
  A method for applying a discount to the entire order subtotal. The subtotal is the total amount of the
  order before any taxes, shipping fees, or discounts are applied. For example, if a customer places an order
  for a t-shirt and a pair of shoes, then the subtotal is the sum of the prices of those items.
  """
  orderSubtotal: OrderSubtotalTarget

  """
  A method for applying a discount to a
  [product variant](https://help.shopify.com/manual/products/variants). Product variants
  are the different versions of a product that can be purchased. For example, if a customer orders a t-shirt
  in a specific size and color, the t-shirt is a product variant. The discount can be applied to all
  product variants in the order, or to specific product variants that meet certain criteria.
  """
  productVariant: ProductVariantTarget
}

"""
The target type of a condition.
"""
enum TargetType {
  """
  The target is the subtotal of the order.
  """
  ORDER_SUBTOTAL

  """
  The target is a product variant.
  """
  PRODUCT_VARIANT
}

"""
A subset of the [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format that
includes the time but not the date or timezone which is determined from context.
For example, "05:43:21".
"""
scalar TimeWithoutTimezone

"""
The value of the discount. The value can be a fixed amount, like $5 (5.0), or a percentage, such as 10% (0.1).
"""
input Value @oneOf {
  """
  A fixed amount value.
  """
  fixedAmount: FixedAmount

  """
  A percentage value.
  """
  percentage: Percentage
}

"""
A void type that can be used to return a null value from a mutation.
"""
scalar Void

"""
Units of measurement for weight.
"""
enum WeightUnit {
  """
  Metric system unit of mass.
  """
  GRAMS

  """
  1 kilogram equals 1000 grams.
  """
  KILOGRAMS

  """
  Imperial system unit of mass.
  """
  OUNCES

  """
  1 pound equals 16 ounces.
  """
  POUNDS
}
