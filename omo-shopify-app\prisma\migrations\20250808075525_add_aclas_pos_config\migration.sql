-- CreateTable
CREATE TABLE `Session` (
    `id` VARCHAR(191) NOT NULL,
    `shop` VARCHAR(191) NOT NULL,
    `state` VARCHAR(191) NOT NULL,
    `isOnline` BOOLEAN NOT NULL DEFAULT false,
    `scope` TEXT NULL,
    `expires` DATETIME(3) NULL,
    `accessToken` VARCHAR(191) NOT NULL,
    `userId` BIGINT NULL,
    `firstName` VARCHAR(191) NULL,
    `lastName` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `accountOwner` BOOLEAN NOT NULL DEFAULT false,
    `locale` VARCHAR(191) NULL,
    `collaborator` <PERSON><PERSON><PERSON><PERSON>N NULL DEFAULT false,
    `emailVerified` BOOLEAN NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `MembershipCancel` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `customerId` VARCHAR(191) NOT NULL,
    `cellphone` VARCHAR(191) NOT NULL,
    `cancelDate` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Shop` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` VARCHAR(191) NOT NULL,
    `shopName` VARCHAR(191) NOT NULL,
    `myshopifyDomain` VARCHAR(191) NOT NULL,
    `shopToken` VARCHAR(191) NULL,
    `apiKey` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShopCreditLoyaltyProgram` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT false,
    `singularName` VARCHAR(191) NOT NULL,
    `pluralName` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ShopCreditLoyaltyProgram_shopId_key`(`shopId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SalesOrders` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `orderPosId` VARCHAR(191) NOT NULL,
    `ordShopiId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `SalesOrders_orderPosId_key`(`orderPosId`),
    UNIQUE INDEX `SalesOrders_ordShopiId_key`(`ordShopiId`),
    INDEX `SalesOrders_orderPosId_idx`(`orderPosId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ReturnOrders` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `orderId` VARCHAR(191) NOT NULL,
    `retOrdShopId` VARCHAR(191) NOT NULL,
    `salesOrdId` INTEGER NOT NULL,

    UNIQUE INDEX `ReturnOrders_orderId_key`(`orderId`),
    UNIQUE INDEX `ReturnOrders_retOrdShopId_key`(`retOrdShopId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyProgram` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `programType` ENUM('POINTS', 'VIP_TIER', 'STORE_CREDIT', 'REFERRAL') NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `LoyaltyProgram_shopId_programType_key`(`shopId`, `programType`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyPoints` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `loyaltyProgramId` INTEGER NOT NULL,
    `programName` VARCHAR(191) NOT NULL,
    `pointSingular` VARCHAR(191) NOT NULL DEFAULT 'Point',
    `pointPlural` VARCHAR(191) NOT NULL DEFAULT 'Points',
    `pointsPerCurrency` DOUBLE NOT NULL DEFAULT 1.0,
    `currencyAmount` DOUBLE NOT NULL DEFAULT 1.0,
    `pointsRedemptionAmount` DOUBLE NOT NULL DEFAULT 100,
    `pointsRedemptionValue` DOUBLE NOT NULL DEFAULT 0.01,
    `maxRedeemPercentage` INTEGER NOT NULL DEFAULT 30,
    `minPurchaseAmount` DOUBLE NULL,
    `roundingMethod` VARCHAR(191) NULL,
    `isConvertPointsByPercentage` BOOLEAN NULL DEFAULT false,
    `includeProductTotal` BOOLEAN NOT NULL DEFAULT true,
    `includeShipping` BOOLEAN NOT NULL DEFAULT false,
    `includeTaxes` BOOLEAN NOT NULL DEFAULT false,
    `pointsIssueType` ENUM('IMMEDIATE', 'DELAYED') NOT NULL DEFAULT 'DELAYED',
    `issueDays` INTEGER NOT NULL DEFAULT 14,
    `orderStatus` ENUM('PAID_FULFILLED', 'PAID') NOT NULL DEFAULT 'PAID_FULFILLED',
    `orderRefundType` ENUM('PROPORTIONAL', 'FULL', 'NONE') NOT NULL DEFAULT 'PROPORTIONAL',
    `redeemedRefundType` ENUM('PROPORTIONAL', 'FULL', 'NONE') NOT NULL DEFAULT 'PROPORTIONAL',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `LoyaltyPoints_loyaltyProgramId_key`(`loyaltyProgramId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyVIPSettings` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `loyaltyProgramId` INTEGER NOT NULL,
    `entryMethod` VARCHAR(191) NOT NULL DEFAULT 'points',
    `ordersCount` BOOLEAN NOT NULL DEFAULT false,
    `validationType` VARCHAR(191) NOT NULL DEFAULT 'immediately',
    `validationDays` INTEGER NOT NULL DEFAULT 0,
    `expirationType` VARCHAR(191) NOT NULL DEFAULT 'lifetime',
    `expirationDays` INTEGER NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `LoyaltyVIPSettings_loyaltyProgramId_key`(`loyaltyProgramId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyVIPTier` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `loyaltyProgramId` INTEGER NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `spendRequirement` DOUBLE NULL,
    `pointsRequirement` INTEGER NULL,
    `spendAmount` INTEGER NULL,
    `pointEarn` INTEGER NULL,
    `basedOnDiffTier` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyVIPReward` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `vipTierId` INTEGER NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `rewardType` ENUM('POINTS', 'STORE_CREDIT', 'AMOUNT_OFF', 'FREE_SHIPPING') NOT NULL,
    `value` VARCHAR(191) NULL,
    `discountType` ENUM('PERCENTAGE', 'FIXED') NULL,
    `minimumRequirement` ENUM('NONE', 'AMOUNT', 'QUANTITY') NULL,
    `minimumValue` DOUBLE NULL,
    `productDiscounts` BOOLEAN NULL,
    `orderDiscounts` BOOLEAN NULL,
    `shippingDiscounts` BOOLEAN NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyStoreCredit` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `loyaltyProgramId` INTEGER NOT NULL,
    `customerLoyaltyId` INTEGER NOT NULL,
    `balance` DOUBLE NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `LoyaltyStoreCredit_customerLoyaltyId_key`(`customerLoyaltyId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyReferral` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `loyaltyProgramId` INTEGER NOT NULL,
    `referrerCustomerId` INTEGER NOT NULL,
    `referredCustomerId` INTEGER NOT NULL,
    `status` ENUM('PENDING', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'PENDING',
    `pointsAwarded` INTEGER NULL,
    `storeCreditAwarded` DOUBLE NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DefaultWaysEarnRewardType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` ENUM('SIGN_UP', 'REFERRAL', 'CUSTOM_REWARD', 'COMPLETE_PROFILE', 'PURCHASE', 'CELEBRATE_BIRTHDAY') NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `DefaultWaysEarnRewardType_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WaysEarnReward` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `typeEarnReward` ENUM('SIGN_UP', 'REFERRAL', 'CUSTOM_REWARD', 'COMPLETE_PROFILE', 'PURCHASE', 'CELEBRATE_BIRTHDAY') NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `subtitle` VARCHAR(191) NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `shopId` INTEGER NULL,
    `defaultWaysEarnRewardTypeId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `WaysEarnReward_shopId_defaultWaysEarnRewardTypeId_key`(`shopId`, `defaultWaysEarnRewardTypeId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BirthdayProgramSettings` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` INTEGER NOT NULL,
    `pageTitle` VARCHAR(191) NOT NULL DEFAULT 'Celebrate a birthday',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `BirthdayProgramSettings_shopId_key`(`shopId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BirthdayReward` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `settingsId` INTEGER NOT NULL,
    `birthdayRewardType` ENUM('points', 'store-credit', 'amount-off') NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `expiryMonths` INTEGER NULL,
    `rewardValue` DOUBLE NULL,
    `discountType` ENUM('PERCENTAGE', 'FIXED') NULL,
    `minimumRequirement` ENUM('NONE', 'AMOUNT', 'QUANTITY') NULL,
    `minimumValue` DOUBLE NULL,
    `productDiscounts` BOOLEAN NULL,
    `orderDiscounts` BOOLEAN NULL,
    `shippingDiscounts` BOOLEAN NULL,
    `shopifyDiscountId` VARCHAR(191) NULL,
    `discountCode` VARCHAR(191) NULL,
    `tierGroup` VARCHAR(191) NULL,
    `vipTierId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SignupReward` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `waysEarnRewardId` INTEGER NULL,
    `title` VARCHAR(191) NOT NULL,
    `rewardType` ENUM('POINTS', 'STORE_CREDIT', 'AMOUNT_OFF', 'FREE_SHIPPING') NOT NULL,
    `value` VARCHAR(191) NOT NULL,
    `discountType` ENUM('PERCENTAGE', 'FIXED') NULL,
    `minimumRequirement` ENUM('NONE', 'AMOUNT', 'QUANTITY') NULL,
    `minimumValue` DOUBLE NULL,
    `productDiscounts` BOOLEAN NULL,
    `orderDiscounts` BOOLEAN NULL,
    `shippingDiscounts` BOOLEAN NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompleteProfileReward` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(191) NOT NULL,
    `type` ENUM('POINTS', 'STORE_CREDIT', 'AMOUNT_OFF', 'FREE_SHIPPING') NOT NULL,
    `value` VARCHAR(191) NULL,
    `discountType` ENUM('PERCENTAGE', 'FIXED') NULL,
    `minRequirementType` ENUM('NO_MINIMUM', 'MIN_PURCHASE_AMOUNT', 'MIN_QUANTITY_ITEMS') NULL,
    `minRequirementValue` DOUBLE NULL,
    `productDiscounts` BOOLEAN NULL,
    `orderDiscounts` BOOLEAN NULL,
    `shippingDiscounts` BOOLEAN NULL,
    `completeProfileSettingsId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Gender` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `customProfileId` INTEGER NOT NULL,

    UNIQUE INDEX `Gender_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CustomProfile` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `editable` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompleteProfileSettings` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` INTEGER NOT NULL,
    `pageTitle` VARCHAR(191) NOT NULL DEFAULT 'Complete your profile',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `customProfileId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `CompleteProfileSettings_shopId_key`(`shopId`),
    UNIQUE INDEX `CompleteProfileSettings_customProfileId_key`(`customProfileId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PlaceAnOrderReward` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `waysEarnRewardId` INTEGER NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `rewardType` ENUM('POINTS', 'STORE_CREDIT', 'AMOUNT_OFF', 'FREE_SHIPPING') NOT NULL,
    `value` VARCHAR(191) NOT NULL,
    `discountType` ENUM('PERCENTAGE', 'FIXED') NULL,
    `minimumRequirement` ENUM('NONE', 'AMOUNT', 'QUANTITY') NULL,
    `minimumValue` DOUBLE NULL,
    `productDiscounts` BOOLEAN NULL,
    `orderDiscounts` BOOLEAN NULL,
    `shippingDiscounts` BOOLEAN NULL,
    `loyaltyVIPTierId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LoyaltyPointsHistory` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `customerId` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `points` INTEGER NOT NULL,
    `earnedAt` DATETIME(3) NOT NULL,
    `issueAt` DATETIME(3) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `issuedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AclasPosConfig` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` INTEGER NOT NULL,
    `merchantNo` VARCHAR(191) NOT NULL,
    `appId` VARCHAR(191) NOT NULL,
    `appSecret` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `AclasPosConfig_shopId_key`(`shopId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CustomerTimeline` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shopId` INTEGER NOT NULL,
    `customerId` VARCHAR(191) NOT NULL,
    `message` MEDIUMTEXT NOT NULL,
    `type` ENUM('POINTS', 'DISCOUNT', 'ORDER', 'OTHER') NOT NULL DEFAULT 'OTHER',
    `date` DATETIME(3) NOT NULL,
    `metafields` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NULL,

    INDEX `CustomerTimeline_customerId_idx`(`customerId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ShopCreditLoyaltyProgram` ADD CONSTRAINT `ShopCreditLoyaltyProgram_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ReturnOrders` ADD CONSTRAINT `ReturnOrders_salesOrdId_fkey` FOREIGN KEY (`salesOrdId`) REFERENCES `SalesOrders`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyProgram` ADD CONSTRAINT `LoyaltyProgram_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyPoints` ADD CONSTRAINT `LoyaltyPoints_loyaltyProgramId_fkey` FOREIGN KEY (`loyaltyProgramId`) REFERENCES `LoyaltyProgram`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyVIPSettings` ADD CONSTRAINT `LoyaltyVIPSettings_loyaltyProgramId_fkey` FOREIGN KEY (`loyaltyProgramId`) REFERENCES `LoyaltyProgram`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyVIPTier` ADD CONSTRAINT `LoyaltyVIPTier_loyaltyProgramId_fkey` FOREIGN KEY (`loyaltyProgramId`) REFERENCES `LoyaltyProgram`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyVIPReward` ADD CONSTRAINT `LoyaltyVIPReward_vipTierId_fkey` FOREIGN KEY (`vipTierId`) REFERENCES `LoyaltyVIPTier`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyStoreCredit` ADD CONSTRAINT `LoyaltyStoreCredit_loyaltyProgramId_fkey` FOREIGN KEY (`loyaltyProgramId`) REFERENCES `LoyaltyProgram`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LoyaltyReferral` ADD CONSTRAINT `LoyaltyReferral_loyaltyProgramId_fkey` FOREIGN KEY (`loyaltyProgramId`) REFERENCES `LoyaltyProgram`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WaysEarnReward` ADD CONSTRAINT `WaysEarnReward_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WaysEarnReward` ADD CONSTRAINT `WaysEarnReward_defaultWaysEarnRewardTypeId_fkey` FOREIGN KEY (`defaultWaysEarnRewardTypeId`) REFERENCES `DefaultWaysEarnRewardType`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BirthdayProgramSettings` ADD CONSTRAINT `BirthdayProgramSettings_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BirthdayReward` ADD CONSTRAINT `BirthdayReward_settingsId_fkey` FOREIGN KEY (`settingsId`) REFERENCES `BirthdayProgramSettings`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BirthdayReward` ADD CONSTRAINT `BirthdayReward_vipTierId_fkey` FOREIGN KEY (`vipTierId`) REFERENCES `LoyaltyVIPTier`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SignupReward` ADD CONSTRAINT `SignupReward_waysEarnRewardId_fkey` FOREIGN KEY (`waysEarnRewardId`) REFERENCES `WaysEarnReward`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompleteProfileReward` ADD CONSTRAINT `CompleteProfileReward_completeProfileSettingsId_fkey` FOREIGN KEY (`completeProfileSettingsId`) REFERENCES `CompleteProfileSettings`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Gender` ADD CONSTRAINT `Gender_customProfileId_fkey` FOREIGN KEY (`customProfileId`) REFERENCES `CustomProfile`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompleteProfileSettings` ADD CONSTRAINT `CompleteProfileSettings_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompleteProfileSettings` ADD CONSTRAINT `CompleteProfileSettings_customProfileId_fkey` FOREIGN KEY (`customProfileId`) REFERENCES `CustomProfile`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PlaceAnOrderReward` ADD CONSTRAINT `PlaceAnOrderReward_waysEarnRewardId_fkey` FOREIGN KEY (`waysEarnRewardId`) REFERENCES `WaysEarnReward`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PlaceAnOrderReward` ADD CONSTRAINT `PlaceAnOrderReward_loyaltyVIPTierId_fkey` FOREIGN KEY (`loyaltyVIPTierId`) REFERENCES `LoyaltyVIPTier`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AclasPosConfig` ADD CONSTRAINT `AclasPosConfig_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CustomerTimeline` ADD CONSTRAINT `CustomerTimeline_shopId_fkey` FOREIGN KEY (`shopId`) REFERENCES `Shop`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
