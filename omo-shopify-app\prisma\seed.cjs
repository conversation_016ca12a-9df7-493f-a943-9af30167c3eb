// prisma/seed.cjs
const { PrismaClient } = require("@prisma/client");
const db = new PrismaClient();
/* … rest of your upsert logic … */

async function main() {
  console.log("Seeding DefaultWaysEarnRewardType…");
  const defaults = [
    { code: "PURCHASE" },
    { code: "SIGN_UP" },
    { code: "REFERRAL" },
    { code: "COMPLETE_PROFILE" },
    { code: "CUSTOM_REWARD" },
    { code: "CELEBRATE_BIRTHDAY" },
  ];

  for (const d of defaults) {
    await db.defaultWaysEarnRewardType.upsert({
      where: { code: d.code },
      update: {},
      create: d,
    });
  }

  // Upsert gender
  const defaultGenders = [
    { name: "Prefer not to say" },
    { name: "Male" },
    { name: "Female" },
    { name: "Transgender" },
    { name: "Non-binary" },
  ];

  let customProfile = await db.customProfile.findFirst();
  if (!customProfile) {
    customProfile = await db.customProfile.create({
      data: { editable: true },
    });
  }

  for (const g of defaultGenders) {
    await db.gender.upsert({
      where: { name: g.name },
      update: {},
      create: {
        name: g.name,
        customProfileId: customProfile.id,
      },
    });
  }

  console.log("✅  Seed complete");
}

main()
  .catch((e) => {
    console.error(e);
  })
  .finally(() => db.$disconnect());
