server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /positions/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: traefik-access
    static_configs:
      - targets: [localhost]
        labels:
          job: traefik
          app: traefik
          __path__: /var/log/traefik/*.json

    # Optional enrichment for JSON access logs
    pipeline_stages:
      - json:
          expressions:
            ts: StartUTC
            client_ip: ClientAddr
            method: RequestMethod
            path: RequestPath
            status: DownstreamStatus
            duration_ms: Duration
            router: RouterName
            service: ServiceAddr
      - labels:
          method:
          status:
          router:
          service:
      - timestamp:
          source: ts
          format: RFC3339Nano
