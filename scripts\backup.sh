#!/bin/bash

# ==============================================
# OMO System Backup Script
# ==============================================

set -e

# Load environment variables
if [ -f .env.prod ]; then
    export $(cat .env.prod | grep -v '#' | xargs)
fi

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# Create backup directory
mkdir -p $BACKUP_DIR

echo "Starting backup process at $(date)"

# Database Backup
echo "Backing up MySQL database..."
docker exec omo_mysql_prod mysqldump \
    -u root -p${DB_ROOT_PASSWORD} \
    --single-transaction \
    --routines \
    --triggers \
    --all-databases > $BACKUP_DIR/mysql_backup_$DATE.sql

# Compress database backup
gzip $BACKUP_DIR/mysql_backup_$DATE.sql

# MinIO Data Backup
echo "Backing up MinIO data..."
docker exec omo_minio_prod mc mirror /data $BACKUP_DIR/minio_backup_$DATE --overwrite

# Compress MinIO backup
tar -czf $BACKUP_DIR/minio_backup_$DATE.tar.gz -C $BACKUP_DIR minio_backup_$DATE
rm -rf $BACKUP_DIR/minio_backup_$DATE

# Application Logs Backup
echo "Backing up application logs..."
tar -czf $BACKUP_DIR/logs_backup_$DATE.tar.gz ./omo-shopify-app/logs

# Grafana Configuration Backup
echo "Backing up Grafana data..."
docker exec omo_grafana_prod tar -czf - /var/lib/grafana > $BACKUP_DIR/grafana_backup_$DATE.tar.gz

# Traefik Configuration Backup
echo "Backing up Traefik certificates..."
docker run --rm -v traefik_data:/data -v $BACKUP_DIR:/backup alpine tar -czf /backup/traefik_backup_$DATE.tar.gz -C /data .

# Upload to MinIO (if configured)
if [ ! -z "$MINIO_ROOT_USER" ]; then
    echo "Uploading backups to MinIO..."
    docker exec omo_minio_prod mc cp /backups/mysql_backup_$DATE.sql.gz local/backups/
    docker exec omo_minio_prod mc cp /backups/minio_backup_$DATE.tar.gz local/backups/
    docker exec omo_minio_prod mc cp /backups/logs_backup_$DATE.tar.gz local/backups/
    docker exec omo_minio_prod mc cp /backups/grafana_backup_$DATE.tar.gz local/backups/
    docker exec omo_minio_prod mc cp /backups/traefik_backup_$DATE.tar.gz local/backups/
fi

# Cleanup old backups
echo "Cleaning up old backups..."
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

# Cleanup old backups in MinIO
if [ ! -z "$MINIO_ROOT_USER" ]; then
    docker exec omo_minio_prod mc rm --recursive --force --older-than ${RETENTION_DAYS}d local/backups/
fi

echo "Backup completed successfully at $(date)"
echo "Backup files created:"
echo "- mysql_backup_$DATE.sql.gz"
echo "- minio_backup_$DATE.tar.gz"
echo "- logs_backup_$DATE.tar.gz"
echo "- grafana_backup_$DATE.tar.gz"
echo "- traefik_backup_$DATE.tar.gz"
