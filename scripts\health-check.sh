#!/bin/bash

# ==============================================
# OMO System Health Check Script
# ==============================================

set -e

# Load environment variables
if [ -f .env.prod ]; then
    export $(cat .env.prod | grep -v '#' | xargs)
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    
    case $status in
        "OK")
            echo -e "${GREEN}✓${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}✗${NC} $message"
            ;;
        "INFO")
            echo -e "${NC}ℹ${NC} $message"
            ;;
    esac
}

# Function to check service health
check_service_health() {
    local service_name=$1
    local container_name=$2
    
    if docker ps --filter "name=$container_name" --filter "status=running" | grep -q $container_name; then
        # Check if container has health check
        health_status=$(docker inspect $container_name --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-healthcheck")
        
        if [ "$health_status" = "healthy" ]; then
            print_status "OK" "$service_name is running and healthy"
            return 0
        elif [ "$health_status" = "unhealthy" ]; then
            print_status "ERROR" "$service_name is running but unhealthy"
            return 1
        elif [ "$health_status" = "starting" ]; then
            print_status "WARNING" "$service_name is starting up"
            return 1
        else
            print_status "OK" "$service_name is running (no health check configured)"
            return 0
        fi
    else
        print_status "ERROR" "$service_name is not running"
        return 1
    fi
}

# Function to check URL accessibility
check_url() {
    local url=$1
    local service_name=$2
    
    if curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url" | grep -q "200\|301\|302"; then
        print_status "OK" "$service_name is accessible at $url"
        return 0
    else
        print_status "ERROR" "$service_name is not accessible at $url"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    if docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "SELECT 1;" >/dev/null 2>&1; then
        print_status "OK" "MySQL database is accessible"
        
        # Check database size
        db_size=$(docker exec omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD} -e "
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size_MB'
            FROM information_schema.tables 
            WHERE table_schema = '$DB_NAME';" -s -N 2>/dev/null || echo "0")
        
        print_status "INFO" "Database size: ${db_size}MB"
        return 0
    else
        print_status "ERROR" "MySQL database is not accessible"
        return 1
    fi
}

# Function to check Redis connectivity
check_redis() {
    if docker exec omo_redis_prod redis-cli ping | grep -q "PONG"; then
        print_status "OK" "Redis is accessible"
        
        # Check Redis memory usage
        redis_memory=$(docker exec omo_redis_prod redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')
        print_status "INFO" "Redis memory usage: $redis_memory"
        return 0
    else
        print_status "ERROR" "Redis is not accessible"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    local threshold=80
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -lt "$threshold" ]; then
        print_status "OK" "Disk usage: ${usage}%"
        return 0
    else
        print_status "WARNING" "Disk usage is high: ${usage}%"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    local threshold=80
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$usage" -lt "$threshold" ]; then
        print_status "OK" "Memory usage: ${usage}%"
        return 0
    else
        print_status "WARNING" "Memory usage is high: ${usage}%"
        return 1
    fi
}

# Function to check SSL certificates
check_ssl_certificates() {
    local domain=${DOMAIN:-"yourdomain.com"}
    
    # Check certificate expiration
    if command -v openssl >/dev/null 2>&1; then
        local cert_info=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            local expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
            print_status "OK" "SSL certificate is valid until: $expiry_date"
            return 0
        else
            print_status "WARNING" "Could not verify SSL certificate for $domain"
            return 1
        fi
    else
        print_status "INFO" "OpenSSL not available, skipping SSL certificate check"
        return 0
    fi
}

# Main health check function
main() {
    echo "========================================"
    echo "OMO System Health Check"
    echo "========================================"
    echo "Timestamp: $(date)"
    echo ""
    
    local overall_status=0
    
    # Check system resources
    echo "System Resources:"
    check_disk_space || overall_status=1
    check_memory || overall_status=1
    echo ""
    
    # Check core services
    echo "Core Services:"
    check_service_health "Traefik" "traefik" || overall_status=1
    check_service_health "MySQL" "omo_mysql_prod" || overall_status=1
    check_service_health "Redis" "omo_redis_prod" || overall_status=1
    check_service_health "MinIO" "omo_minio_prod" || overall_status=1
    echo ""
    
    # Check application services
    echo "Application Services:"
    check_service_health "Shopify App" "omo_shopify_app_prod" || overall_status=1
    check_service_health "NestJS Backend" "omo_nest_be_prod" || overall_status=1
    check_service_health "Grafana" "omo_grafana_prod" || overall_status=1
    check_service_health "Loki" "omo_loki_prod" || overall_status=1
    check_service_health "BullMQ Dashboard" "omo_bullmq_dashboard_prod" || overall_status=1
    echo ""
    
    # Check database and cache connectivity
    echo "Database & Cache:"
    check_database || overall_status=1
    check_redis || overall_status=1
    echo ""
    
    # Check SSL certificates
    echo "SSL/TLS:"
    check_ssl_certificates || overall_status=1
    echo ""
    
    # Check URL accessibility (if domain is configured)
    if [ "$DOMAIN" != "yourdomain.com" ] && [ ! -z "$DOMAIN" ]; then
        echo "URL Accessibility:"
        check_url "https://app.$DOMAIN" "Shopify App" || overall_status=1
        check_url "https://api.$DOMAIN/api" "NestJS Backend" || overall_status=1
        check_url "https://monitoring.$DOMAIN" "Grafana" || overall_status=1
        check_url "https://traefik.$DOMAIN" "Traefik Dashboard" || overall_status=1
        echo ""
    fi
    
    # Overall status
    echo "========================================"
    if [ $overall_status -eq 0 ]; then
        print_status "OK" "Overall system health: HEALTHY"
        echo "All systems are operational."
    else
        print_status "WARNING" "Overall system health: DEGRADED"
        echo "Some issues detected. Please review the above output."
    fi
    echo "========================================"
    
    exit $overall_status
}

# Run main function
main "$@"
