#!/bin/bash

# ==============================================
# OMO System Restore Script
# ==============================================

set -e

# Load environment variables
if [ -f .env.prod ]; then
    export $(cat .env.prod | grep -v '#' | xargs)
fi

# Configuration
BACKUP_DIR="/backups"

# Function to show usage
usage() {
    echo "Usage: $0 [OPTIONS] BACKUP_DATE"
    echo ""
    echo "Options:"
    echo "  -d, --database    Restore database only"
    echo "  -m, --minio       Restore MinIO data only"
    echo "  -g, --grafana     Restore Grafana data only"
    echo "  -t, --traefik     Restore Traefik certificates only"
    echo "  -a, --all         Restore everything (default)"
    echo "  -h, --help        Show this help message"
    echo ""
    echo "BACKUP_DATE format: YYYYMMDD_HHMMSS"
    echo ""
    echo "Examples:"
    echo "  $0 20240115_143000                    # Restore everything"
    echo "  $0 --database 20240115_143000         # Restore database only"
    echo "  $0 --minio --grafana 20240115_143000  # Restore MinIO and Grafana"
}

# Parse command line arguments
RESTORE_ALL=true
RESTORE_DATABASE=false
RESTORE_MINIO=false
RESTORE_GRAFANA=false
RESTORE_TRAEFIK=false
BACKUP_DATE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--database)
            RESTORE_ALL=false
            RESTORE_DATABASE=true
            shift
            ;;
        -m|--minio)
            RESTORE_ALL=false
            RESTORE_MINIO=true
            shift
            ;;
        -g|--grafana)
            RESTORE_ALL=false
            RESTORE_GRAFANA=true
            shift
            ;;
        -t|--traefik)
            RESTORE_ALL=false
            RESTORE_TRAEFIK=true
            shift
            ;;
        -a|--all)
            RESTORE_ALL=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            if [[ -z "$BACKUP_DATE" ]]; then
                BACKUP_DATE="$1"
            else
                echo "Error: Unknown option $1"
                usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate backup date
if [[ -z "$BACKUP_DATE" ]]; then
    echo "Error: BACKUP_DATE is required"
    usage
    exit 1
fi

# Validate backup date format
if [[ ! "$BACKUP_DATE" =~ ^[0-9]{8}_[0-9]{6}$ ]]; then
    echo "Error: Invalid BACKUP_DATE format. Expected: YYYYMMDD_HHMMSS"
    exit 1
fi

echo "Starting restore process at $(date)"
echo "Backup date: $BACKUP_DATE"

# Check if backup files exist
if [[ "$RESTORE_ALL" == true || "$RESTORE_DATABASE" == true ]]; then
    if [[ ! -f "$BACKUP_DIR/mysql_backup_$BACKUP_DATE.sql.gz" ]]; then
        echo "Error: Database backup file not found: $BACKUP_DIR/mysql_backup_$BACKUP_DATE.sql.gz"
        exit 1
    fi
fi

# Restore Database
if [[ "$RESTORE_ALL" == true || "$RESTORE_DATABASE" == true ]]; then
    echo "Restoring MySQL database..."
    
    # Stop applications that depend on database
    echo "Stopping dependent services..."
    docker stop omo_shopify_app_prod omo_nest_be_prod || true
    
    # Restore database
    gunzip -c $BACKUP_DIR/mysql_backup_$BACKUP_DATE.sql.gz | docker exec -i omo_mysql_prod mysql -u root -p${DB_ROOT_PASSWORD}
    
    echo "Database restored successfully"
fi

# Restore MinIO Data
if [[ "$RESTORE_ALL" == true || "$RESTORE_MINIO" == true ]]; then
    if [[ -f "$BACKUP_DIR/minio_backup_$BACKUP_DATE.tar.gz" ]]; then
        echo "Restoring MinIO data..."
        
        # Stop MinIO
        docker stop omo_minio_prod || true
        
        # Extract and restore MinIO data
        tar -xzf $BACKUP_DIR/minio_backup_$BACKUP_DATE.tar.gz -C /tmp
        docker run --rm -v minio_data_prod:/data -v /tmp/minio_backup_$BACKUP_DATE:/backup alpine sh -c "rm -rf /data/* && cp -r /backup/* /data/"
        rm -rf /tmp/minio_backup_$BACKUP_DATE
        
        # Start MinIO
        docker start omo_minio_prod
        
        echo "MinIO data restored successfully"
    else
        echo "Warning: MinIO backup file not found: $BACKUP_DIR/minio_backup_$BACKUP_DATE.tar.gz"
    fi
fi

# Restore Grafana Data
if [[ "$RESTORE_ALL" == true || "$RESTORE_GRAFANA" == true ]]; then
    if [[ -f "$BACKUP_DIR/grafana_backup_$BACKUP_DATE.tar.gz" ]]; then
        echo "Restoring Grafana data..."
        
        # Stop Grafana
        docker stop omo_grafana_prod || true
        
        # Restore Grafana data
        docker run --rm -v grafana_data_prod:/data alpine sh -c "rm -rf /data/*"
        docker run --rm -v grafana_data_prod:/data -v $BACKUP_DIR:/backup alpine tar -xzf /backup/grafana_backup_$BACKUP_DATE.tar.gz -C /
        
        # Start Grafana
        docker start omo_grafana_prod
        
        echo "Grafana data restored successfully"
    else
        echo "Warning: Grafana backup file not found: $BACKUP_DIR/grafana_backup_$BACKUP_DATE.tar.gz"
    fi
fi

# Restore Traefik Certificates
if [[ "$RESTORE_ALL" == true || "$RESTORE_TRAEFIK" == true ]]; then
    if [[ -f "$BACKUP_DIR/traefik_backup_$BACKUP_DATE.tar.gz" ]]; then
        echo "Restoring Traefik certificates..."
        
        # Stop Traefik
        docker stop traefik || true
        
        # Restore certificates
        docker run --rm -v traefik_data:/data alpine sh -c "rm -rf /data/*"
        docker run --rm -v traefik_data:/data -v $BACKUP_DIR:/backup alpine tar -xzf /backup/traefik_backup_$BACKUP_DATE.tar.gz -C /data
        
        # Start Traefik
        docker start traefik
        
        echo "Traefik certificates restored successfully"
    else
        echo "Warning: Traefik backup file not found: $BACKUP_DIR/traefik_backup_$BACKUP_DATE.tar.gz"
    fi
fi

# Restart all services
echo "Restarting all services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 30

# Check service health
echo "Checking service health..."
docker-compose -f docker-compose.prod.yml ps

echo "Restore completed successfully at $(date)"
